daemon off;
worker_processes auto;
pid /tmp/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
	worker_connections 768;
}

http {
	sendfile on;
	tcp_nopush on;
	types_hash_max_size 2048;

	include /etc/nginx/mime.types;
	default_type application/octet-stream;

	ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3;
	ssl_prefer_server_ciphers on;

	gzip on;

  client_body_temp_path /tmp/client_temp;
  proxy_temp_path       /tmp/proxy_temp_path;
  fastcgi_temp_path     /tmp/fastcgi_temp;
  uwsgi_temp_path       /tmp/uwsgi_temp;
  scgi_temp_path        /tmp/scgi_temp;

  server {
      listen 8080;
      listen [::]:8080;

      proxy_ignore_client_abort on;
      proxy_connect_timeout   10;
      proxy_send_timeout      15;
      proxy_read_timeout      10;

      location / {
        root /tmp/html/;
        include /etc/nginx/mime.types;
        try_files $uri $uri/ /index.html;
      }

      location /healthz {
        access_log off;
        return 200 'OK';
        add_header Content-Type text/plain;
      }
  }
}
