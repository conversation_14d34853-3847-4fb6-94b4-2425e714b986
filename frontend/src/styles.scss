@use '@angular/material' as mat;
@use './app/styles/chart-styles.scss';

@include mat.core();

// define the theme object
$compact-theme: mat.define-theme(
   (
      density: (
        scale: -2,
      )
   )
);

:root {
  @include mat.form-field-density($compact-theme);
}


/* You can add global styles to this file, and also import other style files */
html, body { height: 100%; }
body { margin: 0; font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif; }

/* Rules for sizing the icon. */
.material-icons.md-18 { font-size: 18px; font-style: normal; vertical-align: text-top;}
.material-icons.md-24 { font-size: 24px; font-style: normal; vertical-align: text-top;}
.material-icons.md-36 { font-size: 36px; font-style: normal; vertical-align: text-top;}
.material-icons.md-48 { font-size: 48px; font-style: normal; vertical-align: text-top;}