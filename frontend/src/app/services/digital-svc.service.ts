import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { IDigitalSvc } from '../models/models';
import { environment } from '../environment/environment'

@Injectable({
    providedIn: 'root'
})
export class DigitalSvcService {

    constructor(private http: HttpClient) { }

    // Gets Digital Service List
    getDigitalSvcRecords(payload: any): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/DigitalSvcList");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Gets Digital Service Record
    getDigitalSvcRecord(recordId: string): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/DigitalSvc/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    // Create Digital Service Record
    createDigitalSvcRecord(payload: IDigitalSvc): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/DigitalSvc");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Update a Digital Service Record
    updateDigitalSvcRecord(recordId: string, payload: IDigitalSvc): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/DigitalSvc/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

}
