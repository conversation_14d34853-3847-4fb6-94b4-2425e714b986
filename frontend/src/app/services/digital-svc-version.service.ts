import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { IDigitalSvcVersion } from '../models/models';
import { environment } from '../environment/environment'

@Injectable({
    providedIn: 'root'
})
export class DigitalSvcVersionService {

    constructor(private http: HttpClient) { }

    // Gets DigitalSvcVersion List
    getDigitalSvcVersionRecords(payload: any): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/DigitalSvcVersionList");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Gets DigitalSvcVersion Record
    getDigitalSvcVersionRecord(recordId: string): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/DigitalSvcVersion/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    // Create Digital Service Record
    createDigitalSvcVersionRecord(payload: IDigitalSvcVersion): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/DigitalSvcVersion");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Update a Digital Service Record
    updateDigitalSvcVersionRecord(recordId: string, payload: IDigitalSvcVersion): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/DigitalSvcVersion/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

}
