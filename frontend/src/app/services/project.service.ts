import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { IProject } from '../models/models';
import { environment } from '../environment/environment'

@Injectable({
    providedIn: 'root'
})
export class ProjectService {
  
    constructor(private http: HttpClient) { }

    // Gets Projects List
    getProjectRecords(payload: any): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/ProjectList");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Gets Project Record
    getProjectRecord(recordId: string): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/Project/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    // Create Project Record
    createProjectRecord(payload: IProject): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/Project");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Update a Project Record
    updateProjectRecord(recordId: string, payload: IProject): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/Project/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

}
