import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { IContact } from '../models/models';
import { environment } from '../environment/environment';

@Injectable({
    providedIn: 'root'
})
export class ContactService {

    constructor(private http: HttpClient) { }

    // Gets Contacts List
    getContactRecords(payload: any): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/ContactList");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Gets Contact Record
    getContactRecord(recordId: string): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/Contact/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    // Create Contact Record
    createContactRecord(payload: IContact): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/Contact");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Updates a Contact Record
    updateContactRecord(recordId: string, payload: IContact): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/Contact/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

}
