import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { INote } from '../models/models';
import { environment } from '../environment/environment'

@Injectable({
    providedIn: 'root'
})
export class NoteService {
  
    constructor(private http: HttpClient) { }

    // Gets Notes List
    getNoteRecords(payload: any): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/NoteList");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Gets Note Record
    getNoteRecord(recordId: string): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/Note/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    // Create Note Record
    createNoteRecord(payload: INote): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/Note");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Update a Note Record
    updateNoteRecord(recordId: string, payload: INote): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/Note/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

}
