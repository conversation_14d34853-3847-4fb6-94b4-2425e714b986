import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { 
  IDigitalUsageHistory, 
  IDigitalServiceUsage, 
  IRSPAPIAdoption, 
  IRSPDigitalUsage, 
  IRSPAPIPercentage 
} from '../models/models';
import { environment } from '../environment/environment';

@Injectable({
  providedIn: 'root'
})
export class CioRspExecutiveService {

  constructor(private http: HttpClient) { }

  // Gets Digital Usage History
  getDigitalUsageHistory(): Observable<IDigitalUsageHistory[]> {
    // For now, return mock data
    return of([
      { Period: 'Jan-22', TotalAPITxns: 1200000, TotalPortalTxns: 800000, TotalTxns: 2000000 },
      { Period: 'Feb-22', TotalAPITxns: 1250000, TotalPortalTxns: 780000, TotalTxns: 2030000 },
      { Period: 'Mar-22', TotalAPITxns: 1300000, TotalPortalTxns: 760000, TotalTxns: 2060000 },
      { Period: 'Apr-22', TotalAPITxns: 1350000, TotalPortalTxns: 740000, TotalTxns: 2090000 },
      { Period: 'May-22', TotalAPITxns: 1400000, TotalPortalTxns: 720000, TotalTxns: 2120000 },
      { Period: 'Jun-22', TotalAPITxns: 1450000, TotalPortalTxns: 700000, TotalTxns: 2150000 },
      { Period: 'Jul-22', TotalAPITxns: 1500000, TotalPortalTxns: 680000, TotalTxns: 2180000 },
      { Period: 'Aug-22', TotalAPITxns: 1550000, TotalPortalTxns: 660000, TotalTxns: 2210000 },
      { Period: 'Sep-22', TotalAPITxns: 1600000, TotalPortalTxns: 640000, TotalTxns: 2240000 },
      { Period: 'Oct-22', TotalAPITxns: 1650000, TotalPortalTxns: 620000, TotalTxns: 2270000 },
      { Period: 'Nov-22', TotalAPITxns: 1700000, TotalPortalTxns: 600000, TotalTxns: 2300000 },
      { Period: 'Dec-22', TotalAPITxns: 1750000, TotalPortalTxns: 580000, TotalTxns: 2330000 },
      { Period: 'Jan-23', TotalAPITxns: 1800000, TotalPortalTxns: 560000, TotalTxns: 2360000 },
      { Period: 'Feb-23', TotalAPITxns: 1850000, TotalPortalTxns: 540000, TotalTxns: 2390000 },
      { Period: 'Mar-23', TotalAPITxns: 1900000, TotalPortalTxns: 520000, TotalTxns: 2420000 },
      { Period: 'Apr-23', TotalAPITxns: 1950000, TotalPortalTxns: 500000, TotalTxns: 2450000 },
      { Period: 'May-23', TotalAPITxns: 2000000, TotalPortalTxns: 480000, TotalTxns: 2480000 },
      { Period: 'Jun-23', TotalAPITxns: 2050000, TotalPortalTxns: 460000, TotalTxns: 2510000 },
    ]);
  }

  // Gets Digital Service Usage
  getDigitalServiceUsage(period: string): Observable<IDigitalServiceUsage[]> {
    // For now, return mock data
    return of([
      { ServiceName: 'Service A', TotalAPITxns: 500000, TotalPortalTxns: 100000, APIPercentage: 83.3 },
      { ServiceName: 'Service B', TotalAPITxns: 450000, TotalPortalTxns: 120000, APIPercentage: 78.9 },
      { ServiceName: 'Service C', TotalAPITxns: 400000, TotalPortalTxns: 140000, APIPercentage: 74.1 },
      { ServiceName: 'Service D', TotalAPITxns: 350000, TotalPortalTxns: 160000, APIPercentage: 68.6 },
      { ServiceName: 'Service E', TotalAPITxns: 300000, TotalPortalTxns: 180000, APIPercentage: 62.5 },
      { ServiceName: 'Service F', TotalAPITxns: 250000, TotalPortalTxns: 200000, APIPercentage: 55.6 },
      { ServiceName: 'Service G', TotalAPITxns: 200000, TotalPortalTxns: 220000, APIPercentage: 47.6 },
      { ServiceName: 'Service H', TotalAPITxns: 150000, TotalPortalTxns: 240000, APIPercentage: 38.5 },
      { ServiceName: 'Service I', TotalAPITxns: 100000, TotalPortalTxns: 260000, APIPercentage: 27.8 },
      { ServiceName: 'Service J', TotalAPITxns: 50000, TotalPortalTxns: 280000, APIPercentage: 15.2 },
    ]);
  }

  // Gets RSP API Adoption
  getRSPAPIAdoption(period: string): Observable<IRSPAPIAdoption[]> {
    // For now, return mock data
    return of([
      { APIName: 'API A', CertCount: 50, UtilCount: 45 },
      { APIName: 'API B', CertCount: 45, UtilCount: 40 },
      { APIName: 'API C', CertCount: 40, UtilCount: 35 },
      { APIName: 'API D', CertCount: 35, UtilCount: 30 },
      { APIName: 'API E', CertCount: 30, UtilCount: 25 },
      { APIName: 'API F', CertCount: 25, UtilCount: 20 },
      { APIName: 'API G', CertCount: 20, UtilCount: 15 },
      { APIName: 'API H', CertCount: 15, UtilCount: 10 },
      { APIName: 'API I', CertCount: 10, UtilCount: 5 },
      { APIName: 'API J', CertCount: 5, UtilCount: 2 },
    ]);
  }

  // Gets RSP Digital Usage
  getRSPDigitalUsage(period: string): Observable<IRSPDigitalUsage[]> {
    // For now, return mock data
    return of([
      { DigitalVolRank: 1, RSPName: 'RSP A', APIPercentage: 85.5, TotalServices: 20, TxnPerService: 25000, TotalAPITxns: 425000, TotalPortalTxns: 75000, TotalTxns: 500000 },
      { DigitalVolRank: 2, RSPName: 'RSP B', APIPercentage: 80.0, TotalServices: 18, TxnPerService: 22222, TotalAPITxns: 320000, TotalPortalTxns: 80000, TotalTxns: 400000 },
      { DigitalVolRank: 3, RSPName: 'RSP C', APIPercentage: 75.0, TotalServices: 16, TxnPerService: 20000, TotalAPITxns: 240000, TotalPortalTxns: 80000, TotalTxns: 320000 },
      { DigitalVolRank: 4, RSPName: 'RSP D', APIPercentage: 70.0, TotalServices: 14, TxnPerService: 18571, TotalAPITxns: 182000, TotalPortalTxns: 78000, TotalTxns: 260000 },
      { DigitalVolRank: 5, RSPName: 'RSP E', APIPercentage: 65.0, TotalServices: 12, TxnPerService: 16667, TotalAPITxns: 130000, TotalPortalTxns: 70000, TotalTxns: 200000 },
      { DigitalVolRank: 6, RSPName: 'RSP F', APIPercentage: 60.0, TotalServices: 10, TxnPerService: 15000, TotalAPITxns: 90000, TotalPortalTxns: 60000, TotalTxns: 150000 },
      { DigitalVolRank: 7, RSPName: 'RSP G', APIPercentage: 55.0, TotalServices: 8, TxnPerService: 13750, TotalAPITxns: 60500, TotalPortalTxns: 49500, TotalTxns: 110000 },
      { DigitalVolRank: 8, RSPName: 'RSP H', APIPercentage: 50.0, TotalServices: 6, TxnPerService: 12500, TotalAPITxns: 37500, TotalPortalTxns: 37500, TotalTxns: 75000 },
      { DigitalVolRank: 9, RSPName: 'RSP I', APIPercentage: 45.0, TotalServices: 4, TxnPerService: 11250, TotalAPITxns: 20250, TotalPortalTxns: 24750, TotalTxns: 45000 },
      { DigitalVolRank: 10, RSPName: 'RSP J', APIPercentage: 40.0, TotalServices: 2, TxnPerService: 10000, TotalAPITxns: 8000, TotalPortalTxns: 12000, TotalTxns: 20000 },
    ]);
  }

  // Gets RSP API Percentage
  getRSPAPIPercentage(period: string): Observable<IRSPAPIPercentage[]> {
    // For now, return mock data
    return of([
      { DigitalVolRank: 1, RSPName: 'RSP A', APIPercentage: 85.5, ServiceCountRank: 1 },
      { DigitalVolRank: 2, RSPName: 'RSP B', APIPercentage: 80.0, ServiceCountRank: 2 },
      { DigitalVolRank: 3, RSPName: 'RSP C', APIPercentage: 75.0, ServiceCountRank: 3 },
      { DigitalVolRank: 4, RSPName: 'RSP D', APIPercentage: 70.0, ServiceCountRank: 4 },
      { DigitalVolRank: 5, RSPName: 'RSP E', APIPercentage: 65.0, ServiceCountRank: 5 },
      { DigitalVolRank: 6, RSPName: 'RSP F', APIPercentage: 60.0, ServiceCountRank: 6 },
      { DigitalVolRank: 7, RSPName: 'RSP G', APIPercentage: 55.0, ServiceCountRank: 7 },
      { DigitalVolRank: 8, RSPName: 'RSP H', APIPercentage: 50.0, ServiceCountRank: 8 },
      { DigitalVolRank: 9, RSPName: 'RSP I', APIPercentage: 45.0, ServiceCountRank: 9 },
      { DigitalVolRank: 10, RSPName: 'RSP J', APIPercentage: 40.0, ServiceCountRank: 10 },
    ]);
  }

  // In the future, these methods would call the backend API
  // Example of how it would be implemented:
  /*
  getDigitalUsageHistory(): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/digitalusagehistory");
    return this.http.get(encodedURL) as Observable<any>;
  }
  */
}
