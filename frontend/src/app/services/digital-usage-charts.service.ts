import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../environment/environment'

@Injectable({
    providedIn: 'root'
})
export class DigitalUsageChartService {

    constructor(private http: HttpClient) { }

    // ***********************************************************************
    // Digital Dashboard Services
    // ***********************************************************************

    // Gets Digital Usage Test data
    getDigitalUsageHistory(): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/digitalusagehistory");
        return this.http.get(encodedURL) as Observable<any>;

    }

    getDigitalUsageByRSP(period: string): Observable<any> {

          // Add safe, URL encoded search parameter for thr period
        const options = period ? { params: new HttpParams().set('period', period) } : {};

        const encodedURL = encodeURI(environment.base_url + "/digitalusagebyrsp");
        return this.http.get(encodedURL, options) as Observable<any>;

    }

    getDigitalUsageBySvc(period: string): Observable<any> {

        // Add safe, URL encoded search parameter for thr period
      const options = period ? { params: new HttpParams().set('period', period) } : {};

      const encodedURL = encodeURI(environment.base_url + "/digitalusagebyservice");
      return this.http.get(encodedURL, options) as Observable<any>;

    }

    // ***********************************************************************
    // RSP Digital Dashboard Services
    // ***********************************************************************
    getDigitalUsageChartDataForRSP(accessSeekerId: string): Observable<any> {

        // Add safe, URL encoded search parameter for thr period
      const options = accessSeekerId ? { params: new HttpParams().set('accessSeekerId', accessSeekerId) } : {};

      const encodedURL = encodeURI(environment.base_url + "/digitalusageforrsp");
      return this.http.get(encodedURL, options) as Observable<any>;

    }

    // ***********************************************************************
    // Services Digital Dashboard
    // ***********************************************************************
    getDigitalUsageChartDataForServices(): Observable<any> {

      const encodedURL = encodeURI(environment.base_url + "/digitalusageforservices");
      return this.http.get(encodedURL) as Observable<any>;

    }


}
