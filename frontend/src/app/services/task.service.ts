import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ITask } from '../models/models';
import { environment } from '../environment/environment'

@Injectable({
    providedIn: 'root'
})
export class TaskService {

    constructor(private http: HttpClient) { }

    // Gets Tasks List
    getTaskRecords(payload: any): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/TaskList");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Gets Task Record
    getTaskRecord(recordId: string): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/Task/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    // Create Task Record
    createTaskRecord(payload: ITask): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/Task");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Update a Task Record
    updateTaskRecord(recordId: string, payload: ITask): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/Task/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

}
