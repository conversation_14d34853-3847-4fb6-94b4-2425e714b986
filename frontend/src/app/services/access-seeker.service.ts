import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { IAccessSeeker } from '../models/models';
import { environment } from '../environment/environment';

@Injectable({
    providedIn: 'root'
})
export class AccessSeekerService {

    constructor(private http: HttpClient) { }

    // Gets Business Entity Data
    getAccessSeekerRecords(payload: any): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/AccessSeekerList");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Gets Business Entity Data
    getAccessSeekerRecord(recordId: string): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/AccessSeeker/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    // Create Access Seeker Record
    createAccessSeekerRecord(payload: IAccessSeeker): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/AccessSeeker");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    // Updates an Access Seeker Record
    updateAccessSeekerRecord(recordId: string, payload: IAccessSeeker): Observable<any> {

        const encodedURL = encodeURI(environment.base_url + "/AccessSeeker/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

}
