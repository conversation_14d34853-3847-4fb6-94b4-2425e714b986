.example-card {
  padding: 10px;
}

.example-card-title {
  padding: 10px;
}

.example-card-content {
  padding: 10px;
}

form {
width:90%;
padding: 20px;
margin: 0 auto;
background-color: white;
border-radius: 8px;
display: flex;
flex-direction: column;
gap: 20px
}

.form-row {
width: 100%;
display: flex;
gap: 20px;
align-items: flex-start;
}

.form-row-single-col {
width: calc(50% - 10px);
display: flex;
gap: 20px;
align-items: flex-start;
}

mat-form-field {
width: 100%;
}

@media (max-width: 1200px) {
.form-row {
   flex-direction: column;
}
.form-row-single-col {
  width: 100%;
}
}