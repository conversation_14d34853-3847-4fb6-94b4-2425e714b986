@if (isLoading) { 
<mat-spinner></mat-spinner> 
}

@else {

<mat-card class="example-card">

    <form [formGroup]="noteForm">

        <div class="form-row">

            <mat-form-field>
                <mat-label for="access-seeker-id">Access Seeker Id: </mat-label>
                <input matInput type="text" id="access-seeker-id" type="text" formControlName="AccessSeekerId" readonly="true">

                @if (this.allowAccessSeekerSelection) {
                <button 
                type="button" 
                mat-icon-button 
                matSuffix
                matTooltip="Pick an Acccess Seeker"
                (click)="OpenAccessSeekerListDialog()">
                <mat-icon matSuffix class="material-icons md-18">chevron_right</mat-icon>
                </button>
                }

            </mat-form-field>

            <mat-form-field>
                <mat-label for="title">Title:</mat-label>
                <input matInput id="title" type="text" formControlName="Title">
            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="type">Type:</mat-label>
                <input matInput id="type" type="text" formControlName="Type">
            </mat-form-field>

            <mat-form-field>
                <mat-label for="sequence">Sequence:</mat-label>
                <input matInput id="sequence" type="text" formControlName="Sequence">
                <mat-hint>Use to pin notes. Otherwise leave blank</mat-hint>
            </mat-form-field>

        </div>

        <mat-form-field>
            <mat-label for="description">Description:</mat-label>
            <textarea matInput id="description" formControlName="Description" rows="8"></textarea>
        </mat-form-field>

        <mat-divider style="margin-top: 10px;"></mat-divider>

        <div style="display: flex; justify-content: center;"> 
            <button style="margin-right: 20px" mat-raised-button [disabled]="!Note" type="button"(click)="onDeleteClick()">Delete</button>
            <button style="margin-right: 20px" mat-raised-button [disabled]="noteForm.pristine" type="button" (click)="onCancelClick()">Cancel</button>
            <button mat-raised-button [disabled]="noteForm.pristine" type="submit" (click)="onSaveClick()">Submit</button>
        </div>

    </form>

</mat-card>

}