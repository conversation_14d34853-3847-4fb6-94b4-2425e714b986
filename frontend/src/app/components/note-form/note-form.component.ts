import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NoteService } from '../../services/note.service';
import { IAccessSeeker, INote, IYNEnum } from '../../models/models';

import { AccessSeekerListDialogComponent } from '../access-seeker-list-dialog/access-seeker-list-dialog.component';

import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

import { ReactiveFormsModule, Validators } from '@angular/forms';
import { FormGroup, FormControl } from '@angular/forms';
import { YesNoDialogComponent } from '../yes-no-dialog/yes-no-dialog.component';

@Component({
  selector: 'app-note-form',
  imports: [
    MatCardModule,
    MatDividerModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    ReactiveFormsModule
  ],
  templateUrl: './note-form.component.html',
  styleUrl: './note-form.component.scss'
})
export class NoteFormComponent implements OnInit {

  // Input Note Record Id (Optional)
  @Input() noteSkeleton: INote | null = null 

  // Output when a new Note has been created
  @Output() noteCreated: EventEmitter<INote> = new EventEmitter();

  // Output when a Note has been updated
  @Output() noteUpdated: EventEmitter<INote> = new EventEmitter();

  // Current Note record
  Note: INote | null = null

  // Form Group for Note
  noteForm = new FormGroup({
    AccessSeekerRecordId: new FormControl('', Validators.required),
    AccessSeekerId: new FormControl('', Validators.required),
    Type: new FormControl(''),
    Title: new FormControl('', Validators.required),
    Sequence: new FormControl('', Validators.pattern('^[0-9]*$')),
    Description: new FormControl('', [Validators.required, Validators.maxLength(2000)]),
  });

  allowAccessSeekerSelection: boolean = true

  isLoading: boolean = false

  constructor(
    private snackBar: MatSnackBar, 
    private noteService: NoteService,
    public dialog: MatDialog) { }

  ngOnInit(): void {

    // If a Note skeleton was passed in with an id, then it must be an existing record (so get the record)
    if (this.noteSkeleton && this.noteSkeleton.id != '') {

      if (this.noteSkeleton.id) {
        this.getNoteRecord(this.noteSkeleton.id);
      }
    }

    // Else if a Note skeleton was passed in without an id, then it must be a create
    // set the form based on the skeleton
    else if (this.noteSkeleton && this.noteSkeleton.id == '') {
      this.Note = this.noteSkeleton
      this.resetNoteForm()

      // Prevent the user from selecting an Access Seeker
      // as it has been provided in the skeleton
      this.allowAccessSeekerSelection = false

    }

  }

  onSaveClick() {

    if (this.noteForm.valid) {

      // If Note exists, then update the record
      if (this.Note?.id) {
        this.updateNoteRecord()
      }

      // Else we are creating a new Note
      else {

        // If the Access Seeker selection is not allowed, then mark the fields as dirty
        // This is needed to ensure that these fields are sent to the back-end on a create.
        if (this.allowAccessSeekerSelection == false) {
          this.noteForm.controls.AccessSeekerRecordId.markAsDirty()
          this.noteForm.controls.AccessSeekerId.markAsDirty()
        }

        this.createNoteRecord()
      }

    } 
    
    else {
      this.showMessage("Please fix errors before resubmitting")
    }

  }

  onCancelClick() {

    // Reset the Note form
    this.resetNoteForm()

  }

  onDeleteClick() {

    // Open a dialog to confirm the delete action
    const dialogRef = this.dialog.open(YesNoDialogComponent, {
      width: '600px',
      data: {
        Title: "Confirm Delete",
        Subtitle: "Confirm you want to process with deleting record",
        Message: "Are you sure you want to delete this record?",
        IconName: "delete"
      },
    });

    dialogRef.afterClosed().subscribe(result => {

      // If user confirms action, then proceed to delete record
      if (result == IYNEnum.Y) {

        //            this.deleteNoteRecord()
      }

      else {
        this.showMessage("User cancelled delete")
      }

    });

  }

  // Method to get the Note record from the back-end and set the form
  public getNoteRecord(noteRecordId: string) {

    this.isLoading = true

    this.noteService.getNoteRecord(noteRecordId)
      .subscribe({
        next: (data) => {
          
          // Update the Note with the new values
          this.Note = data.data
          this.resetNoteForm()

          this.isLoading = false

        },
        error: () => {

          // Reset the Note record
          this.Note = null
          this.resetNoteForm()

          this.showMessage("Error getting Note record")

        }
      });


  }

  // Method to clear the current Note Record
  public clearNoteRecord() {

    this.Note = null
    this.resetNoteForm()

  }


  // Method to create the Note Record on the back-end
  private createNoteRecord() {

    // Create the update payload
    let payload:INote = this.getChangedFieldsFromForm()

    // Call the back-end to create the record
    this.noteService.createNoteRecord(payload)
      .subscribe({
        next: (data) => {
          
          // Create has been successful, so set the Note
          this.Note = data.data
          this.resetNoteForm()

          this.showMessage("Create Successful")

          // Tell the world that a new Note has been successfully created
          this.noteCreated.emit(this.Note!)

        },
        error: (error) => {

          // If specific error message returned, then show specific message otherwise show a generic message
          if (error.error && error.error.errormsg) {
            this.showMessage("Error creating Note record. " + error.error.errormsg)
            console.error("Error creating Note record. " + error.error.errormsg)
          }

          else {
            this.showMessage("Error creating record")
          }
        }
      });

  }
  
  // Method to update the Note Record on the back-end
  private updateNoteRecord() {

    var payload: INote
    var recordId: string

    // Get the id of the record to update
    recordId = this.Note?.id || ''

    // Create the update payload
    payload = this.getChangedFieldsFromForm()

    // Call the back-end to update the record
    this.noteService.updateNoteRecord(recordId, payload)
      .subscribe({
        next: (data) => {

          // Update has been successful, so update Note with the new values
          this.Note = data.data
          this.resetNoteForm()

          this.showMessage("Update Successful")

          // Tell the world that a note has been successfully updated
          this.noteUpdated.emit(this.Note!)

        },
        error: (error) => {

          // If specific error message returned, then show specific message otherwise show a generic message
          if (error.error && error.error.errormsg) {
            this.showMessage("Error updating record. " + error.error.errormsg)
            console.error("Error updating record. " + error.error.errormsg)
          }

          else {
            this.showMessage("Error updating record")
          }
        }
      });

  }


  getChangedFieldsFromForm() {

    let tempNote:INote = {}

    if (this.noteForm.controls.AccessSeekerRecordId.dirty) {
      tempNote.AccessSeekerRecordId = this.noteForm.controls.AccessSeekerRecordId.value!
    }
    if (this.noteForm.controls.AccessSeekerId.dirty) {
      tempNote.AccessSeekerId = this.noteForm.controls.AccessSeekerId.value!
    }
    if (this.noteForm.controls.Type.dirty) {
      tempNote.Type = this.noteForm.controls.Type.value!
    }
    if (this.noteForm.controls.Title.dirty) {
      tempNote.Title = this.noteForm.controls.Title.value!
    }
    if (this.noteForm.controls.Sequence.dirty) {
      tempNote.Sequence = this.noteForm.controls.Sequence.value!
    }
    if (this.noteForm.controls.Description.dirty) {
      tempNote.Description = this.noteForm.controls.Description.value!
    }

    return tempNote

  }


  // Method to reset the Note Form to the values of the current Note record
  resetNoteForm() {

    // If note record exists, then set the form values to the record values
    if (this.Note) {

      this.noteForm.reset({
        AccessSeekerRecordId: this.Note?.AccessSeekerRecordId ?? '',
        AccessSeekerId: this.Note?.AccessSeekerId ?? '',
        Type: this.Note?.Type ?? '',
        Title: this.Note?.Title ?? '',
        Sequence: this.Note?.Sequence ?? '',
        Description: this.Note?.Description ?? '',
      });

    }
    // Else reset the form to empty values
    else {

      this.noteForm.reset()

    }

  }

OpenAccessSeekerListDialog() {
  
  // Open the Access Seeker List Dialog
  const dialogRef = this.dialog.open(AccessSeekerListDialogComponent, {
    width: '60%',
    height: 'fit',
    data: {},
  });

  // When the dialog is closed, get the selected Access Seeker
  dialogRef.afterClosed().subscribe((result: IAccessSeeker) => {

    // If a result was returned, then set the Access Seeker
    if (result) {

      // Set the Access Seeker Record Id in the form field
      this.noteForm.controls.AccessSeekerRecordId.setValue(result.id!);
      this.noteForm.controls.AccessSeekerRecordId.markAsDirty();

      // Set the Access Seeker Id in the form field
      this.noteForm.controls.AccessSeekerId.setValue(result.AccessSeekerId!);
      this.noteForm.controls.AccessSeekerId.markAsDirty();

    }

  });
}

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
