import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';

import { CioRspExecutiveDigitalUsageComponent } from '../cio-rsp-executive-digital-usage/cio-rsp-executive-digital-usage.component';
import { CioRspExecutiveServiceUsageComponent } from '../cio-rsp-executive-service-usage/cio-rsp-executive-service-usage.component';
import { CioRspExecutiveRspAdoptionComponent } from '../cio-rsp-executive-rsp-adoption/cio-rsp-executive-rsp-adoption.component';
import { CioRspExecutiveRspUsageComponent } from '../cio-rsp-executive-rsp-usage/cio-rsp-executive-rsp-usage.component';
import { CioRspExecutiveApiPercentageComponent } from '../cio-rsp-executive-api-percentage/cio-rsp-executive-api-percentage.component';
import { CioRspExecutivePortalUsersComponent } from '../cio-rsp-executive-portal-users/cio-rsp-executive-portal-users.component';
import { CioRspExecutiveWebsiteUsersComponent } from '../cio-rsp-executive-website-users/cio-rsp-executive-website-users.component';

import { CioRspExecutiveService } from '../../services/cio-rsp-executive.service';

@Component({
  selector: 'app-cio-rsp-executive',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatTabsModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCheckboxModule,
    CioRspExecutiveDigitalUsageComponent,
    CioRspExecutiveServiceUsageComponent,
    CioRspExecutiveRspAdoptionComponent,
    CioRspExecutiveRspUsageComponent,
    CioRspExecutiveApiPercentageComponent,
    CioRspExecutivePortalUsersComponent,
    CioRspExecutiveWebsiteUsersComponent
  ],
  templateUrl: './cio-rsp-executive.component.html',
  styleUrl: './cio-rsp-executive.component.scss'
})
export class CioRspExecutiveComponent implements OnInit {
  // Selected period for reports
  selectedPeriod: string = 'Jun-23';

  // Available periods
  periods: string[] = [
    'Jan-22', 'Feb-22', 'Mar-22', 'Apr-22', 'May-22', 'Jun-22',
    'Jul-22', 'Aug-22', 'Sep-22', 'Oct-22', 'Nov-22', 'Dec-22',
    'Jan-23', 'Feb-23', 'Mar-23', 'Apr-23', 'May-23', 'Jun-23'
  ];

  constructor(
    private cioRspExecutiveService: CioRspExecutiveService,
    private snackBar: MatSnackBar,
    public dialog: MatDialog
  ) { }

  ngOnInit(): void {
    // Initialize with the latest period
    this.selectedPeriod = this.periods[this.periods.length - 1];
  }

  // Method to handle period change
  onPeriodChange(period: string): void {
    this.selectedPeriod = period;
    this.showMessage(`Period changed to ${period}`);
  }

  // Method to show a message in the material snackbar
  showMessage(message: string): void {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }
}
