<mat-card class="main-card">
  <mat-card-header>
    <mat-card-title>CIO RSP Executive Monthly</mat-card-title>
    <mat-card-subtitle>Digital Usage Trends and Analytics</mat-card-subtitle>
  </mat-card-header>

  <mat-card-content>
    <!-- Period selection -->
    <div class="period-selection">
      <mat-form-field appearance="fill">
        <mat-label>Select Period</mat-label>
        <mat-select [(value)]="selectedPeriod" (selectionChange)="onPeriodChange($event.value)">
          @for (period of periods; track period) {
            <mat-option [value]="period">{{ period }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
    </div>

    <!-- Digital Usage History -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>Digital Usage History</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <app-cio-rsp-executive-digital-usage></app-cio-rsp-executive-digital-usage>
      </mat-card-content>
    </mat-card>

    <!-- Digital Usage By Service -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>Digital Usage By Service</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <app-cio-rsp-executive-service-usage [period]="selectedPeriod"></app-cio-rsp-executive-service-usage>
      </mat-card-content>
    </mat-card>

    <!-- RSP API Adoption & Utilisation -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>RSP API Adoption & Utilisation</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <app-cio-rsp-executive-rsp-adoption [period]="selectedPeriod"></app-cio-rsp-executive-rsp-adoption>
      </mat-card-content>
    </mat-card>

    <!-- Digital Usage By RSP -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>Digital Usage By RSP</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <app-cio-rsp-executive-rsp-usage [period]="selectedPeriod"></app-cio-rsp-executive-rsp-usage>
      </mat-card-content>
    </mat-card>

    <!-- Digital Usage API% By RSP -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>Digital Usage API% By RSP</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <app-cio-rsp-executive-api-percentage [period]="selectedPeriod"></app-cio-rsp-executive-api-percentage>
      </mat-card-content>
    </mat-card>

    <!-- Service Portal Users -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>Service Portal Users</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <app-cio-rsp-executive-portal-users></app-cio-rsp-executive-portal-users>
      </mat-card-content>
    </mat-card>

    <!-- Public Website Users -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>Public Website Users</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <app-cio-rsp-executive-website-users></app-cio-rsp-executive-website-users>
      </mat-card-content>
    </mat-card>
  </mat-card-content>
</mat-card>
