import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AccessSeekerService } from '../../services/access-seeker.service';

import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule, MatSlideToggleChange } from '@angular/material/slide-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';

import { ModuleRegistry, ColDef, GridOptions, IServerSideDatasource, SideBarDef, GridApi, SelectionChangedEvent, RowSelectionOptions } from 'ag-grid-community';
import { AgGridAngular } from "ag-grid-angular";
import { AllEnterpriseModule } from 'ag-grid-enterprise';

// Register all Community and Enterprise features
ModuleRegistry.registerModules([AllEnterpriseModule]);

@Component({
  selector: 'app-access-seeker-list',
  imports: [
    MatIconModule,
    MatSlideToggleModule,
    MatButtonModule,
    AgGridAngular,
  ],
  templateUrl: './access-seeker-list.component.html',
  styleUrl: './access-seeker-list.component.css'
})
export class AccessSeekerListComponent implements OnInit {

  @Input() showCreateButton: boolean = false;

  // Output when an Access Seeker selection has changed in the grid
  @Output() selectionChanged: EventEmitter<SelectionChangedEvent> = new EventEmitter();

  // Output when the Create Record button is clicked
  @Output() createRecordClicked: EventEmitter<void> = new EventEmitter<void>();

  // Variables for grid
  gridAPI!: GridApi;
  gridOptions: GridOptions = {}

  // Selected Tab Index
  selectedTabIndex: number = 0

  constructor(
    private snackBar: MatSnackBar, 
    private accessSeekerService: AccessSeekerService,
    public dialog: MatDialog) { }

  ngOnInit(): void {

    // Initialise the Grid
    this.initialiseGrid();

  }

  // Define the server side data source to get grid data
  datasource: IServerSideDatasource = {

    // called by the grid when more rows are required
    getRows: params => {

      var payload = {
        startRow: params.request.startRow,
        endRow: params.request.endRow,
        filterModel: params.request.filterModel,
        sortModel: params.request.sortModel
      };

      this.accessSeekerService.getAccessSeekerRecords(payload)
        .subscribe({
          next: (data: any) => {

            // Invoke the grid callback for successfully getting rows
            params.success({ rowData: data["records"], rowCount: data["totalRecords"] })

          },
          error: (error: any) => {

            // If an error was returned, then show the error. Possible clean-up the variable naming conventions (to many errors :-))
            if (error.error && error.error.errormsg) {
              this.showMessage("Error Retriving AccessSeeker records. " + error.error.errormsg)
              console.error("Error Retriving AccessSeeker records. " + error.error.errormsg)
            }

            // Inform the grid callback that getting rows failed
            params.fail()
          }
        });
    }
  };

  // Method to initialise the grid
  private initialiseGrid() {

    // Setup the default column definition
    var defaultColDef: ColDef = {
      resizable: true,
    }

    // Setup the column definition
    var columnDefs: ColDef[] = [
      { field: 'id', hide: true },
      { field: 'AccessSeekerId', filter: 'agTextColumnFilter' },
      { field: 'Name', filter: 'agTextColumnFilter' },
      { field: 'Alias', filter: 'agTextColumnFilter' },
      { field: 'Status', filter: 'agTextColumnFilter' },
      { field: 'Type', filter: 'agTextColumnFilter' },
      { field: 'ParentAccessSeekerId', hide:true, filter: 'agTextColumnFilter' },
      { field: 'ParentAccessSeekerName', filter: 'agTextColumnFilter' },
      { field: 'Category1', filter: 'agTextColumnFilter' },
      { field: 'Category2', filter: 'agTextColumnFilter' },
      { field: 'SDM', filter: 'agTextColumnFilter' },
      { field: 'AGM', filter: 'agTextColumnFilter' },
      { field: 'CDM', filter: 'agTextColumnFilter' },
      { field: 'created', hide: true },
      { field: 'created_by', hide: true },
      { field: 'modified', hide: true },
      { field: 'modified_by', hide: true },
    ]

    // Setup the side-bar options
    var sideBar: SideBarDef = {
      toolPanels: [
        {
          id: 'columns',
          labelDefault: 'Columns',
          labelKey: 'columns',
          iconKey: 'columns',
          toolPanel: 'agColumnsToolPanel',
          toolPanelParams: {
            suppressRowGroups: true,
            suppressValues: true,
            suppressPivots: true,
            suppressPivotMode: true,
            suppressSideButtons: true,
            suppressColumnFilter: false,
            suppressColumnSelectAll: false,
            suppressColumnExpandAll: false,
          },
        },
      ],
      defaultToolPanel: '',
    };

    // Set Grid Options
    this.gridOptions = {
      serverSideDatasource: this.datasource,
      rowModelType: "serverSide",
      defaultColDef: defaultColDef,
      columnDefs: columnDefs,
      headerHeight: 40,
      rowHeight: 30,
      pagination: true,
      paginationPageSize: 20,
      cacheBlockSize: 20,
      sideBar: sideBar,
      rowSelection: <RowSelectionOptions>{
        mode: "singleRow",
        checkboxes: false,
        enableClickSelection: true,
      },
      getRowId: params => {
        return String(params.data.id);
      },
    };
  }

  // AG-Grid event when the Grid is ready
  onGridReady(params: any) {

      this.gridAPI = params.api 
  }

  // Event triggered when the Autosize columns toggle slide has changed
  onToggleAutoSizeColumns(event: MatSlideToggleChange) {

    var autoSizeGrid = event.checked

    // Resize the grid columns
    if (autoSizeGrid == true) {
      this.gridAPI.autoSizeAllColumns()
    }
    else {
      this.gridAPI.sizeColumnsToFit()
    }

  }

  // Method to refresh Grid Records
  RefreshGridRecords() {

    // Refresh the grids records.
    this.gridAPI.refreshServerSide({ purge: true })

  }

  // AG-Grid event triggered when a row selection has changed
  onSelectionChanged(event: SelectionChangedEvent) {

    // Tell the world the selection has changed
    this.selectionChanged.emit(event)

  }

  onCreateRecordClick() {

    // Tell the world the create record button was clicked
    this.createRecordClicked.emit()

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
