import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { IYesNoDialogData, IYNEnum } from '../../models/models';

@Component({
  selector: 'app-yes-no-dialog',
  imports: [
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatButtonModule
  ],
  templateUrl: './yes-no-dialog.component.html',
  styleUrls: ['./yes-no-dialog.component.scss']
})
export class YesNoDialogComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<YesNoDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: IYesNoDialogData,
  ) { }

  ngOnInit(): void {
  }

  onYesClick(): void {
    this.dialogRef.close(IYNEnum.Y);
  }

  onNoClick(): void {
    this.dialogRef.close(IYNEnum.N);
  }

}
