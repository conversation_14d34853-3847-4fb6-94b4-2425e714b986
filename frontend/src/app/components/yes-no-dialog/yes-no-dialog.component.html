<mat-card class="example-card-title">

    <mat-card-title class="example-card-title">
        <mat-icon color="primary">{{data.IconName}}</mat-icon>
        {{data.Title}}</mat-card-title>
    <mat-card-subtitle style="padding-left: 20px;">{{data.Subtitle}}</mat-card-subtitle>

    <mat-divider></mat-divider>

    <mat-card-content class="example-card-content"> 
        <br>
        {{data.Message}}
        <br>
        <br>
    
        <div mat-dialog-actions align="center">
            <button mat-button color="accent" (click)="onNoClick()">No</button>
            <button mat-button color="primary" (click)="onYesClick()">Yes</button>
        </div>

    </mat-card-content>
    
</mat-card>