import { Component } from '@angular/core';
import { AccessSeekerService } from '../../services/access-seeker.service';
import { AccessSeekerListDialogComponent } from '../access-seeker-list-dialog/access-seeker-list-dialog.component';
import { IAccessSeeker, IYNEnum } from '../../models/models';

import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';

import { ReactiveFormsModule, Validators } from '@angular/forms';
import { FormGroup, FormControl } from '@angular/forms';
import { YesNoDialogComponent } from '../yes-no-dialog/yes-no-dialog.component';


@Component({
  selector: 'app-access-seeker-form',
  imports: [
    MatCardModule,
    MatDividerModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    ReactiveFormsModule
  ],
  templateUrl: './access-seeker-form.component.html',
  styleUrl: './access-seeker-form.component.scss'
})
export class AccessSeekerFormComponent {

  // Current Access Seeker record
  AccessSeeker: IAccessSeeker | null = null

  // Form Group for Access Seekrer
  accessSeekerForm = new FormGroup({
    AccessSeekerId: new FormControl(''),
    Name: new FormControl(''),
    Alias: new FormControl(''),
    Status: new FormControl(''),
    Type: new FormControl(''),
    ParentAccessSeekerRecordId: new FormControl(''),
    ParentAccessSeekerId: new FormControl(''),
    Category1: new FormControl(''),
    Category2: new FormControl(''),
    Website: new FormControl(''),
    About: new FormControl(''),
    Comments: new FormControl(''),
    SDM: new FormControl(''),
    AGM: new FormControl(''),
    CDM: new FormControl(''),
  });

  constructor(
    private snackBar: MatSnackBar, 
    private accessSeekerService: AccessSeekerService,
    public dialog: MatDialog) { }

    onSaveClick() {

      if (this.accessSeekerForm.valid) {
  
        // If Access Seeker exists, then update the record
        if (this.AccessSeeker) {
          this.updateAccessSeekerRecord()
        }
  
        // Else we are creating a new Access Seeker
        else {
          this.createAccessSeekerRecord()
        }
  
      } 
      
      else {
        this.showMessage("Please fix errors before resubmitting")
      }
  
    }
  
    onCancelClick() {
  
      // Reset the Access Seeker form
      this.resetAccessSeekerForm()
  
    }
  
    onDeleteClick() {
  
      // Open a dialog to confirm the delete action
      const dialogRef = this.dialog.open(YesNoDialogComponent, {
        width: '600px',
        data: {
          Title: "Confirm Delete",
          Subtitle: "Confirm you want to process with deleting record",
          Message: "Are you sure you want to delete this record?",
          IconName: "delete"
        },
      });
  
      dialogRef.afterClosed().subscribe(result => {
  
        // If user confirms action, then proceed to delete record
        if (result == IYNEnum.Y) {
  
          //            this.deleteAccessSeekeryRecord()
        }
  
        else {
          this.showMessage("User cancelled delete")
        }
  
      });
  
    }
  
    // Method to get the Access Seeker record from the back-end and set the form
    public getAccessSeekerRecord(accessSeekerRecordId: string) {

      this.accessSeekerService.getAccessSeekerRecord(accessSeekerRecordId)
        .subscribe({
          next: (data) => {
            
            // Update the Access Seeker with the new values
            this.AccessSeeker = data.data
            this.resetAccessSeekerForm()

          },
          error: () => {

            // Reset the Access Seeker record
            this.AccessSeeker = null
            this.resetAccessSeekerForm()

            this.showMessage("Error getting Access Seeker record")

          }
        });


    }

    // Method to get the clear the current Access Seeker Record
    public clearAccessSeekerRecord() {

      this.AccessSeeker = null
      this.resetAccessSeekerForm()

    }


    // Method to create the Business Entity Record on the back-end
    private createAccessSeekerRecord() {
  
      // Create the update payload
      let payload:IAccessSeeker = this.getChangedFieldsFromForm()
  
      // Call the back-end to create the record
      this.accessSeekerService.createAccessSeekerRecord(payload)
        .subscribe({
          next: (data) => {
  
            // Create has been successful, so set the Access Seeker
            this.AccessSeeker = data.data
            this.resetAccessSeekerForm()
  
            this.showMessage("Create Successful")
  
          },
          error: (error) => {
  
            // If specific error message returned, then show specific message otherwise show a generic message
            if (error.error && error.error.errormsg) {
              this.showMessage("Error creating Access Seeker record. " + error.error.errormsg)
              console.error("Error creating Access Seeker record. " + error.error.errormsg)
            }
  
            else {
              this.showMessage("Error creating record")
            }
          }
        });
  
    }
    
    // Method to update the Contact Record on the back-end
    private updateAccessSeekerRecord() {
  
      var payload: IAccessSeeker
      var recordId: string
  
      // Get the id of the record to update
      recordId = this.AccessSeeker?.id || ''
  
      // Create the update payload
      payload = this.getChangedFieldsFromForm()
  
      // Call the back-end to update the record
      this.accessSeekerService.updateAccessSeekerRecord(recordId, payload)
        .subscribe({
          next: (data) => {
  
            // Update has been successful, so update Access Seeker with the new values
            this.AccessSeeker = data.data
            this.resetAccessSeekerForm()
  
            this.showMessage("Update Successful")
  
          },
          error: (error) => {
  
            // If specific error message returned, then show specific message otherwise show a generic message
            if (error.error && error.error.errormsg) {
              this.showMessage("Error updating Access Seeker record. " + error.error.errormsg)
              console.error("Error updating Access Seeker record. " + error.error.errormsg)
            }
  
            else {
              this.showMessage("Error updating record")
            }
          }
        });
  
    }
  
  
    getChangedFieldsFromForm() {
  
      let tempAccessSeeker:IAccessSeeker = {}
  
      if (this.accessSeekerForm.controls.AccessSeekerId.dirty) {
        tempAccessSeeker.AccessSeekerId = this.accessSeekerForm.controls.AccessSeekerId.value!
      }
      if (this.accessSeekerForm.controls.Name.dirty) {
        tempAccessSeeker.Name = this.accessSeekerForm.controls.Name.value!
      }
      if (this.accessSeekerForm.controls.Alias.dirty) {
        tempAccessSeeker.Alias = this.accessSeekerForm.controls.Alias.value!
      }
      if (this.accessSeekerForm.controls.Status.dirty) {
        tempAccessSeeker.Status = this.accessSeekerForm.controls.Status.value!
      }
      if (this.accessSeekerForm.controls.Type.dirty) {
        tempAccessSeeker.Type = this.accessSeekerForm.controls.Type.value!
      }
      if (this.accessSeekerForm.controls.ParentAccessSeekerRecordId.dirty) {
        tempAccessSeeker.ParentAccessSeekerRecordId = this.accessSeekerForm.controls.ParentAccessSeekerRecordId.value!
      }
      if (this.accessSeekerForm.controls.ParentAccessSeekerId.dirty) {
        tempAccessSeeker.ParentAccessSeekerId = this.accessSeekerForm.controls.ParentAccessSeekerId.value!
      }
      if (this.accessSeekerForm.controls.Category1.dirty) {
        tempAccessSeeker.Category1 = this.accessSeekerForm.controls.Category1.value!
      }
      if (this.accessSeekerForm.controls.Category2.dirty) {
        tempAccessSeeker.Category2 = this.accessSeekerForm.controls.Category2.value!
      }
      if (this.accessSeekerForm.controls.Website.dirty) {
        tempAccessSeeker.Website = this.accessSeekerForm.controls.Website.value!
      }
      if (this.accessSeekerForm.controls.About.dirty) {
        tempAccessSeeker.About = this.accessSeekerForm.controls.About.value!
      }
      if (this.accessSeekerForm.controls.Comments.dirty) {
        tempAccessSeeker.Comments = this.accessSeekerForm.controls.Comments.value!
      }
      if (this.accessSeekerForm.controls.SDM.dirty) {
        tempAccessSeeker.SDM = this.accessSeekerForm.controls.SDM.value!
      }
      if (this.accessSeekerForm.controls.AGM.dirty) {
        tempAccessSeeker.AGM = this.accessSeekerForm.controls.AGM.value!
      }
      if (this.accessSeekerForm.controls.CDM.dirty) {
        tempAccessSeeker.CDM = this.accessSeekerForm.controls.CDM.value!
      }
  
      return tempAccessSeeker
  
    }
  
  
    // Method to reset the Access Seeker Form to the values of the current Access Seeker record
    resetAccessSeekerForm() {
  
      // If access seeker record exists, then set the form values to the record values
      if (this.AccessSeeker) {
  
        this.accessSeekerForm.reset({
          AccessSeekerId: this.AccessSeeker?.AccessSeekerId ?? '',
          Name: this.AccessSeeker?.Name ?? '',
          Alias: this.AccessSeeker?.Alias ?? '',
          Status: this.AccessSeeker?.Status ?? '',
          Type: this.AccessSeeker?.Type ?? '',
          ParentAccessSeekerRecordId: this.AccessSeeker?.ParentAccessSeekerRecordId ?? '',
          ParentAccessSeekerId: this.AccessSeeker?.ParentAccessSeekerId ?? '',
          Category1: this.AccessSeeker?.Category1 ?? '',
          Category2: this.AccessSeeker?.Category2 ?? '',
          Website: this.AccessSeeker?.Website ?? '',
          About: this.AccessSeeker?.About ?? '',
          Comments: this.AccessSeeker?.Comments ?? '',
          SDM: this.AccessSeeker?.SDM ?? '',
          AGM: this.AccessSeeker?.AGM ?? '',
          CDM: this.AccessSeeker?.CDM ?? '',
        });
  
      }
      // Else reset the form to empty values
      else {
  
        this.accessSeekerForm.reset()
  
      }
  
    }
  
    OpenAccessSeekerListDialog() {
      
      // Open the Access Seeker List Dialog
      const dialogRef = this.dialog.open(AccessSeekerListDialogComponent, {
        width: '60%',
        height: 'fit',
        data: {},
      });

      // When the dialog is closed, get the selected Access Seeker
      dialogRef.afterClosed().subscribe((result: IAccessSeeker) => {

        // If a result was returned, then set the Access Seeker
        if (result) {

          // Set the Parent Access Seeker Record Id in the form field
          this.accessSeekerForm.controls.ParentAccessSeekerRecordId.setValue(result.id!);
          this.accessSeekerForm.controls.ParentAccessSeekerRecordId.markAsDirty();

          // Set the Parent Access Seeker Id in the form field
          this.accessSeekerForm.controls.ParentAccessSeekerId.setValue(result.AccessSeekerId!);
          this.accessSeekerForm.controls.ParentAccessSeekerId.markAsDirty();

        }

      });
    }



    // Method to show a message in the material snackbar
    showMessage(message: string) {
      this.snackBar.open(message, 'OK', {
        duration: 3000,
      });
    }


}
