<mat-card class="example-card">

    <!-- Show the form header -->
    <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 16px; ">

        <mat-label style="font-family:Roboto;">Access Seeker: {{AccessSeeker?.AccessSeekerId}}</mat-label>

        <div style="display: flex; justify-content: flex-end; padding-right: 25px;"> 
            <button style="margin-right: 10px" mat-raised-button [disabled]="!AccessSeeker" type="button"(click)="onDeleteClick()">Delete</button>
            <button style="margin-right: 10px" mat-raised-button [disabled]="accessSeekerForm.pristine" type="button" (click)="onCancelClick()">Cancel</button>
            <button mat-raised-button [disabled]="accessSeekerForm.pristine" type="submit" (click)="onSaveClick()">Submit</button>
        </div>

    </div>

    <mat-divider></mat-divider>
    
    <form [formGroup]="accessSeekerForm">

        <div class="form-row">

            <mat-form-field>
                <mat-label for="access-seeker-id">Access Seeker Id: </mat-label>
                <input matInput type="text" id="access-seeker-id" type="text" formControlName="AccessSeekerId">
            </mat-form-field>


            <mat-form-field>
                <mat-label for="name">Name:</mat-label>
                <input matInput id="name" type="text" formControlName="Name">
            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="alias">Alias:</mat-label>
                <input matInput id="alias" type="text" formControlName="Alias">
            </mat-form-field>

            <mat-form-field>
                <mat-label for="status">Status:</mat-label>
                <mat-select id="status" formControlName="Status">
                    <mat-option value="Active">Active</mat-option>
                    <mat-option value="Inactive">Inactive</mat-option>
                  </mat-select>
            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="type">Type:</mat-label>
                <mat-select id="type" formControlName="Type">
                    <mat-option value="Direct Access Seeker">Direct Access Seeker</mat-option>
                    <mat-option value="Access Seeker Division">Access Seeker Division</mat-option>
                    <mat-option value="Downstreamer">Downstreamer</mat-option>
                  </mat-select>
            </mat-form-field>

            <mat-form-field>
                <mat-label for="parent-access-seeker-id">Parent Access Seeker Id: </mat-label>
                <input matInput type="text" id="parent-access-seeker-id" type="text" formControlName="ParentAccessSeekerId" readonly="true">

                <button 
                type="button" 
                mat-icon-button 
                matSuffix
                matTooltip="Pick a linked record"
                (click)="OpenAccessSeekerListDialog()">
                <mat-icon matSuffix class="material-icons md-18">chevron_right</mat-icon>
                </button>

            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="category1">Category1:</mat-label>
                <input matInput id="category1" type="text" formControlName="Category1">
            </mat-form-field>


            <mat-form-field>
                <mat-label for="category2">Category2:</mat-label>
                <input matInput id="category2" type="text" formControlName="Category2">
            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="website">Website:</mat-label>
                <input matInput id="website" type="text" formControlName="Website">
            </mat-form-field>

        </div>

        <mat-form-field>
            <mat-label for="about">About:</mat-label>
            <textarea matInput id="about" formControlName="About" rows="3"></textarea>
        </mat-form-field>


        <mat-form-field>
            <mat-label for="comments">Comments:</mat-label>
            <textarea matInput id="comments" formControlName="Comments" rows="3"></textarea>
        </mat-form-field>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="sdm">SDM:</mat-label>
                <input matInput id="sdm" type="text" formControlName="SDM">
            </mat-form-field>


            <mat-form-field>
                <mat-label for="agm">AGM:</mat-label>
                <input matInput id="agm" type="text" formControlName="AGM">
            </mat-form-field>

            <mat-form-field>
                <mat-label for="cdm">CDM:</mat-label>
                <input matInput id="cdm" type="text" formControlName="CDM">
            </mat-form-field>

        </div>

    </form>

</mat-card>