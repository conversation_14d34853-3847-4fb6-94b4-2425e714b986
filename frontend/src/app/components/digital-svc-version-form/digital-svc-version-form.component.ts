import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { DigitalSvcVersionService } from '../../services/digital-svc-version.service';
import { IDigitalSvc, IDigitalSvcVersion, IDigitalSvcVersionSkeleton, IYNEnum } from '../../models/models';

import { DigitalSvcListDialogComponent } from '../digital-svc-list-dialog/digital-svc-list-dialog.component';

import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { ReactiveFormsModule, Validators } from '@angular/forms';
import { FormGroup, FormControl } from '@angular/forms';
import { YesNoDialogComponent } from '../yes-no-dialog/yes-no-dialog.component';


@Component({
  selector: 'app-digital-svc-version-form',
  imports: [
    MatCardModule,
    MatDividerModule,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatInputModule,
    MatProgressSpinnerModule,
    ReactiveFormsModule
  ],
  templateUrl: './digital-svc-version-form.component.html',
  styleUrl: './digital-svc-version-form.component.scss'
})
export class DigitalSvcVersionFormComponent implements OnInit {

  // Input DigitalSvcVersion Skeleton (Optional)
  @Input() digitalSvcVersionSkeleton: IDigitalSvcVersionSkeleton | null = null 

  // Output when a new DigitalSvcVersion has been created
  @Output() digitalSvcVersionCreated: EventEmitter<IDigitalSvcVersion> = new EventEmitter();

  // Output when a DigitalSvcVersion has been updated
  @Output() digitalSvcVersionUpdated: EventEmitter<IDigitalSvcVersion> = new EventEmitter();

  // Current DigitalSvcVersion record
  DigitalSvcVersion: IDigitalSvcVersion | null = null

  // Form Group for DigitalSvcVersion
  digitalSvcVersionForm = new FormGroup({
    DigitalServiceRecordId: new FormControl('', Validators.required),
    ServiceName: new FormControl('', Validators.required),
    APIName: new FormControl('', Validators.required),
    Version: new FormControl('', Validators.required),
    Status: new FormControl('', Validators.required),
    ReleaseDate: new FormControl<Date | null>(null),
    Description: new FormControl(''),
  });

  allowDigitalSvcSelection: boolean = true

  isLoading: boolean = false

  constructor(
    private snackBar: MatSnackBar, 
    private digitalSvcVersionService: DigitalSvcVersionService,
    public dialog: MatDialog) { }

  ngOnInit(): void {

    // If a DigitalSvcVersion skeleton was passed in with an id, then it must be an existing record (so get the record)
    if (this.digitalSvcVersionSkeleton && this.digitalSvcVersionSkeleton.id != '') {

      if (this.digitalSvcVersionSkeleton.id) {
        this.getDigitalSvcVersionRecord(this.digitalSvcVersionSkeleton.id);
      }
    }

    // Else if a DigitalSvcVersion skeleton was passed in without an id, then it must be a create
    // set the form based on the skeleton
    else if (this.digitalSvcVersionSkeleton && this.digitalSvcVersionSkeleton.id == '') {
      this.DigitalSvcVersion = this.digitalSvcVersionSkeleton
      this.resetDigitalSvcVersionForm()

      // Prevent the user from selecting an Access Seeker
      // as it has been provided in the skeleton
      this.allowDigitalSvcSelection = false

    }

  }

  onSaveClick() {

    if (this.digitalSvcVersionForm.valid) {

      // If DigitalSvcVersion exists, then update the record
      if (this.DigitalSvcVersion?.id) {
        this.updateDigitalSvcVersionRecord()
      }

      // Else we are creating a new DigitalSvcVersion
      else {

        // If the DigitalSvcVersion selection is not allowed, then mark the fields as dirty
        // This is needed to ensure that these fields are sent to the back-end on a create.
        if (this.allowDigitalSvcSelection == false) {
          this.digitalSvcVersionForm.controls.DigitalServiceRecordId.markAsDirty()
        }

        this.createDigitalSvcVersionRecord()
      }

    } 
    
    else {
      this.showMessage("Please fix errors before resubmitting")
    }

  }

  onCancelClick() {

    // Reset the DigitalSvcVersion form
    this.resetDigitalSvcVersionForm()

  }

  onDeleteClick() {

    // Open a dialog to confirm the delete action
    const dialogRef = this.dialog.open(YesNoDialogComponent, {
      width: '600px',
      data: {
        Title: "Confirm Delete",
        Subtitle: "Confirm you want to process with deleting record",
        Message: "Are you sure you want to delete this record?",
        IconName: "delete"
      },
    });

    dialogRef.afterClosed().subscribe(result => {

      // If user confirms action, then proceed to delete record
      if (result == IYNEnum.Y) {

        //            this.deleteDigitalSvcVersionRecord()
      }

      else {
        this.showMessage("User cancelled delete")
      }

    });

  }

  // Method to get the DigitalSvcVersion record from the back-end and set the form
  public getDigitalSvcVersionRecord(digitalSvcVersionRecordId: string) {

    this.isLoading = true

    this.digitalSvcVersionService.getDigitalSvcVersionRecord(digitalSvcVersionRecordId)
      .subscribe({
        next: (data) => {
          
          // Update the DigitalSvcVersion with the new values
          this.DigitalSvcVersion = data.data
          this.resetDigitalSvcVersionForm()

          this.isLoading = false

        },
        error: () => {

          // Reset the Task record
          this.DigitalSvcVersion = null
          this.resetDigitalSvcVersionForm()

          this.showMessage("Error getting DigitalSvcVersion record")

        }
      });


  }

  // Method to clear the current DigitalSvcVersion Record
  public clearDigitalSvcVersionRecord() {

    this.DigitalSvcVersion = null
    this.resetDigitalSvcVersionForm()

  }


  // Method to create the DigitalSvcVersion Record on the back-end
  private createDigitalSvcVersionRecord() {

    // Create the update payload
    let payload:IDigitalSvcVersion = this.getChangedFieldsFromForm()

    // Call the back-end to create the record
    this.digitalSvcVersionService.createDigitalSvcVersionRecord(payload)
      .subscribe({
        next: (data) => {
          
          // Create has been successful, so set the DigitalSvcVersion
          this.DigitalSvcVersion = data.data
          this.resetDigitalSvcVersionForm()

          this.showMessage("Create Successful")

          // Tell the world that a new DigitalSvcVersion has been successfully created
          this.digitalSvcVersionCreated.emit(this.DigitalSvcVersion!)

        },
        error: (error) => {

          // If specific error message returned, then show specific message otherwise show a generic message
          if (error.error && error.error.errormsg) {
            this.showMessage("Error creating DigitalSvcVersion record. " + error.error.errormsg)
            console.error("Error creating DigitalSvcVersion record. " + error.error.errormsg)
          }

          else {
            this.showMessage("Error creating record")
          }
        }
      });

  }
  
  // Method to update the DigitalSvcVersion Record on the back-end
  private updateDigitalSvcVersionRecord() {

    var payload: IDigitalSvcVersion
    var recordId: string

    // Get the id of the record to update
    recordId = this.DigitalSvcVersion?.id || ''

    // Create the update payload
    payload = this.getChangedFieldsFromForm()

    // Call the back-end to update the record
    this.digitalSvcVersionService.updateDigitalSvcVersionRecord(recordId, payload)
      .subscribe({
        next: (data) => {

          // Update has been successful, so update DigitalSvcVersion with the new values
          this.DigitalSvcVersion = data.data
          this.resetDigitalSvcVersionForm()

          this.showMessage("Update Successful")

          // Tell the world that a DigitalSvcVersion has been successfully updated
          this.digitalSvcVersionUpdated.emit(this.DigitalSvcVersion!)

        },
        error: (error) => {

          // If specific error message returned, then show specific message otherwise show a generic message
          if (error.error && error.error.errormsg) {
            this.showMessage("Error updating DigitalSvcVersion record. " + error.error.errormsg)
            console.error("Error updating DigitalSvcVersion record. " + error.error.errormsg)
          }

          else {
            this.showMessage("Error updating record")
          }
        }
      });

  }


  getChangedFieldsFromForm() {

    let tempDigitalSvcVersion:IDigitalSvcVersion = {}

    if (this.digitalSvcVersionForm.controls.DigitalServiceRecordId.dirty) {
      tempDigitalSvcVersion.DigitalServiceRecordId = this.digitalSvcVersionForm.controls.DigitalServiceRecordId.value!
    }
    if (this.digitalSvcVersionForm.controls.ServiceName.dirty) {
      tempDigitalSvcVersion.ServiceName = this.digitalSvcVersionForm.controls.ServiceName.value!
    }
    if (this.digitalSvcVersionForm.controls.APIName.dirty) {
      tempDigitalSvcVersion.APIName = this.digitalSvcVersionForm.controls.APIName.value!
    }
    if (this.digitalSvcVersionForm.controls.Version.dirty) {
      tempDigitalSvcVersion.Version = this.digitalSvcVersionForm.controls.Version.value!
    }
    if (this.digitalSvcVersionForm.controls.Status.dirty) {
      tempDigitalSvcVersion.Status = this.digitalSvcVersionForm.controls.Status.value!
    }
    if (this.digitalSvcVersionForm.controls.ReleaseDate.dirty) {
      tempDigitalSvcVersion.ReleaseDate = this.digitalSvcVersionForm.controls.ReleaseDate.value!
    }
    if (this.digitalSvcVersionForm.controls.Description.dirty) {
      tempDigitalSvcVersion.Description = this.digitalSvcVersionForm.controls.Description.value!
    }
    
    return tempDigitalSvcVersion

  }


  // Method to reset the DigitalSvcVersion Form to the values of the current DigitalSvcVersion record
  resetDigitalSvcVersionForm() {

    // If task record exists, then set the form values to the record values
    if (this.DigitalSvcVersion) {

      this.digitalSvcVersionForm.reset({
        DigitalServiceRecordId: this.DigitalSvcVersion?.DigitalServiceRecordId ?? '',
        ServiceName: this.DigitalSvcVersion?.ServiceName ?? '',
        APIName: this.DigitalSvcVersion?.APIName ?? '',
        Version: this.DigitalSvcVersion?.Version ?? '',
        Status: this.DigitalSvcVersion?.Status ?? '',
        ReleaseDate: this.DigitalSvcVersion?.ReleaseDate,
        Description: this.DigitalSvcVersion?.Description ?? '',
      });

    }
    // Else reset the form to empty values
    else {

      this.digitalSvcVersionForm.reset()

    }

  }

  OpenDigitalSvcListDialog() {
    
    // Open the DigitalSvc List Dialog
    const dialogRef = this.dialog.open(DigitalSvcListDialogComponent, {
      width: '60%',
      height: 'fit',
      data: {},
    });

    // When the dialog is closed, get the selected DigitalSvc
    dialogRef.afterClosed().subscribe((result: IDigitalSvc) => {

      // If a result was returned, then set the DigitalSvc
      if (result) {

        // Set the DigitalService Record Id in the form field
        this.digitalSvcVersionForm.controls.DigitalServiceRecordId.setValue(result.id!);
        this.digitalSvcVersionForm.controls.DigitalServiceRecordId.markAsDirty();

        // Set the Service Name in the form field
        this.digitalSvcVersionForm.controls.ServiceName.setValue(result.ServiceName!);
        this.digitalSvcVersionForm.controls.ServiceName.markAsDirty();

        // Set the API Name in the form field
        this.digitalSvcVersionForm.controls.APIName.setValue(result.APIName!);
        this.digitalSvcVersionForm.controls.APIName.markAsDirty();

      }

    });
  }


  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
