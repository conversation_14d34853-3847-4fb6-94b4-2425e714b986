@if (isLoading) { 
<mat-spinner></mat-spinner> 
}

@else {

<mat-card class="example-card">

    <form [formGroup]="digitalSvcVersionForm">

        <div class="form-row">

            <mat-form-field>
                <mat-label for="service-name">Service: </mat-label>
                <input matInput id="service-name" type="text" formControlName="ServiceName" readonly="true">

                @if (this.allowDigitalSvcSelection) {
                <button 
                type="button" 
                mat-icon-button 
                matSuffix
                matTooltip="Pick a linked record"
                (click)="OpenDigitalSvcListDialog()">
                <mat-icon matSuffix class="material-icons md-18">chevron_right</mat-icon>
                </button>
                }

            </mat-form-field>

            <mat-form-field>
                <mat-label for="api-name">API Name:</mat-label>
                <input matInput id="api-name" type="text" formControlName="APIName" readonly="true">
            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="version">Version:</mat-label>
                <mat-select id="version" formControlName="Version">
                    <mat-option value="1">1</mat-option>
                    <mat-option value="2">2</mat-option>
                    <mat-option value="3">3</mat-option>
                    <mat-option value="4">4</mat-option>
                    <mat-option value="5">5</mat-option>
                    <mat-option value="6">6</mat-option>
                    <mat-option value="7">7</mat-option>
                    <mat-option value="8">8</mat-option>
                    <mat-option value="9">9</mat-option>
                    <mat-option value="10">10</mat-option>
                  </mat-select>
            </mat-form-field>


            <mat-form-field>
                <mat-label for="status">Status:</mat-label>
                <mat-select id="status" formControlName="Status">
                    <mat-option value="Planned">Planned</mat-option>
                    <mat-option value="Active">Active</mat-option>
                  </mat-select>
            </mat-form-field>

        </div>

        <div class="form-row-single-col">

            <mat-form-field>
                <mat-label for="release-date">Release Date:</mat-label>
                <input matInput id="release-date" type="text" formControlName="ReleaseDate">
            </mat-form-field>

        </div>

        <mat-form-field>
            <mat-label for="description">Description:</mat-label>
            <textarea matInput id="description" formControlName="Description" rows="3"></textarea>
        </mat-form-field>

        <mat-divider style="margin-top: 10px;"></mat-divider>

        <div style="display: flex; justify-content: center;"> 
            <button style="margin-right: 20px" mat-raised-button [disabled]="!DigitalSvcVersion" type="button" (click)="onDeleteClick()">Delete</button>
            <button style="margin-right: 20px" mat-raised-button [disabled]="digitalSvcVersionForm.pristine" type="button" (click)="onCancelClick()">Cancel</button>
            <button mat-raised-button [disabled]="digitalSvcVersionForm.pristine" type="submit" (click)="onSaveClick()">Submit</button>
        </div>

    </form>

</mat-card>

}