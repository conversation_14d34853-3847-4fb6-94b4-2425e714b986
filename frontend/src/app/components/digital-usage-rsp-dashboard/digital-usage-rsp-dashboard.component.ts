import { Component, OnInit } from '@angular/core';

import { AccessSeekerService } from '../../services/access-seeker.service';
import { DigitalUsageChartService } from '../../services/digital-usage-charts.service';
import { IAccessSeeker, IAccessSeekerSkeleton } from '../../models/models';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule, MatSelectChange } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';

import { AgCharts } from "ag-charts-angular";
import { AgGridAngular } from "ag-grid-angular";
import { ColDef } from "ag-grid-community";

import {
  AgBarSeriesOptions,
  AgCategoryAxisOptions,
  AgChartCaptionOptions,
  AgChartLegendOptions,
  AgChartOptions,
  AgChartSubtitleOptions,
  AgLineSeriesOptions,
  AgNumberAxisOptions,
} from "ag-charts-community";


@Component({
  selector: 'app-digital-usage-rsp-dashboard',
  imports: [
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatFormFieldModule,
    MatSelectModule,
    MatTabsModule,
    MatProgressSpinnerModule,
    AgCharts,
    AgGridAngular
  ],
  templateUrl: './digital-usage-rsp-dashboard.component.html',
  styleUrl: './digital-usage-rsp-dashboard.component.scss'
})
export class DigitalUsageRspDashboardComponent implements OnInit {

  // The list of access seekers to be displayed in the dropdown
  public accessSeekers: IAccessSeeker[] = []

  // The selected access seeker id from the dropdown
  public selectedAccessSeeker: IAccessSeeker = {}

  // A flag for if the charts are loading
  isChartsLoading: boolean = false

  // Array of chart options for all the digital usage charts
  public rspUsageChartOptions: AgChartOptions[] = [];

  // Set the usage grid column definitions
  usageGridColDefs: ColDef[] = [
    { field: "Period" },
    { field: "TotalAPITxns" },
    { field: "TotalPortalTxns" },
    { field: "TotalTxns" },
    { field: "SuccessAPITxns" },
    { field: "APIBusExceptionCount" },
    { field: "APITechExceptionCount" }
  ];

  // Set the usage grid default column definitions
  usageGridDefaultColDef: ColDef = {
    flex: 1,
  };
  
  constructor(
    private snackBar: MatSnackBar, 
    private accessSeekerService: AccessSeekerService,
    private digitalUsageChartService: DigitalUsageChartService) { }

  
  ngOnInit(): void {

    this.getAccessSeekers()

  }

  getAccessSeekers() {

    let payload = {"startRow":0,"endRow":100,"filterModel":{},"sortModel":[]}

    this.accessSeekerService.getAccessSeekerRecords(payload)
    .subscribe({
      next: (data: any) => {

        // Set the access seekers to the data returned from the server
        this.accessSeekers = data["records"]

      },
      error: (error: any) => {

        // If an error was returned, then show the error. Possible clean-up the variable naming conventions (to many errors :-))
        if (error.error && error.error.errormsg) {
          this.showMessage("Error Retriving AccessSeeker records. " + error.error.errormsg)
          console.error("Error Retriving AccessSeeker records. " + error.error.errormsg)
        }

      }
    });

  }

  onChangeRSP(event: MatSelectChange) {

    // Track that the charts are loading
    this.isChartsLoading = true

    // Reset any existing charts
    this.rspUsageChartOptions = []

    // Get the digital usage for the selected access seeker & create the new charts
    this.getDigitalUsageForRSP()

  }

  // Method to create AG Charts options for digital usage charts
  private createChartOptions(chartTitle: string, chartSubtitle: string, chartData: any[]) {

    const chartOptions: AgChartOptions = {
      // Chart Title
      title: { text: chartTitle } as AgChartCaptionOptions,
      // Chart Subtitle
      subtitle: { text: chartSubtitle } as AgChartSubtitleOptions,
      // Data: Data to be displayed within the chart
      data: chartData,
      // Series: Defines which chart type and data to use
      series: [
        {
          type: "line",
          xKey: "Period",
          yKey: "TotalAPITxns",
          yName: "Total API Txns",
        } as AgLineSeriesOptions,
        {
          type: "line",
          xKey: "Period",
          yKey: "TotalPortalTxns",
          yName: "Total Portal Txns",
        } as AgLineSeriesOptions,
        {
          type: "line",
          xKey: "Period",
          yKey: "TotalTxns",
          yName: "Total Txns",
        } as AgLineSeriesOptions,
      ],
      // Axes: Configure the axes for the chart
      axes: [
        // Display category (xKey) as the bottom axis
        {
          type: "category",
          position: "bottom",
        } as AgCategoryAxisOptions,
        // Use left axis for 'iceCreamSales' series
        {
          type: "number",
          position: "left",
          keys: ["TotalAPITxns", "TotalPortalTxns", "TotalTxns"],
          // Format the label applied to this axis
          label: {
            formatter: (params) => {
              return parseFloat(params.value).toLocaleString();
            },
          },
        } as AgNumberAxisOptions,
      ],
      // Legend: Matches visual elements to their corresponding series or data categories.
      legend: {
        position: "right",
      } as AgChartLegendOptions,
    };

    return chartOptions;

  }

  getDigitalUsageForRSP() {

    // If an access seeker is selected, then get the digital usage data for that access seeker
    if (this.selectedAccessSeeker.AccessSeekerId) {

      this.digitalUsageChartService.getDigitalUsageChartDataForRSP(this.selectedAccessSeeker.AccessSeekerId)
      .subscribe({
        next: (data) => {

          // Loop through the digital usage for each services and create a chart for each one
          for (let aDigitalServiceName in data.DigitalUsage) {

            // Get the relevant inputs for the chart
            const chartTitle = this.selectedAccessSeeker.Name + " (" + this.selectedAccessSeeker.AccessSeekerId + ")"
            const chartSubtitleTitle = "Digital Usage for " + data.DigitalUsage[aDigitalServiceName].ServiceName 
            const chartData = data.DigitalUsage[aDigitalServiceName].UsageRecords

            // Create the chart options
            const chartOptions = this.createChartOptions (chartSubtitleTitle, chartTitle, chartData)

            // Add the chart options for the service to array of all chart options
            this.rspUsageChartOptions.push(chartOptions)

          }

          // Track that the charts have finished loading
          this.isChartsLoading = false

        },
        error: (error) => {
            this.showMessage("Error getting data")
        }
      });

    }

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }

}
