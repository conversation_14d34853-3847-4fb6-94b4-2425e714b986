import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DigitalUsageRspDashboardComponent } from './digital-usage-rsp-dashboard.component';

describe('DigitalUsageRspDashboardComponent', () => {
  let component: DigitalUsageRspDashboardComponent;
  let fixture: ComponentFixture<DigitalUsageRspDashboardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DigitalUsageRspDashboardComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DigitalUsageRspDashboardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
