<mat-card class="example-card">

    <mat-card-title class="example-card-title">
        <mat-icon class="material-icons md-24" color="primary">leaderboard</mat-icon>
        RSP Digital Usage Dashboard
    </mat-card-title>

    <mat-divider></mat-divider>

    <mat-card-content class="example-card-content">

        <div style = "display: flex; justify-content: flex-end;">

            <mat-form-field style="width: 400px;">
                <mat-label for="access-seeker">Select Access Seeker</mat-label>
                <mat-select id="access-seeker" [(value)]="selectedAccessSeeker" (selectionChange)="onChangeRSP($event)">
                    @for (accessSeeker of accessSeekers; track accessSeeker) {
                        <mat-option [value]="accessSeeker">{{accessSeeker.Name}}</mat-option>
                      }
                </mat-select>
            </mat-form-field>

        </div>

        @if (isChartsLoading) { 
            <mat-spinner diameter="40"></mat-spinner>Mouse running in a wheel...
        } 
        @else {

            @for (rspUsageChartOption of rspUsageChartOptions; track rspUsageChartOption) {

                <mat-card class="example-card">

                    <mat-card-title class="example-card-title" style="background-color: whitesmoke;">
                        {{rspUsageChartOption.title?.text}}
                    </mat-card-title>

                    <mat-card-content class="example-card-content">

                        <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start">

                            <mat-tab label="Chart">
                
                                <ag-charts class="digital-usage-chart"
                                [options]="rspUsageChartOption"
                                ></ag-charts>
                
                            </mat-tab>

                            <mat-tab label="Data">
                
                                <ag-grid-angular class="digital-usage-grid"
                                [rowData]="rspUsageChartOption.data"
                                [columnDefs]="usageGridColDefs"
                                [defaultColDef]="usageGridDefaultColDef"
                                />
                
                            </mat-tab>

                        </mat-tab-group>

                    </mat-card-content>

                </mat-card>
                <br>

            }

        }

    </mat-card-content>
    
</mat-card>
