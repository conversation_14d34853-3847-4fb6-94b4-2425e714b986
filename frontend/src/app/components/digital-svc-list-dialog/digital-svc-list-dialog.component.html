<mat-card class="example-card-title">

    <mat-card-title class="example-card-title">
        <mat-icon class="material-icons md-24" color="primary">widgets</mat-icon>
        Digital Services
    </mat-card-title>

    <mat-divider></mat-divider>

    <mat-card-content class="example-card-content"> 

        <app-digital-svc-list 
            (showCreateButton)="false"
            (selectionChanged)="onSelectionChanged($event)">
        </app-digital-svc-list>
    
        <div mat-dialog-actions align="center">
            <button mat-button [disabled]="!SelectedRecord.id" type="button" color="primary" (click)="onPickClick()">Pick</button>
            <button mat-button type="button" color="accent" (click)="onCancelClick()" >Cancel</button>
        </div>

    </mat-card-content>
    
</mat-card>
