import { Component, Inject, OnInit } from '@angular/core';

import { INote, INoteSkeleton } from '../../models/models';
import { NoteFormComponent } from '../note-form/note-form.component';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';


@Component({
  selector: 'app-note-form-dialog',
  imports: [
    NoteFormComponent,
    MatCardModule,
    MatIconModule,
    MatDividerModule
  ],
  templateUrl: './note-form-dialog.component.html',
  styleUrl: './note-form-dialog.component.scss'
})
export class NoteFormDialogComponent  implements OnInit {

  constructor(
    @Inject(MAT_DIALOG_DATA) public noteSkeleton: INoteSkeleton,
    public dialogRef: MatDialogRef<NoteFormDialogComponent>,
    private snackBar: MatSnackBar) { }

  ngOnInit(): void {

  }

  onNoteCreated(note: INote) {
    
    // Close the dialog and return the note record
    this.dialogRef.close(note);

  }

  onNoteUpdated(note: INote) {

    // Close the dialog and return the note record
    this.dialogRef.close(note);

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
