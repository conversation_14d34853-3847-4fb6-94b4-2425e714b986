import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { AgChartsModule } from 'ag-charts-angular';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';

import { CioRspExecutiveService } from '../../services/cio-rsp-executive.service';
import { IRSPDigitalUsage } from '../../models/models';

@Component({
  selector: 'app-cio-rsp-executive-rsp-usage',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    AgChartsModule,
    AgGridAngular
  ],
  templateUrl: './cio-rsp-executive-rsp-usage.component.html',
  styleUrl: './cio-rsp-executive-rsp-usage.component.scss'
})
export class CioRspExecutiveRspUsageComponent implements OnInit, OnChanges {
  @Input() period: string = '';

  // Data for the chart
  rspUsageData: IRSPDigitalUsage[] = [];

  // Chart options
  options: any;

  // AG Grid column definitions
  columnDefs: ColDef[] = [
    { field: 'DigitalVolRank', headerName: 'Digital Vol Rank', sortable: true, filter: true, width: 150 },
    { field: 'RSPName', headerName: 'RSP Name', sortable: true, filter: true },
    {
      field: 'APIPercentage',
      headerName: '% Txns via APIs',
      sortable: true,
      filter: true,
      valueFormatter: params => params.value ? `${params.value.toFixed(1)}%` : '0%'
    },
    { field: 'TotalServices', headerName: 'Total Services', sortable: true, filter: true },
    {
      field: 'TxnPerService',
      headerName: 'Txn/Service Ratio',
      sortable: true,
      filter: true,
      valueFormatter: params => params.value ? params.value.toLocaleString() : '0'
    },
    {
      field: 'TotalAPITxns',
      headerName: 'API Transactions',
      sortable: true,
      filter: true,
      valueFormatter: params => params.value ? params.value.toLocaleString() : '0'
    },
    {
      field: 'TotalPortalTxns',
      headerName: 'Portal Transactions',
      sortable: true,
      filter: true,
      valueFormatter: params => params.value ? params.value.toLocaleString() : '0'
    },
    {
      field: 'TotalTxns',
      headerName: 'Total Transactions',
      sortable: true,
      filter: true,
      valueFormatter: params => params.value ? params.value.toLocaleString() : '0'
    }
  ];

  // AG Grid default column definitions
  defaultColDef: ColDef = {
    flex: 1,
    minWidth: 100,
    resizable: true
  };

  // Selected tab index
  selectedTabIndex: number = 0;

  constructor(private cioRspExecutiveService: CioRspExecutiveService) { }

  ngOnInit(): void {
    if (this.period) {
      this.loadRSPUsageData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['period'] && !changes['period'].firstChange) {
      this.loadRSPUsageData();
    }
  }

  // Load RSP usage data
  loadRSPUsageData(): void {
    this.cioRspExecutiveService.getRSPDigitalUsage(this.period).subscribe(
      (data) => {
        this.rspUsageData = data;
        this.generateChart();
      },
      (error) => {
        console.error('Error loading RSP usage data:', error);
      }
    );
  }

  // Generate the chart
  generateChart(): void {
    console.log('Generating RSP usage chart with data:', this.rspUsageData);

    if (!this.rspUsageData || this.rspUsageData.length === 0) {
      console.warn('No RSP usage data available for chart');
      return;
    }

    // Sort data by total transactions
    const sortedData = [...this.rspUsageData].sort((a, b) => b.TotalTxns - a.TotalTxns);

    // Create series data with minimal configuration
    this.options = {
      data: sortedData,
      series: [
        {
          type: 'bar',
          xKey: 'RSPName',
          yKey: 'TotalAPITxns',
          yName: 'API Transactions',
          stacked: true,
        },
        {
          type: 'bar',
          xKey: 'RSPName',
          yKey: 'TotalPortalTxns',
          yName: 'Portal Transactions',
          stacked: true,
        }
      ],
      legend: {
        position: 'bottom',
      },
      axes: [
        {
          type: 'category',
          position: 'bottom',
        },
        {
          type: 'number',
          position: 'left',
        },
      ],
    };

    console.log('RSP usage chart options set:', this.options);
  }
}
