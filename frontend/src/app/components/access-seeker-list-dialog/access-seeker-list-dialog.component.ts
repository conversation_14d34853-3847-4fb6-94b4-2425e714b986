import { Component } from '@angular/core';
import { MatDialogRef, MatDialogModule } from '@angular/material/dialog';

import { AccessSeekerListComponent } from '../access-seeker-list/access-seeker-list.component';

import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar } from '@angular/material/snack-bar';

import { SelectionChangedEvent } from 'ag-grid-enterprise';
import { IAccessSeeker } from '../../models/models';

@Component({
  selector: 'app-access-seeker-list-dialog',
  imports: [
    AccessSeekerListComponent, 
    MatCardModule,
    MatDividerModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule
  ],
  templateUrl: './access-seeker-list-dialog.component.html',
  styleUrl: './access-seeker-list-dialog.component.scss'
})
export class AccessSeekerListDialogComponent {

  SelectedRecord: IAccessSeeker = {};

  constructor(
      public dialogRef: MatDialogRef<AccessSeekerListDialogComponent>,
      private snackBar: MatSnackBar,

  ) { }

  // AG-Grid event triggered when a row selection has changed
  onSelectionChanged(event: SelectionChangedEvent) {

    let selectedRows = event.api.getSelectedRows()

    // If a row has been selected, then get the Access Seeler and update the form
    if (selectedRows && selectedRows[0]) {
      this.SelectedRecord = selectedRows[0]
    }
    else {
      this.SelectedRecord = {}
    }

  }

  // Triggered when the user picks a record
  onPickClick(): void {

      // Close the dialog and return the picked record
      this.dialogRef.close(this.SelectedRecord);

  }

  // Triggered when the user clicks cancel. In this case, return no data
  onCancelClick(): void {
    this.dialogRef.close();
  }  

}
