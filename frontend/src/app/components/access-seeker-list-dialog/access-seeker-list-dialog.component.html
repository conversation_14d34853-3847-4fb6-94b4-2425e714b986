<mat-card class="example-card-title">

    <mat-card-title class="example-card-title">
        <mat-icon class="material-icons md-24" color="primary">cable</mat-icon>
        Access Seekers
    </mat-card-title>

    <mat-divider></mat-divider>

    <mat-card-content class="example-card-content"> 

        <app-access-seeker-list 
            (showCreateButton)="false"
            (selectionChanged)="onSelectionChanged($event)">
        </app-access-seeker-list>
    
        <div mat-dialog-actions align="center">
            <button mat-button [disabled]="!SelectedRecord.id" type="button" color="primary" (click)="onPickClick()">Pick</button>
            <button mat-button type="button" color="accent" (click)="onCancelClick()" >Cancel</button>
        </div>

    </mat-card-content>
    
</mat-card>
