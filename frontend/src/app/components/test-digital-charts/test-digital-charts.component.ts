import { Component, OnInit } from '@angular/core';

import { DigitalUsageChartService } from '../../services/digital-usage-charts.service';

import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar } from '@angular/material/snack-bar';

import { AgCharts } from "ag-charts-angular";
import {
  AgBarSeriesOptions,
  AgCategoryAxisOptions,
  AgChartCaptionOptions,
  AgChartLegendOptions,
  AgChartOptions,
  AgChartSubtitleOptions,
  AgLineSeriesOptions,
  AgNumberAxisOptions,
} from "ag-charts-community";


interface IDigitalUsageHistory {
  Period: string;
  TotalAPITxns: number;
  TotalPortalTxns: number;
  TotalTxns: number;
}

@Component({
  selector: 'app-test-digital-charts',
  imports: [
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    AgCharts
  ],
  templateUrl: './test-digital-charts.component.html',
  styleUrl: './test-digital-charts.component.scss'
})
export class TestDigitalChartsComponent implements OnInit {

  constructor(
    private snackBar: MatSnackBar, 
    private digitalUsageChartService: DigitalUsageChartService) { }

  public digitalUsageChartOptions!: AgChartOptions;

  
  ngOnInit(): void {

    this.initialiseDigitalUsageChart();

    this.getDigitalUsageHistory();

  }

  // Method to update the Contact Record on the back-end
  private initialiseDigitalUsageChart() {

    this.digitalUsageChartOptions = {
      // Chart Title
      title: { text: "RSP Digital Usage History" } as AgChartCaptionOptions,
      // Chart Subtitle
      subtitle: { text: "Put Subtitle Here" } as AgChartSubtitleOptions,
      // Data: Data to be displayed within the chart
      data: [],
      // Series: Defines which chart type and data to use
      series: [
        {
          type: "line",
          xKey: "Period",
          yKey: "TotalAPITxns",
          yName: "Total API Txns",
        } as AgLineSeriesOptions,
        {
          type: "line",
          xKey: "Period",
          yKey: "TotalPortalTxns",
          yName: "Total Portal Txns",
        } as AgLineSeriesOptions,
        {
          type: "line",
          xKey: "Period",
          yKey: "TotalTxns",
          yName: "Total Txns",
        } as AgLineSeriesOptions,
      ],
      // Axes: Configure the axes for the chart
      axes: [
        // Display category (xKey) as the bottom axis
        {
          type: "category",
          position: "bottom",
        } as AgCategoryAxisOptions,
        // Use left axis for 'iceCreamSales' series
        {
          type: "number",
          position: "left",
          keys: ["TotalAPITxns", "TotalPortalTxns", "TotalTxns"],
          // Format the label applied to this axis
          label: {
            formatter: (params) => {
              return parseFloat(params.value).toLocaleString();
            },
          },
        } as AgNumberAxisOptions,
      ],
      // Legend: Matches visual elements to their corresponding series or data categories.
      legend: {
        position: "right",
      } as AgChartLegendOptions,
    };

  }


  // Method to update the Contact Record on the back-end
  private getDigitalUsageHistory() {

    // Call the back-end to update the record
    this.digitalUsageChartService.getDigitalUsageTest()
      .subscribe({
        next: (data) => {
          
          // Update the chart with the latest data
          let tempChartOption = {...this.digitalUsageChartOptions}
          tempChartOption.data = <IDigitalUsageHistory[]>data.data;
          this.digitalUsageChartOptions = tempChartOption;


        },
        error: (error) => {
            this.showMessage("Error getting data")
        }
      });

  }


  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
