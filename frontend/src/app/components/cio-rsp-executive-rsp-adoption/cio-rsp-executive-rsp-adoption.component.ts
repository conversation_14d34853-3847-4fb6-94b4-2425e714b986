import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { AgChartsModule } from 'ag-charts-angular';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';

import { CioRspExecutiveService } from '../../services/cio-rsp-executive.service';
import { IRSPAPIAdoption } from '../../models/models';

@Component({
  selector: 'app-cio-rsp-executive-rsp-adoption',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    AgChartsModule,
    AgGridAngular
  ],
  templateUrl: './cio-rsp-executive-rsp-adoption.component.html',
  styleUrl: './cio-rsp-executive-rsp-adoption.component.scss'
})
export class CioRspExecutiveRspAdoptionComponent implements OnInit, OnChanges {
  @Input() period: string = '';

  // Data for the chart
  rspAdoptionData: IRSPAPIAdoption[] = [];

  // Chart options
  options: any;

  // AG Grid column definitions
  columnDefs: ColDef[] = [
    { field: 'APIName', headerName: 'API Name', sortable: true, filter: true },
    { field: 'CertCount', headerName: 'Certified RSPs', sortable: true, filter: true },
    { field: 'UtilCount', headerName: 'Utilizing RSPs', sortable: true, filter: true },
    {
      field: 'UtilizationRate',
      headerName: 'Utilization Rate',
      sortable: true,
      filter: true,
      valueGetter: params => {
        if (params.data.CertCount > 0) {
          return (params.data.UtilCount / params.data.CertCount) * 100;
        }
        return 0;
      },
      valueFormatter: params => `${params.value.toFixed(1)}%`
    }
  ];

  // AG Grid default column definitions
  defaultColDef: ColDef = {
    flex: 1,
    minWidth: 100,
    resizable: true
  };

  // Selected tab index
  selectedTabIndex: number = 0;

  constructor(private cioRspExecutiveService: CioRspExecutiveService) { }

  ngOnInit(): void {
    if (this.period) {
      this.loadRSPAdoptionData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['period'] && !changes['period'].firstChange) {
      this.loadRSPAdoptionData();
    }
  }

  // Load RSP adoption data
  loadRSPAdoptionData(): void {
    this.cioRspExecutiveService.getRSPAPIAdoption(this.period).subscribe(
      (data) => {
        this.rspAdoptionData = data;
        this.generateChart();
      },
      (error) => {
        console.error('Error loading RSP adoption data:', error);
      }
    );
  }

  // Generate the chart
  generateChart(): void {
    console.log('Generating RSP adoption chart with data:', this.rspAdoptionData);

    if (!this.rspAdoptionData || this.rspAdoptionData.length === 0) {
      console.warn('No RSP adoption data available for chart');
      return;
    }

    // Sort data by certified count
    const sortedData = [...this.rspAdoptionData].sort((a, b) => b.CertCount - a.CertCount);

    // Create series data with minimal configuration
    this.options = {
      data: sortedData,
      series: [
        {
          type: 'bar',
          xKey: 'APIName',
          yKey: 'CertCount',
          yName: 'Certified RSPs',
        },
        {
          type: 'bar',
          xKey: 'APIName',
          yKey: 'UtilCount',
          yName: 'Utilizing RSPs',
        }
      ],
      legend: {
        position: 'bottom',
      },
      axes: [
        {
          type: 'category',
          position: 'bottom',
        },
        {
          type: 'number',
          position: 'left',
        },
      ],
    };

    console.log('RSP adoption chart options set:', this.options);
  }
}
