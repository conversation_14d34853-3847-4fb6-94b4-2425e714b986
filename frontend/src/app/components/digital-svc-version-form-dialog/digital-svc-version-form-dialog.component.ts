import { Component, Inject, OnInit } from '@angular/core';

import { IDigitalSvcVersion, IDigitalSvcVersionSkeleton } from '../../models/models';
import { DigitalSvcVersionFormComponent } from '../digital-svc-version-form/digital-svc-version-form.component';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';


@Component({
  selector: 'app-digital-svc-version-form-dialog',
  imports: [
    DigitalSvcVersionFormComponent,
    MatCardModule,
    MatIconModule,
    MatDividerModule
  ],
  templateUrl: './digital-svc-version-form-dialog.component.html',
  styleUrl: './digital-svc-version-form-dialog.component.scss'
})
export class DigitalSvcVersionFormDialogComponent implements OnInit {

  constructor(
    @Inject(MAT_DIALOG_DATA) public digitalSvcVersionSkeleton: IDigitalSvcVersionSkeleton,
    public dialogRef: MatDialogRef<DigitalSvcVersionFormComponent>,
    private snackBar: MatSnackBar) { }

  ngOnInit(): void {

  }

  onDigitalSvcVersionCreated(digitalSvcVersion: IDigitalSvcVersion) {
    
    // Close the dialog and return the digitalSvcVersion record
    this.dialogRef.close(digitalSvcVersion);

  }

  onDigitalSvcVersionUpdated(digitalSvcVersion: IDigitalSvcVersion) {

    // Close the dialog and return the digitalSvcVersion record
    this.dialogRef.close(digitalSvcVersion);

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
