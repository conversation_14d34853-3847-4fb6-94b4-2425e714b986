import { Component, Inject, OnInit } from '@angular/core';

import { ITask, ITaskSkeleton } from '../../models/models';
import { TaskFormComponent } from '../task-form/task-form.component';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';


@Component({
  selector: 'app-task-form-dialog',
  imports: [
    TaskFormComponent,
    MatCardModule,
    MatIconModule,
    MatDividerModule
  ],
  templateUrl: './task-form-dialog.component.html',
  styleUrl: './task-form-dialog.component.scss'
})
export class TaskFormDialogComponent implements OnInit {

  constructor(
    @Inject(MAT_DIALOG_DATA) public taskSkeleton: ITaskSkeleton,
    public dialogRef: MatDialogRef<TaskFormDialogComponent>,
    private snackBar: MatSnackBar) { }

  ngOnInit(): void {

  }

  onTaskCreated(task: ITask) {
    
    // Close the dialog and return the task record
    this.dialogRef.close(task);

  }

  onTaskUpdated(task: ITask) {

    // Close the dialog and return the task record
    this.dialogRef.close(task);

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
