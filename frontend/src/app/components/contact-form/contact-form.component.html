@if (isLoading) { 
<mat-spinner></mat-spinner> 
}

@else {

<mat-card class="example-card">

    <form [formGroup]="contactForm">

        <div class="form-row">

            <mat-form-field>
                <mat-label for="access-seeker-id">Access Seeker Id: </mat-label>
                <input matInput type="text" id="access-seeker-id" type="text" formControlName="AccessSeekerId" readonly="true">

                @if (this.allowAccessSeekerSelection) {
                <button 
                type="button" 
                mat-icon-button 
                matSuffix
                matTooltip="Pick an Acccess Seeker"
                (click)="OpenAccessSeekerListDialog()">
                <mat-icon matSuffix class="material-icons md-18">chevron_right</mat-icon>
                </button>
                }

            </mat-form-field>


            <mat-form-field>
                <mat-label for="first-name">First Name:</mat-label>
                <input matInput id="first-name" type="text" formControlName="FirstName">
            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="last-name">Last Name:</mat-label>
                <input matInput id="last-name" type="text" formControlName="LastName">
            </mat-form-field>


            <mat-form-field>
                <mat-label for="role">Role:</mat-label>
                <input matInput id="role" type="text" formControlName="Role">
            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="phone">Phone:</mat-label>
                <input matInput id="phone" type="text" formControlName="Phone">
            </mat-form-field>


            <mat-form-field>
                <mat-label for="email">Email:</mat-label>
                <input matInput id="email" type="text" formControlName="Email">
            </mat-form-field>

        </div>

        <mat-form-field>
            <mat-label for="notes">Notes:</mat-label>
            <textarea matInput id="notes" formControlName="Notes" rows="4"></textarea>
        </mat-form-field>

        <mat-divider style="margin-top: 10px;"></mat-divider>

        <div style="display: flex; justify-content: center;"> 
            <button style="margin-right: 20px" mat-raised-button [disabled]="!Contact" type="button"(click)="onDeleteClick()">Delete</button>
            <button style="margin-right: 20px" mat-raised-button [disabled]="contactForm.pristine" type="button" (click)="onCancelClick()">Cancel</button>
            <button mat-raised-button [disabled]="contactForm.pristine" type="submit" (click)="onSaveClick()">Submit</button>
        </div>

    </form>

</mat-card>

}