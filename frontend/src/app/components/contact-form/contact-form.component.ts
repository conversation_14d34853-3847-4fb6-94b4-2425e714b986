import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ContactService } from '../../services/contact.service';
import { IAccessSeeker, IContact, IYNEnum } from '../../models/models';

import { AccessSeekerListDialogComponent } from '../access-seeker-list-dialog/access-seeker-list-dialog.component';

import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

import { ReactiveFormsModule, Validators } from '@angular/forms';
import { FormGroup, FormControl } from '@angular/forms';
import { YesNoDialogComponent } from '../yes-no-dialog/yes-no-dialog.component';


@Component({
  selector: 'app-contact-form',
  imports: [
    MatCardModule,
    MatDividerModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    ReactiveFormsModule
  ],
  templateUrl: './contact-form.component.html',
  styleUrl: './contact-form.component.scss'
})
export class ContactFormComponent implements OnInit {

  // Input Contact Record Id (Optional)
  @Input() contactSkeleton: IContact | null = null 

  // Output when a new Contact has been created
  @Output() contactCreated: EventEmitter<IContact> = new EventEmitter();

  // Output when a Contact has been updated
  @Output() contactUpdated: EventEmitter<IContact> = new EventEmitter();

  // Current Contact record
  Contact: IContact | null = null

  // Form Group for Contact
  contactForm = new FormGroup({
    AccessSeekerRecordId: new FormControl('', Validators.required),
    AccessSeekerId: new FormControl('', Validators.required),
    FirstName: new FormControl('', Validators.required),
    LastName: new FormControl('', Validators.required),
    Role: new FormControl(''),
    Phone: new FormControl(''),
    Email: new FormControl(''),
    Notes: new FormControl(''),
  });

  allowAccessSeekerSelection: boolean = true

  isLoading: boolean = false

  constructor(
    private snackBar: MatSnackBar, 
    private contactService: ContactService,
    public dialog: MatDialog) { }

  ngOnInit(): void {

    // If a Contact skeleton was passed in with an id, then it must be an existing record (so get the record)
    if (this.contactSkeleton && this.contactSkeleton.id != '') {

      if (this.contactSkeleton.id) {
        this.getContactRecord(this.contactSkeleton.id);
      }
    }

    // Else if a contact skeleton was passed in without an id, then it must be a create
    // set the form based on the skeleton
    else if (this.contactSkeleton && this.contactSkeleton.id == '') {
      this.Contact = this.contactSkeleton
      this.resetContactForm()

      // Prevent the user from selecting an Access Seeker
      // as it has been provided in the skeleton
      this.allowAccessSeekerSelection = false

    }

  }

  onSaveClick() {

    if (this.contactForm.valid) {

      // If Contact exists, then update the record
      if (this.Contact?.id) {
        this.updateContactRecord()
      }

      // Else we are creating a new Contact
      else {

        // If the Access Seeker selection is not allowed, then mark the fields as dirty
        // This is needed to ensure that these fields are sent to the back-end on a create.
        if (this.allowAccessSeekerSelection == false) {
          this.contactForm.controls.AccessSeekerRecordId.markAsDirty()
          this.contactForm.controls.AccessSeekerId.markAsDirty()
        }

        this.createContactRecord()
      }

    } 
    
    else {
      this.showMessage("Please fix errors before resubmitting")
    }

  }

  onCancelClick() {

    // Reset the Contact form
    this.resetContactForm()

  }

  onDeleteClick() {

    // Open a dialog to confirm the delete action
    const dialogRef = this.dialog.open(YesNoDialogComponent, {
      width: '600px',
      data: {
        Title: "Confirm Delete",
        Subtitle: "Confirm you want to process with deleting record",
        Message: "Are you sure you want to delete this record?",
        IconName: "delete"
      },
    });

    dialogRef.afterClosed().subscribe(result => {

      // If user confirms action, then proceed to delete record
      if (result == IYNEnum.Y) {

        //            this.deleteContactRecord()
      }

      else {
        this.showMessage("User cancelled delete")
      }

    });

  }

  // Method to get the Contact record from the back-end and set the form
  public getContactRecord(contactRecordId: string) {

    this.isLoading = true

    this.contactService.getContactRecord(contactRecordId)
      .subscribe({
        next: (data) => {
          
          // Update the Contact with the new values
          this.Contact = data.data
          this.resetContactForm()

          this.isLoading = false

        },
        error: () => {

          // Reset the Contact record
          this.Contact = null
          this.resetContactForm()

          this.showMessage("Error getting Contact record")

        }
      });


  }

  // Method to clear the current Contact Record
  public clearContactRecord() {

    this.Contact = null
    this.resetContactForm()

  }


  // Method to create the Contact Record on the back-end
  private createContactRecord() {

    // Create the update payload
    let payload:IContact = this.getChangedFieldsFromForm()

    // Call the back-end to create the record
    this.contactService.createContactRecord(payload)
      .subscribe({
        next: (data) => {
          
          // Create has been successful, so set the Contact
          this.Contact = data.data
          this.resetContactForm()

          this.showMessage("Create Successful")

          // Tell the world that a new Contact has been successfully created
          this.contactCreated.emit(this.Contact!)

        },
        error: (error) => {

          // If specific error message returned, then show specific message otherwise show a generic message
          if (error.error && error.error.errormsg) {
            this.showMessage("Error creating Contact record. " + error.error.errormsg)
            console.error("Error creating Contact record. " + error.error.errormsg)
          }

          else {
            this.showMessage("Error creating record")
          }
        }
      });

  }
  
  // Method to update the Contact Record on the back-end
  private updateContactRecord() {

    var payload: IContact
    var recordId: string

    // Get the id of the record to update
    recordId = this.Contact?.id || ''

    // Create the update payload
    payload = this.getChangedFieldsFromForm()

    // Call the back-end to update the record
    this.contactService.updateContactRecord(recordId, payload)
      .subscribe({
        next: (data) => {

          // Update has been successful, so update Contact with the new values
          this.Contact = data.data
          this.resetContactForm()

          this.showMessage("Update Successful")

          // Tell the world that a contact has been successfully updated
          this.contactUpdated.emit(this.Contact!)

        },
        error: (error) => {

          // If specific error message returned, then show specific message otherwise show a generic message
          if (error.error && error.error.errormsg) {
            this.showMessage("Error updating Contact record. " + error.error.errormsg)
            console.error("Error updating Contact record. " + error.error.errormsg)
          }

          else {
            this.showMessage("Error updating record")
          }
        }
      });

  }


  getChangedFieldsFromForm() {

    let tempContact:IContact = {}

    if (this.contactForm.controls.AccessSeekerRecordId.dirty) {
      tempContact.AccessSeekerRecordId = this.contactForm.controls.AccessSeekerRecordId.value!
    }
    if (this.contactForm.controls.AccessSeekerId.dirty) {
      tempContact.AccessSeekerId = this.contactForm.controls.AccessSeekerId.value!
    }
    if (this.contactForm.controls.FirstName.dirty) {
      tempContact.FirstName = this.contactForm.controls.FirstName.value!
    }
    if (this.contactForm.controls.LastName.dirty) {
      tempContact.LastName = this.contactForm.controls.LastName.value!
    }
    if (this.contactForm.controls.Role.dirty) {
      tempContact.Role = this.contactForm.controls.Role.value!
    }
    if (this.contactForm.controls.Phone.dirty) {
      tempContact.Phone = this.contactForm.controls.Phone.value!
    }
    if (this.contactForm.controls.Email.dirty) {
      tempContact.Email = this.contactForm.controls.Email.value!
    }
    if (this.contactForm.controls.Notes.dirty) {
      tempContact.Notes = this.contactForm.controls.Notes.value!
    }

    return tempContact

  }


  // Method to reset the Contact Form to the values of the current Contact record
  resetContactForm() {

    // If contact record exists, then set the form values to the record values
    if (this.Contact) {

      this.contactForm.reset({
        AccessSeekerRecordId: this.Contact?.AccessSeekerRecordId ?? '',
        AccessSeekerId: this.Contact?.AccessSeekerId ?? '',
        FirstName: this.Contact?.FirstName ?? '',
        LastName: this.Contact?.LastName ?? '',
        Role: this.Contact?.Role ?? '',
        Phone: this.Contact?.Phone ?? '',
        Email: this.Contact?.Email ?? '',
        Notes: this.Contact?.Notes ?? '',
      });

    }
    // Else reset the form to empty values
    else {

      this.contactForm.reset()

    }

  }

OpenAccessSeekerListDialog() {
  
  // Open the Access Seeker List Dialog
  const dialogRef = this.dialog.open(AccessSeekerListDialogComponent, {
    width: '60%',
    height: 'fit',
    data: {},
  });

  // When the dialog is closed, get the selected Access Seeker
  dialogRef.afterClosed().subscribe((result: IAccessSeeker) => {

    // If a result was returned, then set the Access Seeker
    if (result) {

      // Set the Access Seeker Record Id in the form field
      this.contactForm.controls.AccessSeekerRecordId.setValue(result.id!);
      this.contactForm.controls.AccessSeekerRecordId.markAsDirty();

      // Set the Access Seeker Id in the form field
      this.contactForm.controls.AccessSeekerId.setValue(result.AccessSeekerId!);
      this.contactForm.controls.AccessSeekerId.markAsDirty();

    }

  });
}

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
