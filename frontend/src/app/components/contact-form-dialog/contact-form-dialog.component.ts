import { Component, Inject, OnInit } from '@angular/core';

import { IContact, IContactSkeleton } from '../../models/models';
import { ContactFormComponent } from '../contact-form/contact-form.component';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';


@Component({
  selector: 'app-contact-form-dialog',
  imports: [
    ContactFormComponent,
    MatCardModule,
    MatIconModule,
    MatDividerModule
  ],
  templateUrl: './contact-form-dialog.component.html',
  styleUrl: './contact-form-dialog.component.scss'
})
export class ContactFormDialogComponent implements OnInit {

  constructor(
    @Inject(MAT_DIALOG_DATA) public contactSkeleton: IContactSkeleton,
    public dialogRef: MatDialogRef<ContactFormDialogComponent>,
    private snackBar: MatSnackBar) { }

  ngOnInit(): void {

  }

  onContactCreated(contact: IContact) {
    
    // Close the dialog and return the contact record
    this.dialogRef.close(contact);

  }

  onContactUpdated(contact: IContact) {

    // Close the dialog and return the contact record
    this.dialogRef.close(contact);

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
