import { Component, Inject, OnInit } from '@angular/core';

import { IProject, IProjectSkeleton } from '../../models/models';
import { ProjectFormComponent } from '../project-form/project-form.component';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';


@Component({
  selector: 'app-project-form-dialog',
  imports: [
    ProjectFormComponent,
    MatCardModule,
    MatIconModule,
    MatDividerModule
  ],
  templateUrl: './project-form-dialog.component.html',
  styleUrl: './project-form-dialog.component.scss'
})
export class ProjectFormDialogComponent implements OnInit {

  constructor(
    @Inject(MAT_DIALOG_DATA) public projectSkeleton: IProjectSkeleton,
    public dialogRef: MatDialogRef<ProjectFormDialogComponent>,
    private snackBar: MatSnackBar) { }

  ngOnInit(): void {

  }

  onProjectCreated(project: IProject) {
    
    // Close the dialog and return the project record
    this.dialogRef.close(project);

  }

  onProjectUpdated(project: IProject) {

    // Close the dialog and return the project record
    this.dialogRef.close(project);

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
