.chart-options {
  margin-bottom: 20px;

  .option-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;

    .projection-months {
      width: 180px;
    }
  }
}

.chart-container {
  height: 500px;
  width: 100%;
  margin-bottom: 30px;
  display: block;
  overflow: visible;

  ag-charts {
    display: block;
    width: 100%;
    height: 100%;
    min-height: 500px;
  }
}

.comparison-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;

  h3 {
    margin-bottom: 20px;
    color: #0066cc;
  }

  .month-selection {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    mat-form-field {
      width: 200px;
    }
  }

  .key-insights {
    margin-top: 30px;

    .insights-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;

      .insight-column {
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 5px;

        h4 {
          margin-top: 0;
          margin-bottom: 15px;
          color: #0066cc;
          font-size: 16px;
        }

        .insight-item {
          margin-bottom: 20px;

          h5 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 500;
          }

          p {
            margin: 5px 0;
            font-size: 14px;
          }
        }
      }
    }
  }
}

.data-table-container {
  margin-top: 20px;
  overflow-x: auto;

  .data-table {
    width: 100%;
    border-collapse: collapse;

    th, td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #e0e0e0;
    }

    th {
      background-color: #f5f5f5;
      font-weight: 500;
    }

    tr:hover {
      background-color: #f9f9f9;
    }
  }
}

.insights-container {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 5px;

  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #0066cc;
  }

  .placeholder-text {
    font-style: italic;
    color: #666;
  }
}
