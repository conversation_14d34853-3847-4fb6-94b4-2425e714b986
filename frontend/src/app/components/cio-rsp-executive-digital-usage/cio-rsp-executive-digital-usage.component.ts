import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { AgChartsModule } from 'ag-charts-angular';

import { CioRspExecutiveService } from '../../services/cio-rsp-executive.service';
import { IDigitalUsageHistory } from '../../models/models';

@Component({
  selector: 'app-cio-rsp-executive-digital-usage',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatTabsModule,
    MatSelectModule,
    MatFormFieldModule,
    MatCheckboxModule,
    MatInputModule,
    MatButtonModule,
    AgChartsModule
  ],
  templateUrl: './cio-rsp-executive-digital-usage.component.html',
  styleUrl: './cio-rsp-executive-digital-usage.component.scss'
})
export class CioRspExecutiveDigitalUsageComponent implements OnInit, AfterViewInit {
  // Data for the chart
  digitalUsageData: IDigitalUsageHistory[] = [];

  // Chart options
  options: any;

  // Selected months for comparison
  selectedMonth1: string = '';
  selectedMonth2: string = '';

  // Chart configuration options
  showTrendlines: boolean = true;
  showProjections: boolean = true;
  projectionMonths: number = 6;
  showConfidenceInterval: boolean = true;

  // Tab index
  selectedTabIndex: number = 0;

  // Comparison data
  comparisonData: any = null;

  constructor(private cioRspExecutiveService: CioRspExecutiveService) { }

  ngOnInit(): void {
    this.loadDigitalUsageData();
  }

  ngAfterViewInit(): void {
    // Initialize chart after view is initialized
  }

  // Load digital usage data
  loadDigitalUsageData(): void {
    this.cioRspExecutiveService.getDigitalUsageHistory().subscribe(
      (data) => {
        this.digitalUsageData = data;

        // Set default selected months
        if (this.digitalUsageData.length > 0) {
          this.selectedMonth1 = this.digitalUsageData[0].Period;
          this.selectedMonth2 = this.digitalUsageData[this.digitalUsageData.length - 1].Period;

          // Generate chart
          this.generateChart();

          // Generate comparison data
          this.generateComparisonData();
        }
      },
      (error) => {
        console.error('Error loading digital usage data:', error);
      }
    );
  }

  // Generate the chart
  generateChart(): void {
    console.log('Generating chart with data:', this.digitalUsageData);

    if (!this.digitalUsageData || this.digitalUsageData.length === 0) {
      console.warn('No data available for chart');
      return;
    }

    // Set chart options with a very basic configuration
    this.options = {
      data: this.digitalUsageData,
      series: [
        {
          type: 'line',
          xKey: 'Period',
          yKey: 'TotalTxns',
          yName: 'Total Transactions',
        },
        {
          type: 'line',
          xKey: 'Period',
          yKey: 'TotalAPITxns',
          yName: 'API Transactions',
        },
        {
          type: 'line',
          xKey: 'Period',
          yKey: 'TotalPortalTxns',
          yName: 'Portal Transactions',
        }
      ],
      legend: {
        position: 'bottom',
      },
      axes: [
        {
          type: 'category',
          position: 'bottom',
        },
        {
          type: 'number',
          position: 'left',
        },
      ],
    };

    console.log('Chart options set:', this.options);
  }

  // Generate comparison data
  generateComparisonData(): void {
    if (!this.selectedMonth1 || !this.selectedMonth2) {
      return;
    }

    // Find data for selected months
    const data1 = this.digitalUsageData.find(d => d.Period === this.selectedMonth1);
    const data2 = this.digitalUsageData.find(d => d.Period === this.selectedMonth2);

    if (!data1 || !data2) {
      return;
    }

    // Calculate metrics
    const totalDiff = data2.TotalTxns - data1.TotalTxns;
    const totalPctChange = (totalDiff / data1.TotalTxns) * 100;

    const apiDiff = data2.TotalAPITxns - data1.TotalAPITxns;
    const apiPctChange = (apiDiff / data1.TotalAPITxns) * 100;

    const portalDiff = data2.TotalPortalTxns - data1.TotalPortalTxns;
    const portalPctChange = (portalDiff / data1.TotalPortalTxns) * 100;

    // Calculate channel mix
    const apiPct1 = (data1.TotalAPITxns / data1.TotalTxns) * 100;
    const portalPct1 = (data1.TotalPortalTxns / data1.TotalTxns) * 100;

    const apiPct2 = (data2.TotalAPITxns / data2.TotalTxns) * 100;
    const portalPct2 = (data2.TotalPortalTxns / data2.TotalTxns) * 100;

    const apiShift = apiPct2 - apiPct1;

    // Store comparison data
    this.comparisonData = {
      month1: this.selectedMonth1,
      month2: this.selectedMonth2,
      data1,
      data2,
      totalDiff,
      totalPctChange,
      apiDiff,
      apiPctChange,
      portalDiff,
      portalPctChange,
      apiPct1,
      portalPct1,
      apiPct2,
      portalPct2,
      apiShift
    };
  }

  // Handle month selection change
  onMonthSelectionChange(): void {
    this.generateComparisonData();
  }

  // Handle chart option change
  onChartOptionChange(): void {
    // Regenerate the chart with new options
    this.generateChart();

    // Update UI based on option changes
    if (!this.showProjections) {
      this.showConfidenceInterval = false;
    }

    // Log the changes for debugging
    console.log('Chart options updated:', {
      showTrendlines: this.showTrendlines,
      showProjections: this.showProjections,
      projectionMonths: this.projectionMonths,
      showConfidenceInterval: this.showConfidenceInterval
    });
  }
}
