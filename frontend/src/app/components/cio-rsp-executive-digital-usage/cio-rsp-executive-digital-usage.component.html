<!-- Chart options -->
<div class="chart-options">
  <div class="option-row">
    <mat-checkbox [(ngModel)]="showTrendlines" (change)="onChartOptionChange()">
      Show trendlines
    </mat-checkbox>

    <mat-checkbox [(ngModel)]="showProjections" (change)="onChartOptionChange()">
      Show future projections
    </mat-checkbox>

    <mat-form-field appearance="fill" class="projection-months">
      <mat-label>Future projection months</mat-label>
      <input matInput type="number" [(ngModel)]="projectionMonths" min="0" max="12"
             [disabled]="!showProjections" (change)="onChartOptionChange()">
    </mat-form-field>

    <mat-checkbox [(ngModel)]="showConfidenceInterval" [disabled]="!showTrendlines || !showProjections"
                 (change)="onChartOptionChange()">
      Show confidence interval
    </mat-checkbox>
  </div>
</div>

<!-- Simplified chart display -->
<div style="width: 100%; height: 500px; border: 1px solid #ccc; margin: 20px 0;">
  <ag-charts [options]="options"></ag-charts>
</div>

<!-- Data display -->
<div class="data-table-container">
  <table class="data-table">
    <thead>
      <tr>
        <th>Period</th>
        <th>Total Transactions</th>
        <th>API Transactions</th>
        <th>Portal Transactions</th>
        <th>API %</th>
      </tr>
    </thead>
    <tbody>
      @for (item of digitalUsageData; track item.Period) {
        <tr>
          <td>{{ item.Period }}</td>
          <td>{{ item.TotalTxns | number }}</td>
          <td>{{ item.TotalAPITxns | number }}</td>
          <td>{{ item.TotalPortalTxns | number }}</td>
          <td>{{ (item.TotalAPITxns / item.TotalTxns) * 100 | number:'1.1-1' }}%</td>
        </tr>
      }
    </tbody>
  </table>
</div>
