import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ContactService } from '../../services/contact.service';

import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule, MatSlideToggleChange } from '@angular/material/slide-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';

import { ModuleRegistry, ColDef, GridOptions, IServerSideDatasource, SideBarDef, GridApi, SelectionChangedEvent, RowSelectionOptions, FilterModel } from 'ag-grid-community';
import { AgGridAngular } from "ag-grid-angular";
import { AllEnterpriseModule } from 'ag-grid-enterprise';
import { ContactFormDialogComponent } from '../contact-form-dialog/contact-form-dialog.component';
import { IAccessSeekerSkeleton, IContact, IContactSkeleton } from '../../models/models';

// Register all Community and Enterprise features
ModuleRegistry.registerModules([AllEnterpriseModule]);

@Component({
  selector: 'app-contact-list',
  imports: [
    MatIconModule,
    MatSlideToggleModule,
    MatButtonModule,
    AgGridAngular,
  ],
  templateUrl: './contact-list.component.html',
  styleUrl: './contact-list.component.css'
})
export class ContactListComponent implements OnInit {

  // Input for the parent Access Seeker (Skeleton)
  // Contacts will be filtered to only that Access Seeker if provided
  // Needs to be done with getters and setters to trigger the grid refresh
@Input()
  get parentAccessSeekerSkeleton(): IAccessSeekerSkeleton { return this._parentAccessSeekerSkeleton; }
  set parentAccessSeekerSkeleton(parentAccessSeekerSkeleton: IAccessSeekerSkeleton) {
    this._parentAccessSeekerSkeleton = parentAccessSeekerSkeleton;
    if (this.gridAPI) {
      this.RefreshGridRecords();
    }
    
  }
  private _parentAccessSeekerSkeleton:IAccessSeekerSkeleton = {
    id: '',
    AccessSeekerId: ''
  }


  @Input() showCreateButton: boolean = false;

  // Output when a Contact selection has changed in the grid
  @Output() selectionChanged: EventEmitter<SelectionChangedEvent> = new EventEmitter();

  // Output when the Create Record button is clicked
  @Output() createRecordClicked: EventEmitter<void> = new EventEmitter<void>();

  // Variables for grid
  gridAPI!: GridApi;
  gridOptions: GridOptions = {}

  // Selected Tab Index
  selectedTabIndex: number = 0

  constructor(
    private snackBar: MatSnackBar, 
    private contactService: ContactService,
    public dialog: MatDialog) { }

  ngOnInit(): void {

    // Initialise the Grid
    this.initialiseGrid();

  }

  // Define the server side data source to get grid data
  datasource: IServerSideDatasource = {

    // called by the grid when more rows are required
    getRows: params => {

      // Restrict the records to the parent Access Seeker if provided
      if (this.parentAccessSeekerSkeleton.id) {

        if (params.request.filterModel) {
          (params.request.filterModel as FilterModel)["AccessSeekerRecordId"] = {"filterType":"text","type":"equals","filter":this.parentAccessSeekerSkeleton.id};
        }

      }

      var payload = {
        startRow: params.request.startRow,
        endRow: params.request.endRow,
        filterModel: params.request.filterModel,
        sortModel: params.request.sortModel
      };

      this.contactService.getContactRecords(payload)
        .subscribe({
          next: (data: any) => {

            // Invoke the grid callback for successfully getting rows
            params.success({ rowData: data["records"], rowCount: data["totalRecords"] })

          },
          error: (error: any) => {

            // If an error was returned, then show the error. Possible clean-up the variable naming conventions (to many errors :-))
            if (error.error && error.error.errormsg) {
              this.showMessage("Error Retriving Contact records. " + error.error.errormsg)
              console.error("Error Retriving Contact records. " + error.error.errormsg)
            }

            // Inform the grid callback that getting rows failed
            params.fail()
          }
        });
    }
  };

  // Method to initialise the grid
  private initialiseGrid() {

    // Setup the default column definition
    var defaultColDef: ColDef = {
      resizable: true,
    }

    // Setup the column definition
    var columnDefs: ColDef[] = [
      { field: 'id', hide: true },
      { field: 'AccessSeekerId' , filter: 'agTextColumnFilter' },
      { field: 'FirstName', filter: 'agTextColumnFilter' },
      { field: 'LastName', filter: 'agTextColumnFilter' },
      { field: 'Role' },
      { field: 'Phone' },
      { field: 'Email' },
      { field: 'created', hide: true },
      { field: 'created_by', hide: true },
      { field: 'modified', hide: true },
      { field: 'modified_by', hide: true },
    ]

    // Setup the side-bar options
    var sideBar: SideBarDef = {
      toolPanels: [
        {
          id: 'columns',
          labelDefault: 'Columns',
          labelKey: 'columns',
          iconKey: 'columns',
          toolPanel: 'agColumnsToolPanel',
          toolPanelParams: {
            suppressRowGroups: true,
            suppressValues: true,
            suppressPivots: true,
            suppressPivotMode: true,
            suppressSideButtons: true,
            suppressColumnFilter: false,
            suppressColumnSelectAll: false,
            suppressColumnExpandAll: false,
          },
        },
      ],
      defaultToolPanel: '',
    };

    // Set Grid Options
    this.gridOptions = {
      serverSideDatasource: this.datasource,
      rowModelType: "serverSide",
      defaultColDef: defaultColDef,
      columnDefs: columnDefs,
      headerHeight: 40,
      rowHeight: 30,
      pagination: true,
      paginationPageSize: 20,
      cacheBlockSize: 20,
      sideBar: sideBar,
      rowSelection: <RowSelectionOptions>{
        mode: "singleRow",
        checkboxes: false,
        enableClickSelection: true,
      },
      getRowId: params => {
        return String(params.data.id);
      },
    };
  }

  // AG-Grid event when the Grid is ready
  onGridReady(params: any) {

      this.gridAPI = params.api 
  }

  // Event triggered when the Autosize columns toggle slide has changed
  onToggleAutoSizeColumns(event: MatSlideToggleChange) {

    var autoSizeGrid = event.checked

    // Resize the grid columns
    if (autoSizeGrid == true) {
      this.gridAPI.autoSizeAllColumns()
    }
    else {
      this.gridAPI.sizeColumnsToFit()
    }

  }

  // Method to refresh Grid Records
  RefreshGridRecords() {

    // Refresh the grids records.
    this.gridAPI.refreshServerSide({ purge: true })

  }

  // AG-Grid event triggered when a row selection has changed
  onSelectionChanged(event: SelectionChangedEvent) {

    // Tell the world the selection has changed
    // Think about if we should always tell the world, or only when the list is not
    // being used as a child of Access Seeker
    this.selectionChanged.emit(event)

    // If the contact list is being used as a Child of Access Seeker, then
    // handle select change within this component using a dialof box.
    if (this.parentAccessSeekerSkeleton.id) {

      let selectedRows = event.api.getSelectedRows()

      // If a row has been selected, then get the Access Seeler and update the form
      if (selectedRows && selectedRows[0]) {
  
        let selectedRowId = selectedRows[0].id

        let contactSkeleton: IContactSkeleton = {
          id: selectedRowId,
          AccessSeekerRecordId: this.parentAccessSeekerSkeleton.id,
          AccessSeekerId: this.parentAccessSeekerSkeleton.AccessSeekerId
        }
  
        // Open the contact form dialog to create a new contact for the parent access seeker
        const dialogRef = this.dialog.open(ContactFormDialogComponent, {
          width: '60%',
          data: contactSkeleton
        });

        dialogRef.afterClosed().subscribe(result => {
    
          // If a record was updated, then refresh the grid
          if (result) {
            // Rrefresh the grid to include the new record created
            this.RefreshGridRecords();  
          }

        });
        
      }

    }

  }

  onCreateRecordClick() {

    // Tell the world the create record button was clicked
    // Think about if we should always tell the world, or only when the list is not
    // being used as a child of Access Seeker
    this.createRecordClicked.emit()

    // If the contact list is being used as a Child of Access Seeker, then
    // handle create within this component using a dialof box.
    if (this.parentAccessSeekerSkeleton.id) {

      let contactSkeleton: IContactSkeleton = {
        id: '',
        AccessSeekerRecordId: this.parentAccessSeekerSkeleton.id,
        AccessSeekerId: this.parentAccessSeekerSkeleton.AccessSeekerId
      }

      // Open the contact form dialog to create a new contact for the parent access seeker
      const dialogRef = this.dialog.open(ContactFormDialogComponent, {
        width: '60%',
        data: contactSkeleton
      });

      dialogRef.afterClosed().subscribe(result => {
  
        // If a record was created, then refresh the grid
        if (result) {
          // Rrefresh the grid to include the new record created
          this.RefreshGridRecords();  
        }

      });

    }

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
