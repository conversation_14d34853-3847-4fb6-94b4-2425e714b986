import { Component, OnInit } from '@angular/core';

import { DigitalUsageChartService } from '../../services/digital-usage-charts.service';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule, MatSelectChange } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';

import { AgCharts } from "ag-charts-angular";
import { AgGridAngular } from "ag-grid-angular";
import { ColDef } from 'ag-grid-community';

import {
  AgBarSeriesOptions,
  AgCategoryAxisOptions,
  AgChartCaptionOptions,
  AgChartLegendOptions,
  AgChartOptions,
  AgChartSubtitleOptions,
  AgLineSeriesOptions,
  AgNumberAxisOptions,
} from "ag-charts-community";


@Component({
  selector: 'app-digital-usage-svc-dashboard',
  imports: [
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatFormFieldModule,
    MatSelectModule,
    MatTabsModule,
    MatProgressSpinnerModule,
    AgCharts,
    AgGridAngular
  ],
  templateUrl: './digital-usage-svc-dashboard.component.html',
  styleUrl: './digital-usage-svc-dashboard.component.scss'
})
export class DigitalUsageSvcDashboardComponent implements OnInit {

  // A flag for if the charts are loading
  isChartsLoading: boolean = false

  // Array of chart options for all the digital usage charts
  public svcUsageChartOptions: AgChartOptions[] = [];

  // Set the usage grid column definitions
  usageGridColDefs: ColDef[] = [
    { field: "Period" },
    { field: "TotalAPITxns" },
    { field: "TotalPortalTxns" },
    { field: "TotalTxns" },
    { field: "SuccessAPITxns" },
    { field: "APIBusExceptionCount" },
    { field: "APITechExceptionCount" }
  ];

  // Set the usage grid default column definitions
  usageGridDefaultColDef: ColDef = {
    flex: 1,
  };

  constructor(
    private snackBar: MatSnackBar, 
    private digitalUsageChartService: DigitalUsageChartService) { }

  
  ngOnInit(): void {

    // Track that the charts are loading
    this.isChartsLoading = true

    // Get the digital usage 
    this.getDigitalUsageForServices()

  }

  // Method to create AG Charts options for digital usage charts
  private createChartOptions(chartTitle: string, chartSubtitle: string, chartData: any[]) {

    const chartOptions: AgChartOptions = {
      // Chart Title
      title: { text: chartTitle } as AgChartCaptionOptions,
      // Chart Subtitle
      subtitle: { text: chartSubtitle } as AgChartSubtitleOptions,
      // Data: Data to be displayed within the chart
      data: chartData,
      // Series: Defines which chart type and data to use
      series: [
        {
          type: "line",
          xKey: "Period",
          yKey: "TotalAPITxns",
          yName: "Total API Txns",
        } as AgLineSeriesOptions,
        {
          type: "line",
          xKey: "Period",
          yKey: "TotalPortalTxns",
          yName: "Total Portal Txns",
        } as AgLineSeriesOptions,
        {
          type: "line",
          xKey: "Period",
          yKey: "TotalTxns",
          yName: "Total Txns",
        } as AgLineSeriesOptions,
      ],
      // Axes: Configure the axes for the chart
      axes: [
        // Display category (xKey) as the bottom axis
        {
          type: "category",
          position: "bottom",
        } as AgCategoryAxisOptions,
        // Use left axis for 'iceCreamSales' series
        {
          type: "number",
          position: "left",
          keys: ["TotalAPITxns", "TotalPortalTxns", "TotalTxns"],
          // Format the label applied to this axis
          label: {
            formatter: (params) => {
              return parseFloat(params.value).toLocaleString();
            },
          },
        } as AgNumberAxisOptions,
      ],
      // Legend: Matches visual elements to their corresponding series or data categories.
      legend: {
        position: "right",
      } as AgChartLegendOptions,
    };

    return chartOptions;

  }


  getDigitalUsageForServices() {

    this.digitalUsageChartService.getDigitalUsageChartDataForServices()
    .subscribe({
      next: (data) => {

        // Loop through the digital usage for each services and create a chart for each one
        for (let aDigitalServiceName in data.DigitalUsage) {

          // Get the relevant inputs for the chart
          const chartTitle = "Digital Usage for " + data.DigitalUsage[aDigitalServiceName].ServiceName 
          const chartSubtitleTitle = "Put Subtitle here"
          const chartData = data.DigitalUsage[aDigitalServiceName].UsageRecords

          // Create the chart options
          const chartOptions = this.createChartOptions (chartTitle, chartSubtitleTitle, chartData)

          // Add the chart options for the service to array of all chart options
          this.svcUsageChartOptions.push(chartOptions)

        }

        // Track that the charts have finished loading
        this.isChartsLoading = false

      },
      error: (error) => {
          this.showMessage("Error getting data")
      }
    });


  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}