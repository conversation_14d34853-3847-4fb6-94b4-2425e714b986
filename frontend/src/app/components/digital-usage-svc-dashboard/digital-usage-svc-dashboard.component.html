<mat-card class="example-card">

    <mat-card-title class="example-card-title">
        <mat-icon class="material-icons md-24" color="primary">leaderboard</mat-icon>
        API Digital Usage Dashboard
    </mat-card-title>

    <mat-divider></mat-divider>

    <mat-card-content class="example-card-content">

        @if (isChartsLoading) { 
            <mat-spinner diameter="40"></mat-spinner>Mouse running in a wheel...
        } 
        @else {

            @for (svcUsageChartOption of svcUsageChartOptions; track svcUsageChartOption) {

                <mat-card class="example-card">

                    <mat-card-title class="example-card-title" style="background-color: whitesmoke;">
                        {{svcUsageChartOption.title?.text}}
                    </mat-card-title>

                    <mat-card-content class="example-card-content">

                        <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start">

                            <mat-tab label="Chart">

                                <ag-charts class="digital-usage-chart"
                                [options]="svcUsageChartOption"
                                ></ag-charts>
                            </mat-tab>

                            <mat-tab label="Data">
                
                                <ag-grid-angular class="digital-usage-grid"
                                [rowData]="svcUsageChartOption.data"
                                [columnDefs]="usageGridColDefs"
                                [defaultColDef]="usageGridDefaultColDef"
                                />
                
                            </mat-tab>

                        </mat-tab-group>

                    </mat-card-content>

                </mat-card>
                <br>

            }
            
        }

    </mat-card-content>
    
</mat-card>
