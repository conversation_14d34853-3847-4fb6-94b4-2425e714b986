import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DigitalUsageSvcDashboardComponent } from './digital-usage-svc-dashboard.component';

describe('DigitalUsageSvcDashboardComponent', () => {
  let component: DigitalUsageSvcDashboardComponent;
  let fixture: ComponentFixture<DigitalUsageSvcDashboardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DigitalUsageSvcDashboardComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DigitalUsageSvcDashboardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
