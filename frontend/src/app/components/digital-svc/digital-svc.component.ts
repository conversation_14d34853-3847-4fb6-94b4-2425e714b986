import { Component, OnInit, ViewChild } from '@angular/core';

import { DigitalSvcListComponent } from '../digital-svc-list/digital-svc-list.component';
import { DigitalSvcFormComponent } from '../digital-svc-form/digital-svc-form.component';
import { DigitalSvcVersionListComponent } from '../digital-svc-version-list/digital-svc-version-list.component';
import { IDigitalSvcSkeleton } from '../../models/models';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';

import { SelectionChangedEvent } from 'ag-grid-community';

@Component({
  selector: 'app-digital-svc',
  imports: [
    DigitalSvcListComponent,
    DigitalSvcFormComponent,
    DigitalSvcVersionListComponent,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatTabsModule,
    MatSlideToggleModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
  ],
  templateUrl: './digital-svc.component.html',
  styleUrl: './digital-svc.component.scss'
})
export class DigitalSvcComponent implements OnInit {

  @ViewChild(DigitalSvcFormComponent)
  private DigitalSvcForm!: DigitalSvcFormComponent;

  // Selected Tab Index
  selectedTabIndex: number = 0
  selectedDetailTabIndex: number = 0

  selectedDigitalSvcSkeleton: IDigitalSvcSkeleton = {
    id: '',
    ServiceName: '', 
    APIName: ''
  }

  constructor(
    private snackBar: MatSnackBar, 
    public dialog: MatDialog) { }

  ngOnInit(): void {

  }

  // Event triggered when the DigitalSvc list selection has changed
  onSelectionChanged(event: SelectionChangedEvent) {

    let selectedRows = event.api.getSelectedRows()

    // If a row has been selected, then get the DigitalSvc and update the form
    if (selectedRows && selectedRows[0]) {

      this.selectedDigitalSvcSkeleton = {
        id: selectedRows[0].id,
        ServiceName: selectedRows[0].ServiceName,
        APIName: selectedRows[0].APIName
      }

    // Call the form to get and set the record
    this.DigitalSvcForm.getDigitalSvcRecord(selectedRows[0].id)

    // Move to the details tab
    this.selectedTabIndex = 1

    // Reset the detailed tab index to the first tab
    this.selectedDetailTabIndex = 0


    }
    // Else selection has been cleared, so reset the DigitalSvc & form
    else {

      this.selectedDigitalSvcSkeleton = {
        id: '',
        ServiceName: '',
        APIName: ''
      }

      this.DigitalSvcForm.clearDigitalSvcRecord()

    }

  }

  // When the user has clicked to create a new DigitalSvc record
  onCreateRecordClick() {

    this.DigitalSvcForm.clearDigitalSvcRecord()

    this.selectedTabIndex = 1

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }

}
