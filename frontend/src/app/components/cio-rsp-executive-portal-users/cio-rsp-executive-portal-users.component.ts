import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { AgChartsModule } from 'ag-charts-angular';

@Component({
  selector: 'app-cio-rsp-executive-portal-users',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTabsModule,
    AgChartsModule
  ],
  templateUrl: './cio-rsp-executive-portal-users.component.html',
  styleUrl: './cio-rsp-executive-portal-users.component.scss'
})
export class CioRspExecutivePortalUsersComponent implements OnInit {
  // Chart options
  options: any;

  // Selected tab index
  selectedTabIndex: number = 0;

  constructor() { }

  ngOnInit(): void {
    this.generateChart();
  }

  // Generate the chart
  generateChart(): void {
    console.log('Generating portal users chart');

    // Create placeholder data
    const placeholderData = [
      { Metric: 'Total Logins', Value: 0 },
      { Metric: 'Unique Logins', Value: 0 },
      { Metric: 'Active Users', Value: 0 }
    ];

    // Set chart options
    this.options = {
      title: {
        text: 'Service Portal Users - Data Coming Soon',
        fontSize: 18,
      },
      data: placeholderData,
      series: [
        {
          type: 'bar',
          xKey: 'Metric',
          yKey: 'Value',
          fill: '#0066cc'
        }
      ],
      container: {
        padding: {
          top: 20,
          right: 20,
          bottom: 20,
          left: 20
        }
      },
      autoSize: true,
      height: 500,
      width: '100%',
      axes: [
        {
          type: 'category',
          position: 'bottom',
          title: {
            text: 'Metric',
          },
        },
        {
          type: 'number',
          position: 'left',
          title: {
            text: 'Count',
          },
        },
      ],
      // Add a "Data Coming Soon" text overlay
      annotations: [
        {
          type: 'text',
          text: 'Data Coming Soon',
          fontSize: 24,
          color: 'gray',
          position: {
            x: 0.5,
            y: 0.5
          },
          alignment: {
            horizontal: 'center',
            vertical: 'middle'
          }
        }
      ]
    };

    console.log('Portal users chart options set:', this.options);
  }
}
