<mat-card class="example-card">

    <mat-card-title class="example-card-title">
        <mat-icon class="material-icons md-24" color="primary">cable</mat-icon>
        Access Seekers
    </mat-card-title>
    
    <mat-divider></mat-divider>

    <mat-card-content class="example-card-content">

        <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" [(selectedIndex)]="selectedTabIndex">

            <mat-tab label="List View">

                <app-access-seeker-list 
                    [showCreateButton]="true"
                    (createRecordClicked)="onCreateRecordClick()"
                    (selectionChanged)="onSelectionChanged($event)">
                </app-access-seeker-list>

            </mat-tab>

            <mat-tab label="Detail View">

                <app-access-seeker-form>
                </app-access-seeker-form>

                @if (selectedAccessSeekerSkeleton.id) {
                    <mat-card class="example-card">

                        <mat-card-title class="example-card-title">
                            <mat-icon class="material-icons md-24" color="primary">cable</mat-icon>
                            Related Records
                        </mat-card-title>

                        <mat-card-content class="example-card-content">

                            <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" [(selectedIndex)]="selectedDetailTabIndex">

                                <mat-tab label="Notes">

                                    <app-note-list
                                        [showCreateButton]="true"
                                        [parentAccessSeekerSkeleton]="selectedAccessSeekerSkeleton"
                                    ></app-note-list>

                                </mat-tab>

                                <mat-tab label="Contacts">

                                    <app-contact-list
                                        [showCreateButton]="true"
                                        [parentAccessSeekerSkeleton]="selectedAccessSeekerSkeleton"
                                    ></app-contact-list>
                            
                                </mat-tab>

                                <mat-tab label="Tasks">

                                    <app-task-list
                                        [showCreateButton]="true"
                                        [parentAccessSeekerSkeleton]="selectedAccessSeekerSkeleton"
                                    ></app-task-list>

                                </mat-tab>

                                <mat-tab label="Projects">

                                    <app-project-list
                                        [showCreateButton]="true"
                                        [parentAccessSeekerSkeleton]="selectedAccessSeekerSkeleton"
                                    ></app-project-list>

                                </mat-tab>

                                <mat-tab label="Certified APIs">
                                </mat-tab>

                                <mat-tab label="Digital Usage">
                                </mat-tab>

                            </mat-tab-group>

                        </mat-card-content>

                    </mat-card>
                }

            </mat-tab>  

        </mat-tab-group>

    </mat-card-content>
    
</mat-card>
