import { Component, OnInit, ViewChild } from '@angular/core';

import { AccessSeekerListComponent } from '../access-seeker-list/access-seeker-list.component';
import { AccessSeekerFormComponent } from '../access-seeker-form/access-seeker-form.component';
import { NoteListComponent } from '../note-list/note-list.component';
import { ContactListComponent } from '../contact-list/contact-list.component';
import { TaskListComponent } from '../task-list/task-list.component';
import { ProjectListComponent } from '../project-list/project-list.component';
import { IAccessSeekerSkeleton } from '../../models/models';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';

import { SelectionChangedEvent } from 'ag-grid-community';


@Component({
  selector: 'app-access-seeker',
  imports: [
    AccessSeekerListComponent,
    AccessSeekerFormComponent,
    NoteListComponent,
    ContactListComponent,
    ProjectListComponent,
    TaskListComponent,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatTabsModule,
    MatSlideToggleModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
  ],
  templateUrl: './access-seeker.component.html',
  styleUrl: './access-seeker.component.scss'
})
export class AccessSeekerComponent implements OnInit {

  @ViewChild(AccessSeekerFormComponent)
  private AccessSeekerForm!: AccessSeekerFormComponent;

  // Selected Tab Index
  selectedTabIndex: number = 0
  selectedDetailTabIndex: number = 0

  selectedAccessSeekerSkeleton: IAccessSeekerSkeleton = {
    id: '',
    AccessSeekerId: '' 
  }

  constructor(
    private snackBar: MatSnackBar, 
    public dialog: MatDialog) { }

  ngOnInit(): void {

  }

  // Event triggered when the Access Seeker list selection has changed
  onSelectionChanged(event: SelectionChangedEvent) {

    let selectedRows = event.api.getSelectedRows()

    // If a row has been selected, then get the Access Seeker and update the form
    if (selectedRows && selectedRows[0]) {

        this.selectedAccessSeekerSkeleton = {
          id: selectedRows[0].id,
          AccessSeekerId: selectedRows[0].AccessSeekerId
        }

      // Call the form to get and set the record
      this.AccessSeekerForm.getAccessSeekerRecord(selectedRows[0].id)

      // Move to the details tab
      this.selectedTabIndex = 1

      // Reset the detailed tab index to the first tab
      this.selectedDetailTabIndex = 0

    }
    // Else selection has been cleared, so reset the Access Seeker & form
    else {

      this.selectedAccessSeekerSkeleton = {
        id: '',
        AccessSeekerId: '' 
      }

      this.AccessSeekerForm.clearAccessSeekerRecord()

    }

  }

  // When the user has clicked to create a new Bus Entity records for the parent
  onCreateRecordClick() {

    this.AccessSeekerForm.clearAccessSeekerRecord()

    this.selectedTabIndex = 1

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }

}
