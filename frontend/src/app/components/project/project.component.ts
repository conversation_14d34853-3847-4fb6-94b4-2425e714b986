import { Component, OnInit, ViewChild } from '@angular/core';

import { ProjectListComponent } from '../project-list/project-list.component';
import { ProjectFormComponent } from '../project-form/project-form.component';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';

import { SelectionChangedEvent } from 'ag-grid-community';

@Component({
  selector: 'app-project',
  imports: [
    ProjectListComponent,
    ProjectFormComponent,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatTabsModule,
    MatSlideToggleModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
  ],
  templateUrl: './project.component.html',
  styleUrl: './project.component.scss'
})
export class ProjectComponent implements OnInit {

  @ViewChild(ProjectFormComponent)
  private ProjectForm!: ProjectFormComponent;

  // Selected Tab Index
  selectedTabIndex: number = 0

  constructor(
    private snackBar: MatSnackBar, 
    public dialog: MatDialog) { }

  ngOnInit(): void {

  }

  // Event triggered when the Project list selection has changed
  onSelectionChanged(event: SelectionChangedEvent) {

    let selectedRows = event.api.getSelectedRows()

    // If a row has been selected, then get the Project and update the form
    if (selectedRows && selectedRows[0]) {

      let selectedRowId = selectedRows[0].id

      // Call the form to get and set the record
      this.ProjectForm.getProjectRecord(selectedRowId)

      // Move to the details tab
      this.selectedTabIndex = 1


    }
    // Else selection has been cleared, so reset the Project & form
    else {

      this.ProjectForm.clearProjectRecord()

    }

  }

  // When the user has clicked to create a new Project record
  onCreateRecordClick() {

    this.ProjectForm.clearProjectRecord()

    this.selectedTabIndex = 1

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }

}
