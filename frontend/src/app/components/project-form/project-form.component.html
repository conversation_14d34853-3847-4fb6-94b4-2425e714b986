@if (isLoading) { 
<mat-spinner></mat-spinner> 
}

@else {

<mat-card class="example-card">

    <form [formGroup]="projectForm">

        <div class="form-row">

            <mat-form-field>
                <mat-label for="access-seeker-id">Access Seeker Id: </mat-label>
                <input matInput type="text" id="access-seeker-id" type="text" formControlName="AccessSeekerId" readonly="true">

                @if (this.allowAccessSeekerSelection) {
                <button 
                type="button" 
                mat-icon-button 
                matSuffix
                matTooltip="Pick an Acccess Seeker"
                (click)="OpenAccessSeekerListDialog()">
                <mat-icon matSuffix class="material-icons md-18">chevron_right</mat-icon>
                </button>
                }

            </mat-form-field>

            <mat-form-field>
                <mat-label for="status">Status:</mat-label>
                <mat-select id="status" formControlName="Status">
                    <mat-option value="New">New</mat-option>
                    <mat-option value="In Progress">In Progress</mat-option>
                    <mat-option value="On Hold">On Hold</mat-option>
                    <mat-option value="Cancelled">Cancelled</mat-option>
                    <mat-option value="Complete">Complete</mat-option>
                  </mat-select>
            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="name">Name:</mat-label>
                <input matInput id="name" type="text" formControlName="Name">
            </mat-form-field>

            <mat-form-field>
                <mat-label for="org">Org:</mat-label>
                <mat-select id="org" formControlName="Org">
                    <mat-option value="nbn">nbn</mat-option>
                    <mat-option value="RSP">RSP</mat-option>
                  </mat-select>
            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="category1">Category1:</mat-label>
                <mat-select id="category1" formControlName="Category1">
                    <mat-option value="Acquisition">Acquisition</mat-option>
                    <mat-option value="Retention">Retention</mat-option>
                    <mat-option value="High Speed Tiers">High Speed Tiers</mat-option>
                    <mat-option value="Supporting">Supporting</mat-option>
                  </mat-select>
            </mat-form-field>

            <mat-form-field>
                <mat-label for="category2">Category2:</mat-label>
                <input matInput id="category2" type="text" formControlName="Category2">
            </mat-form-field>

        </div>
        
        <div class="form-row">

            <mat-form-field>
                <mat-label for="planned-start-date">PlannedStartDate:</mat-label>
                <input matInput id="planned-start-date" [matDatepicker]="plannedstartpicker" formControlName="PlannedStartDate" readonly="true">
                <button type="button" mat-icon-button matSuffix matTooltip="Clear date" (click)="ClearDateFormField('PlannedStartDate')">
                    <mat-icon matSuffix class="material-icons md-24">clear</mat-icon>
                </button>
                <mat-datepicker-toggle matSuffix [for]="plannedstartpicker" matTooltip="Select date"></mat-datepicker-toggle>
                <mat-datepicker #plannedstartpicker></mat-datepicker>
            </mat-form-field>

            <mat-form-field>
                <mat-label for="actual-start-date">ActualStartDate:</mat-label>
                <input matInput id="actual-start-date" [matDatepicker]="actualstartpicker" formControlName="ActualStartDate" readonly="true">
                <button type="button" mat-icon-button matSuffix matTooltip="Clear date" (click)="ClearDateFormField('ActualStartDate')">
                    <mat-icon matSuffix class="material-icons md-24">clear</mat-icon>
                </button>
                <mat-datepicker-toggle matSuffix [for]="actualstartpicker" matTooltip="Select date"></mat-datepicker-toggle>
                <mat-datepicker #actualstartpicker></mat-datepicker>
            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="planned-end-date">PlannedEndDate:</mat-label>
                <input matInput id="planned-end-date" [matDatepicker]="plannedendpicker" formControlName="PlannedEndDate" readonly="true">
                <button type="button" mat-icon-button matSuffix matTooltip="Clear date" (click)="ClearDateFormField('PlannedEndDate')">
                    <mat-icon matSuffix class="material-icons md-24">clear</mat-icon>
                </button>
                <mat-datepicker-toggle matSuffix [for]="plannedendpicker" matTooltip="Select date"></mat-datepicker-toggle>
                <mat-datepicker #plannedendpicker></mat-datepicker>
            </mat-form-field>

            <mat-form-field>
                <mat-label for="actual-end-date">ActualEndDate:</mat-label>
                <input matInput id="actual-end-date" [matDatepicker]="actualendpicker" formControlName="ActualEndDate" readonly="true">
                <button type="button" mat-icon-button matSuffix matTooltip="Clear date" (click)="ClearDateFormField('ActualEndDate')">
                    <mat-icon matSuffix class="material-icons md-24">clear</mat-icon>
                </button>
                <mat-datepicker-toggle matSuffix [for]="actualendpicker" matTooltip="Select date"></mat-datepicker-toggle>
                <mat-datepicker #actualendpicker></mat-datepicker>
            </mat-form-field>

        </div>
        
        <mat-form-field>
            <mat-label for="description">Description:</mat-label>
            <textarea matInput id="description" formControlName="Description" rows="3"></textarea>
        </mat-form-field>

        <mat-form-field>
            <mat-label for="notes">Notes:</mat-label>
            <textarea matInput id="notes" formControlName="Notes" rows="3"></textarea>
        </mat-form-field>
    
        <mat-divider style="margin-top: 10px;"></mat-divider>

        <div style="display: flex; justify-content: center;"> 
            <button style="margin-right: 20px" mat-raised-button [disabled]="!Project" type="button" (click)="onDeleteClick()">Delete</button>
            <button style="margin-right: 20px" mat-raised-button [disabled]="projectForm.pristine" type="button" (click)="onCancelClick()">Cancel</button>
            <button mat-raised-button [disabled]="projectForm.pristine" type="submit" (click)="onSaveClick()">Submit</button>
        </div>

    </form>

</mat-card>

}