import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ProjectService } from '../../services/project.service';
import { IAccessSeeker, IProject, IProjectSkeleton, IYNEnum } from '../../models/models';

import { AccessSeekerListDialogComponent } from '../access-seeker-list-dialog/access-seeker-list-dialog.component';

import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

import { ReactiveFormsModule, Validators } from '@angular/forms';
import { FormGroup, FormControl } from '@angular/forms';
import { YesNoDialogComponent } from '../yes-no-dialog/yes-no-dialog.component';

import { MatNativeDateModule } from '@angular/material/core';
import { DateAdapter, MAT_DATE_LOCALE } from '@angular/material/core';
import { CustomDateAdapter } from '../../classes/custom-date-adapter.class';

@Component({
  selector: 'app-project-form',
  imports: [
    MatCardModule,
    MatDividerModule,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    ReactiveFormsModule,
    MatNativeDateModule
  ],
  providers: [
    { provide: DateAdapter, useClass: CustomDateAdapter },
    { provide: MAT_DATE_LOCALE, useValue: 'en-AU' } // Optional: Set the locale to en-AU
  ],
  templateUrl: './project-form.component.html',
  styleUrl: './project-form.component.scss'
})
export class ProjectFormComponent implements OnInit {

  // Input Project Skeleton (Optional)
  @Input() projectSkeleton: IProjectSkeleton | null = null 

  // Output when a new Project has been created
  @Output() projectCreated: EventEmitter<IProject> = new EventEmitter();

  // Output when a Project has been updated
  @Output() projectUpdated: EventEmitter<IProject> = new EventEmitter();

  // Current Project record
  Project: IProject | null = null

  // Form Group for Project
  projectForm = new FormGroup({
    AccessSeekerRecordId: new FormControl('', Validators.required),
    AccessSeekerId: new FormControl('', Validators.required),
    Name: new FormControl(''),
    Org: new FormControl(''),
    Category1: new FormControl(''),
    Category2: new FormControl(''),
    Status: new FormControl(''),
    PlannedStartDate: new FormControl<Date | null>(null),
    ActualStartDate: new FormControl<Date | null>(null),
    PlannedEndDate: new FormControl<Date | null>(null),
    ActualEndDate: new FormControl<Date | null>(null),
    Description: new FormControl(''),
    Notes: new FormControl(''),
  });

  allowAccessSeekerSelection: boolean = true

  isLoading: boolean = false

  constructor(
    private snackBar: MatSnackBar, 
    private projectService: ProjectService,
    public dialog: MatDialog) { }

  ngOnInit(): void {

    // If a Project skeleton was passed in with an id, then it must be an existing record (so get the record)
    if (this.projectSkeleton && this.projectSkeleton.id != '') {

      if (this.projectSkeleton.id) {
        this.getProjectRecord(this.projectSkeleton.id);
      }
    }

    // Else if a Project skeleton was passed in without an id, then it must be a create
    // set the form based on the skeleton
    else if (this.projectSkeleton && this.projectSkeleton.id == '') {
      this.Project = this.projectSkeleton
      this.resetProjectForm()

      // Prevent the user from selecting an Access Seeker
      // as it has been provided in the skeleton
      this.allowAccessSeekerSelection = false

    }

  }

  onSaveClick() {

    if (this.projectForm.valid) {

      // If Project exists, then update the record
      if (this.Project?.id) {
        this.updateProjectRecord()
      }

      // Else we are creating a new Project
      else {

        // If the Access Seeker selection is not allowed, then mark the fields as dirty
        // This is needed to ensure that these fields are sent to the back-end on a create.
        if (this.allowAccessSeekerSelection == false) {
          this.projectForm.controls.AccessSeekerRecordId.markAsDirty()
          this.projectForm.controls.AccessSeekerId.markAsDirty()
        }

        this.createProjectRecord()
      }

    } 
    
    else {
      this.showMessage("Please fix errors before resubmitting")
    }

  }

  onCancelClick() {

    // Reset the Project form
    this.resetProjectForm()

  }

  onDeleteClick() {

    // Open a dialog to confirm the delete action
    const dialogRef = this.dialog.open(YesNoDialogComponent, {
      width: '600px',
      data: {
        Title: "Confirm Delete",
        Subtitle: "Confirm you want to process with deleting record",
        Message: "Are you sure you want to delete this record?",
        IconName: "delete"
      },
    });

    dialogRef.afterClosed().subscribe(result => {

      // If user confirms action, then proceed to delete record
      if (result == IYNEnum.Y) {

        //            this.deleteProjectRecord()
      }

      else {
        this.showMessage("User cancelled delete")
      }

    });

  }

  // Method to get the Project record from the back-end and set the form
  public getProjectRecord(projectRecordId: string) {

    this.isLoading = true

    this.projectService.getProjectRecord(projectRecordId)
      .subscribe({
        next: (data) => {
          
          // Update the Project with the new values
          this.Project = data.data
          this.resetProjectForm()

          this.isLoading = false

        },
        error: () => {

          // Reset the Project record
          this.Project = null
          this.resetProjectForm()

          this.showMessage("Error getting Project record")

        }
      });


  }

  // Method to clear the current Project Record
  public clearProjectRecord() {

    this.Project = null
    this.resetProjectForm()

  }


  // Method to create the Project Record on the back-end
  private createProjectRecord() {

    // Create the update payload
    let payload:IProject = this.getChangedFieldsFromForm()

    // Call the back-end to create the record
    this.projectService.createProjectRecord(payload)
      .subscribe({
        next: (data) => {
          
          // Create has been successful, so set the Project
          this.Project = data.data
          this.resetProjectForm()

          this.showMessage("Create Successful")

          // Tell the world that a new Project has been successfully created
          this.projectCreated.emit(this.Project!)

        },
        error: (error) => {

          // If specific error message returned, then show specific message otherwise show a generic message
          if (error.error && error.error.errormsg) {
            this.showMessage("Error creating Project record. " + error.error.errormsg)
            console.error("Error creating Project record. " + error.error.errormsg)
          }

          else {
            this.showMessage("Error creating record")
          }
        }
      });

  }
  
  // Method to update the Project Record on the back-end
  private updateProjectRecord() {

    var payload: IProject
    var recordId: string

    // Get the id of the record to update
    recordId = this.Project?.id || ''

    // Create the update payload
    payload = this.getChangedFieldsFromForm()

    // Call the back-end to update the record
    this.projectService.updateProjectRecord(recordId, payload)
      .subscribe({
        next: (data) => {

          // Update has been successful, so update Project with the new values
          this.Project = data.data
          this.resetProjectForm()

          this.showMessage("Update Successful")

          // Tell the world that a Project has been successfully updated
          this.projectUpdated.emit(this.Project!)

        },
        error: (error) => {

          // If specific error message returned, then show specific message otherwise show a generic message
          if (error.error && error.error.errormsg) {
            this.showMessage("Error updating Project record. " + error.error.errormsg)
            console.error("Error updating Project record. " + error.error.errormsg)
          }

          else {
            this.showMessage("Error updating record")
          }
        }
      });

  }


  getChangedFieldsFromForm() {

    let tempProject:IProject = {}

    if (this.projectForm.controls.AccessSeekerRecordId.dirty) {
      tempProject.AccessSeekerRecordId = this.projectForm.controls.AccessSeekerRecordId.value!
    }
    if (this.projectForm.controls.AccessSeekerId.dirty) {
      tempProject.AccessSeekerId = this.projectForm.controls.AccessSeekerId.value!
    }
    if (this.projectForm.controls.Name.dirty) {
      tempProject.Name = this.projectForm.controls.Name.value!
    }
    if (this.projectForm.controls.Org.dirty) {
      tempProject.Org = this.projectForm.controls.Org.value!
    }
    if (this.projectForm.controls.Category1.dirty) {
      tempProject.Category1 = this.projectForm.controls.Category1.value!
    }
    if (this.projectForm.controls.Category2.dirty) {
      tempProject.Category2 = this.projectForm.controls.Category2.value!
    }
    if (this.projectForm.controls.Status.dirty) {
      tempProject.Status = this.projectForm.controls.Status.value!
    }
    if (this.projectForm.controls.PlannedStartDate.dirty) {
      tempProject.PlannedStartDate = this.dateToString(this.projectForm.controls.PlannedStartDate.value)
    }
    if (this.projectForm.controls.ActualStartDate.dirty) {
      tempProject.ActualStartDate = this.dateToString(this.projectForm.controls.ActualStartDate.value)
    }
    if (this.projectForm.controls.PlannedEndDate.dirty) {
      tempProject.PlannedEndDate = this.dateToString(this.projectForm.controls.PlannedEndDate.value)
    }
    if (this.projectForm.controls.ActualEndDate.dirty) {
      tempProject.ActualEndDate = this.dateToString(this.projectForm.controls.ActualEndDate.value)
    }
    if (this.projectForm.controls.Description.dirty) {
      tempProject.Description = this.projectForm.controls.Description.value!
    }
    if (this.projectForm.controls.Notes.dirty) {
      tempProject.Notes = this.projectForm.controls.Notes.value!
    }
    
    return tempProject

  }


  // Method to reset the Project Form to the values of the current Project record
  resetProjectForm() {

    // If Project record exists, then set the form values to the record values
    if (this.Project) {

      this.projectForm.reset({
        AccessSeekerRecordId: this.Project?.AccessSeekerRecordId ?? '',
        AccessSeekerId: this.Project?.AccessSeekerId ?? '',
        Name: this.Project?.Name ?? '',
        Org: this.Project?.Org ?? '',
        Category1: this.Project?.Category1 ?? '',
        Category2: this.Project?.Category2 ?? '',
        Status: this.Project?.Status ?? '',
        PlannedStartDate: this.Project?.PlannedStartDate ? new Date(this.Project.PlannedStartDate) : null,
        ActualStartDate: this.Project?.ActualStartDate ? new Date(this.Project.ActualStartDate) : null,
        PlannedEndDate: this.Project?.PlannedEndDate ? new Date(this.Project.PlannedEndDate) : null,
        ActualEndDate: this.Project?.ActualEndDate ? new Date(this.Project.ActualEndDate) : null,
        Description: this.Project?.Description ?? '',
        Notes: this.Project?.Notes ?? '',
      });

    }
    // Else reset the form to empty values
    else {

      this.projectForm.reset()

    }

  }

  OpenAccessSeekerListDialog() {
    
    // Open the Access Seeker List Dialog
    const dialogRef = this.dialog.open(AccessSeekerListDialogComponent, {
      width: '60%',
      height: 'fit',
      data: {},
    });

    // When the dialog is closed, get the selected Access Seeker
    dialogRef.afterClosed().subscribe((result: IAccessSeeker) => {

      // If a result was returned, then set the Access Seeker
      if (result) {

        // Set the Access Seeker Record Id in the form field
        this.projectForm.controls.AccessSeekerRecordId.setValue(result.id!);
        this.projectForm.controls.AccessSeekerRecordId.markAsDirty();

        // Set the Access Seeker Id in the form field
        this.projectForm.controls.AccessSeekerId.setValue(result.AccessSeekerId!);
        this.projectForm.controls.AccessSeekerId.markAsDirty();

      }

    });
  }

  dateToString(date: Date | null): string {

    var dateString:string = ''

    // If Due Date is set, then set covert it to a string in the format YYYY-MM-DD
    // This ensures that the date is not changed due to timezone differences
    // and can be stored in the back-end in a consistent format
    if (date) {

      dateString = date.getFullYear() + '-' + ("0" + (date.getMonth() + 1)).slice(-2) + '-' + ("0" + date.getDate()).slice(-2)
    }
    // Else set the Due Date to an empty string
    else {

      dateString = ''
    }

    return dateString

  }

  // Method to clear a date form field
  ClearDateFormField(formFieldControlName: string) {
    this.projectForm.get(formFieldControlName)?.setValue(null);
    this.projectForm.get(formFieldControlName)?.markAsDirty();
  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}

