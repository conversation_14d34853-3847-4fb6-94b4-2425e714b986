<div style="display:flex; justify-content: space-between; align-items: center; padding: 4px">

    <!-- Slider to autosize columns -->
    <mat-slide-toggle (change)="onToggleAutoSizeColumns($event)">
        Autosize Columns
    </mat-slide-toggle>

    <!-- Grid Buttons -->
    <div style="display:flex; padding-right: 25px ">

        <!-- Refresh Records Button -->
        <button 
            style="margin: 10px" 
            mat-raised-button (click)="RefreshGridRecords()" 
            matTooltip="Refresh records">
            <mat-icon style="margin: 0px;">refresh</mat-icon>
        </button>

        <!-- Add Button -->
         @if (showCreateButton) {
        <button 
            style="margin: 10px" 
            mat-raised-button (click)="onCreateRecordClick()"
            matTooltip="Create Digital Service record">
            <mat-icon style="margin: 0px">add</mat-icon>
        </button>
        }

    </div>
</div>

<ag-grid-angular 
style="height: 500px;" 
class="ag-theme-alpine" 
[gridOptions]="gridOptions"
(selectionChanged)="onSelectionChanged($event)"
(gridReady)="onGridReady($event)"
>
</ag-grid-angular>