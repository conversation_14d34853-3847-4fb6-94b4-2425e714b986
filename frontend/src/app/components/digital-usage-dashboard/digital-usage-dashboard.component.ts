import { Component, OnInit } from '@angular/core';

import { DigitalUsageChartService } from '../../services/digital-usage-charts.service';
import { IDigitalUsageHistory } from '../../models/models';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule, MatSelectChange } from '@angular/material/select';
import { MatSnackBar } from '@angular/material/snack-bar';

import { AgCharts } from "ag-charts-angular";

import {
  AgBarSeriesOptions,
  AgCategoryAxisOptions,
  AgChartCaptionOptions,
  AgChartLegendOptions,
  AgChartOptions,
  AgChartSubtitleOptions,
  AgLineSeriesOptions,
  AgNumberAxisOptions,
} from "ag-charts-community";

@Component({
  selector: 'app-digital-usage-dashboard',
  imports: [
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatFormFieldModule,
    MatSelectModule,
    AgCharts
  ],
  templateUrl: './digital-usage-dashboard.component.html',
  styleUrl: './digital-usage-dashboard.component.scss'
})
export class DigitalUsageDashboardComponent implements OnInit {

  public selectedPeriod:string ='Mar-25'

  public digitalUsageHistoryChartOptions!: AgChartOptions;
  public digitalUsageByRSPChartOptions!: AgChartOptions;
  public digitalUsageBySvcChartOptions!: AgChartOptions;

  constructor(
    private snackBar: MatSnackBar, 
    private digitalUsageChartService: DigitalUsageChartService) { }

  
  ngOnInit(): void {

    // Digital Usage History
    this.initDigitalUsageHistoryChart();
    this.getDigitalUsageHistory();

    // Digital Usage By RSP
    this.initDigitalUsageByRSPChart();
    this.getDigitalUsageByRSP();

    // Digital Usage By Service
    this.initDigitalUsageBySvcChart();
    this.getDigitalUsageBySvc();

  }


  onChangePeriod(event: MatSelectChange) {
    this.getDigitalUsageByRSP()
    this.getDigitalUsageBySvc()
  }

  // Method to initialize the Digital Usage History Chart
  private initDigitalUsageHistoryChart() {

    this.digitalUsageHistoryChartOptions = {
      // Chart Title
      title: { text: "RSP Digital Usage History" } as AgChartCaptionOptions,
      // Chart Subtitle
      subtitle: { text: "Put Subtitle Here" } as AgChartSubtitleOptions,
      // Data: Data to be displayed within the chart
      data: [],
      // Series: Defines which chart type and data to use
      series: [
        {
          type: "line",
          xKey: "Period",
          yKey: "TotalAPITxns",
          yName: "Total API Txns",
        } as AgLineSeriesOptions,
        {
          type: "line",
          xKey: "Period",
          yKey: "TotalPortalTxns",
          yName: "Total Portal Txns",
        } as AgLineSeriesOptions,
        {
          type: "line",
          xKey: "Period",
          yKey: "TotalTxns",
          yName: "Total Txns",
        } as AgLineSeriesOptions,
      ],
      // Axes: Configure the axes for the chart
      axes: [
        // Display category (xKey) as the bottom axis
        {
          type: "category",
          position: "bottom",
        } as AgCategoryAxisOptions,
        // Use left axis for 'iceCreamSales' series
        {
          type: "number",
          position: "left",
          keys: ["TotalAPITxns", "TotalPortalTxns", "TotalTxns"],
          // Format the label applied to this axis
          label: {
            formatter: (params) => {
              return parseFloat(params.value).toLocaleString();
            },
          },
        } as AgNumberAxisOptions,
      ],
      // Legend: Matches visual elements to their corresponding series or data categories.
      legend: {
        position: "right",
      } as AgChartLegendOptions,
    };

  }


  // Method to get the Digital Usage History data
  private getDigitalUsageHistory() {

    // Call the back-end to update the record
    this.digitalUsageChartService.getDigitalUsageHistory()
      .subscribe({
        next: (data) => {
          
          // Update the chart with the latest data
          let tempChartOption = {...this.digitalUsageHistoryChartOptions}
          tempChartOption.data = <IDigitalUsageHistory[]>data.data;
          this.digitalUsageHistoryChartOptions = tempChartOption;


        },
        error: (error) => {
            this.showMessage("Error getting data")
        }
      });

  }



  // Method to initialize the Digital Usage By RSP Chart
  private initDigitalUsageByRSPChart() {

    this.digitalUsageByRSPChartOptions = {
      // Chart Title
      title: { text: "RSP Digital Usage By RSP" } as AgChartCaptionOptions,
      // Chart Subtitle
      subtitle: { text: "Period: " + this.selectedPeriod } as AgChartSubtitleOptions,
      // Data: Data to be displayed within the chart
      data: [],
      // Series: Defines which chart type and data to use
      series: [
        {
          type: "bar",
          xKey: "Name",
          yKey: "TotalAPITxns",
          yName: "TotalAPITxns",
          stacked: true,
        },
        {
          type: "bar",
          xKey: "Name",
          yKey: "TotalPortalTxns",
          yName: "TotalPortalTxns",
          stacked: true,
        },
      ],
      // Axes: Configure the axes for the chart
      axes: [
        // Display category (xKey) as the bottom axis
        {
          type: "category",
          position: "bottom",
        } as AgCategoryAxisOptions,
        // Use left axis for 'digital txn' series
        {
          type: "number",
          position: "left",
          keys: ["TotalAPITxns", "TotalPortalTxns"],
          // Format the label applied to this axis
          label: {
            formatter: (params) => {
              return parseFloat(params.value).toLocaleString();
            },
          },
        } as AgNumberAxisOptions,
      ],
      // Legend: Matches visual elements to their corresponding series or data categories.
      legend: {
        position: "right",
      } as AgChartLegendOptions,
    };

  }

  // Method to get the Digital Usage By RSP data
  private getDigitalUsageByRSP() {

    // Call the back-end to update the record
    this.digitalUsageChartService.getDigitalUsageByRSP(this.selectedPeriod)
      .subscribe({
        next: (data) => {
          
          // Update the chart with the latest data
          let tempChartOption = {...this.digitalUsageByRSPChartOptions}
          tempChartOption.data = data.data;
          tempChartOption.subtitle = { text: "Period: " + this.selectedPeriod }
          this.digitalUsageByRSPChartOptions = tempChartOption;


        },
        error: (error) => {
            this.showMessage("Error getting data")
        }
      });

  }


  // Method to initialize the Digital Usage By Service Chart
  private initDigitalUsageBySvcChart() {

    this.digitalUsageBySvcChartOptions = {
      // Chart Title
      title: { text: "RSP Digital Usage By Service" } as AgChartCaptionOptions,
      // Chart Subtitle
      subtitle: { text: "Period: " + this.selectedPeriod } as AgChartSubtitleOptions,
      // Data: Data to be displayed within the chart
      data: [],
      // Series: Defines which chart type and data to use
      series: [
        {
          type: "bar",
          xKey: "ServiceName",
          yKey: "TotalAPITxns",
          yName: "TotalAPITxns",
          stacked: true,
        },
        {
          type: "bar",
          xKey: "ServiceName",
          yKey: "TotalPortalTxns",
          yName: "TotalPortalTxns",
          stacked: true,
        },
      ],
      // Axes: Configure the axes for the chart
      axes: [
        // Display category (xKey) as the bottom axis
        {
          type: "category",
          position: "bottom",
        } as AgCategoryAxisOptions,
        // Use left axis for 'digital txn' series
        {
          type: "number",
          position: "left",
          keys: ["TotalAPITxns", "TotalPortalTxns"],
          // Format the label applied to this axis
          label: {
            formatter: (params) => {
              return parseFloat(params.value).toLocaleString();
            },
          },
        } as AgNumberAxisOptions,
      ],
      // Legend: Matches visual elements to their corresponding series or data categories.
      legend: {
        position: "right",
      } as AgChartLegendOptions,
    };

  }

  // Method to get the Digital Usage By Svc data
  private getDigitalUsageBySvc() {

    // Call the back-end to update the record
    this.digitalUsageChartService.getDigitalUsageBySvc(this.selectedPeriod)
      .subscribe({
        next: (data) => {
          
          // Update the chart with the latest data
          let tempChartOption = {...this.digitalUsageBySvcChartOptions}
          tempChartOption.data = data.data;
          tempChartOption.subtitle = { text: "Period: " + this.selectedPeriod }
          this.digitalUsageBySvcChartOptions = tempChartOption;


        },
        error: (error) => {
            this.showMessage("Error getting data")
        }
      });

  }


  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
