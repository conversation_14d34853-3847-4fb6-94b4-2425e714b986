<mat-card class="example-card">

    <mat-card-title class="example-card-title">
        <mat-icon class="material-icons md-24" color="primary">leaderboard</mat-icon>
        Digital Usage Dashboard
    </mat-card-title>

    <mat-divider></mat-divider>

    <mat-card-content class="example-card-content">

        <div style = "display: flex; justify-content: flex-end;">

            <mat-form-field>
                <mat-label for="period">Period (for relevant charts)</mat-label>
                <mat-select id="period" [(value)]="selectedPeriod" (selectionChange)="onChangePeriod($event)">
                    <mat-option value="Mar-25">Mar-25</mat-option>
                    <mat-option value="Feb-25">Feb-25</mat-option>
                    <mat-option value="Jan-25">Jan-25</mat-option>
                    <mat-option value="Dec-24">Dec-24</mat-option>
                </mat-select>
            </mat-form-field>

        </div>

        <ag-charts class="digital-usage-chart"
        [options]="digitalUsageHistoryChartOptions"
        ></ag-charts>

        <ag-charts class="digital-usage-chart"
        [options]="digitalUsageByRSPChartOptions"
        ></ag-charts>

        <ag-charts class="digital-usage-chart"
        [options]="digitalUsageBySvcChartOptions"
        ></ag-charts>

    </mat-card-content>
    
</mat-card>
