import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';

@Component({
    selector: 'app-side-navbar',
    imports: [
        CommonModule,
        RouterModule,
        MatProgressSpinnerModule,
        MatExpansionModule,
        MatListModule,
        MatIconModule,
        MatDividerModule
    ],
    templateUrl: './side-navbar.component.html',
    styleUrls: ['./side-navbar.component.scss']
})
export class SideNavbarComponent implements OnInit {

    constructor(private snackBar: MatSnackBar) { }

    ngOnInit(): void {

    }

    // Method to show a message in the material snackbar
    showMessage(message: string) {
        this.snackBar.open(message, 'OK', {
            duration: 3000,
        });
    }

}
