<mat-nav-list class="side-navnav">
    <mat-accordion multi="false">

        <mat-nav-list>
            <mat-list-item routerLink="accessseeker" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact: true}">
                <mat-icon class="material-icons md-18">cable</mat-icon>Access Seekers
            </mat-list-item>
            <mat-list-item routerLink="note" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact: true}">
                <mat-icon class="material-icons md-18">note</mat-icon>Notes
            </mat-list-item>
            <mat-list-item routerLink="contact" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact: true}">
                <mat-icon class="material-icons md-18">contacts</mat-icon>Contacts
            </mat-list-item>
            <mat-list-item routerLink="task" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact: true}">
                <mat-icon class="material-icons md-18">task</mat-icon>Tasks
            </mat-list-item>
            <mat-list-item routerLink="project" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact: true}">
                <mat-icon class="material-icons md-18">construction</mat-icon>Projects
            </mat-list-item>
            <mat-list-item routerLink="digitalsvc" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact: true}">
                <mat-icon class="material-icons md-18">widgets</mat-icon>Digital Services
            </mat-list-item>
            <mat-list-item routerLink="digitalsvcversion" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact: true}">
                <mat-icon class="material-icons md-18">widgets</mat-icon>Digital Service Versions
            </mat-list-item>

            <mat-divider></mat-divider>
            <br>

            <mat-list-item routerLink="digitalusagedashboard" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact: true}">
                <mat-icon class="material-icons md-18">leaderboard</mat-icon>Digital Usage Dashboard
            </mat-list-item>
            <mat-list-item routerLink="digitalusagerspdashboard" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact: true}">
                <mat-icon class="material-icons md-18">leaderboard</mat-icon>RSP Digital Usage Dashboard
            </mat-list-item>
            <mat-list-item routerLink="digitalusagesvcdashboard" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact: true}">
                <mat-icon class="material-icons md-18">leaderboard</mat-icon>Service Digital Usage Dashboard
            </mat-list-item>
            <mat-list-item routerLink="cio-rsp-executive" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact: true}">
                <mat-icon class="material-icons md-18">analytics</mat-icon>CIO RSP Executive Monthly
            </mat-list-item>
            <mat-list-item routerLink="test" routerLinkActive="is-active" [routerLinkActiveOptions]="{exact: true}">
                <mat-icon class="material-icons md-18">star</mat-icon>Test
            </mat-list-item>
        </mat-nav-list>

    </mat-accordion>
</mat-nav-list>

