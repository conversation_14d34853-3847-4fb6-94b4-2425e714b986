import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { AgChartsModule } from 'ag-charts-angular';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';

import { CioRspExecutiveService } from '../../services/cio-rsp-executive.service';
import { IDigitalServiceUsage } from '../../models/models';

@Component({
  selector: 'app-cio-rsp-executive-service-usage',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    AgChartsModule,
    AgGridAngular
  ],
  templateUrl: './cio-rsp-executive-service-usage.component.html',
  styleUrl: './cio-rsp-executive-service-usage.component.scss'
})
export class CioRspExecutiveServiceUsageComponent implements OnInit, OnChanges {
  @Input() period: string = '';

  // Data for the chart
  serviceUsageData: IDigitalServiceUsage[] = [];

  // Chart options
  options: any;

  // AG Grid column definitions
  columnDefs: ColDef[] = [
    { field: 'ServiceName', headerName: 'Service Name', sortable: true, filter: true },
    {
      field: 'TotalAPITxns',
      headerName: 'API Transactions',
      sortable: true,
      filter: true,
      valueFormatter: params => params.value ? params.value.toLocaleString() : '0'
    },
    {
      field: 'TotalPortalTxns',
      headerName: 'Portal Transactions',
      sortable: true,
      filter: true,
      valueFormatter: params => params.value ? params.value.toLocaleString() : '0'
    },
    {
      field: 'APIPercentage',
      headerName: 'API %',
      sortable: true,
      filter: true,
      valueFormatter: params => params.value ? `${params.value.toFixed(1)}%` : '0%'
    }
  ];

  // AG Grid default column definitions
  defaultColDef: ColDef = {
    flex: 1,
    minWidth: 100,
    resizable: true
  };

  // Selected tab index
  selectedTabIndex: number = 0;

  constructor(private cioRspExecutiveService: CioRspExecutiveService) { }

  ngOnInit(): void {
    if (this.period) {
      this.loadServiceUsageData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['period'] && !changes['period'].firstChange) {
      this.loadServiceUsageData();
    }
  }

  // Load service usage data
  loadServiceUsageData(): void {
    this.cioRspExecutiveService.getDigitalServiceUsage(this.period).subscribe(
      (data) => {
        this.serviceUsageData = data;
        this.generateChart();
      },
      (error) => {
        console.error('Error loading service usage data:', error);
      }
    );
  }

  // Generate the chart
  generateChart(): void {
    console.log('Generating service usage chart with data:', this.serviceUsageData);

    if (!this.serviceUsageData || this.serviceUsageData.length === 0) {
      console.warn('No service usage data available for chart');
      return;
    }

    // Sort data by total transactions
    const sortedData = [...this.serviceUsageData].sort((a, b) =>
      (b.TotalAPITxns + b.TotalPortalTxns) - (a.TotalAPITxns + a.TotalPortalTxns)
    );

    // Create series data with minimal configuration
    this.options = {
      data: sortedData,
      series: [
        {
          type: 'bar',
          xKey: 'ServiceName',
          yKey: 'TotalAPITxns',
          yName: 'API Transactions',
          stacked: true,
        },
        {
          type: 'bar',
          xKey: 'ServiceName',
          yKey: 'TotalPortalTxns',
          yName: 'Portal Transactions',
          stacked: true,
        }
      ],
      legend: {
        position: 'bottom',
      },
      axes: [
        {
          type: 'category',
          position: 'bottom',
        },
        {
          type: 'number',
          position: 'left',
        },
      ],
    };

    console.log('Service usage chart options set:', this.options);
  }
}
