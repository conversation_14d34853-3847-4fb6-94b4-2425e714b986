import { Component, OnInit, ViewChild } from '@angular/core';

import { DigitalSvcVersionListComponent } from '../digital-svc-version-list/digital-svc-version-list.component';
import { DigitalSvcVersionFormComponent } from '../digital-svc-version-form/digital-svc-version-form.component';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';

import { SelectionChangedEvent } from 'ag-grid-community';

@Component({
  selector: 'app-digital-svc-version',
  imports: [
    DigitalSvcVersionListComponent,
    DigitalSvcVersionFormComponent,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatTabsModule,
    MatSlideToggleModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
  ],
  templateUrl: './digital-svc-version.component.html',
  styleUrl: './digital-svc-version.component.scss'
})
export class DigitalSvcVersionComponent implements OnInit {

  @ViewChild(DigitalSvcVersionFormComponent)
  private DigitalSvcVersionForm!: DigitalSvcVersionFormComponent;

  // Selected Tab Index
  selectedTabIndex: number = 0

  constructor(
    private snackBar: MatSnackBar, 
    public dialog: MatDialog) { }

  ngOnInit(): void {

  }

  // Event triggered when the DigitalSvcVersion list selection has changed
  onSelectionChanged(event: SelectionChangedEvent) {

    let selectedRows = event.api.getSelectedRows()

    // If a row has been selected, then get the DigitalSvcVersion and update the form
    if (selectedRows && selectedRows[0]) {

      let selectedRowId = selectedRows[0].id

      // Call the form to get and set the record
      this.DigitalSvcVersionForm.getDigitalSvcVersionRecord(selectedRowId)

      // Move to the details tab
      this.selectedTabIndex = 1


    }
    // Else selection has been cleared, so reset the DigitalSvcVersion & form
    else {

      this.DigitalSvcVersionForm.clearDigitalSvcVersionRecord()

    }

  }

  // When the user has clicked to create a new DigitalSvc record
  onCreateRecordClick() {

    this.DigitalSvcVersionForm.clearDigitalSvcVersionRecord()

    this.selectedTabIndex = 1

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }

}
