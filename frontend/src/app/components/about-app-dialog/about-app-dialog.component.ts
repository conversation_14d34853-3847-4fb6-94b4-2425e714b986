import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';

@Component({
    selector: 'app-about-app-dialog',
    imports: [
        MatCardModule,
        MatIconModule,
        MatDividerModule,
        MatButtonModule
    ],
    templateUrl: './about-app-dialog.component.html',
    styleUrls: ['./about-app-dialog.component.scss']
})
export class AboutAppDialogComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<AboutAppDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) { }

  ngOnInit(): void {
  }

  onOkClick(): void {
    this.dialogRef.close();
  }

}
