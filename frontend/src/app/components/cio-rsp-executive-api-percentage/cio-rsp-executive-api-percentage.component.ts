import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { AgChartsModule } from 'ag-charts-angular';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';

import { CioRspExecutiveService } from '../../services/cio-rsp-executive.service';
import { IRSPAPIPercentage } from '../../models/models';

@Component({
  selector: 'app-cio-rsp-executive-api-percentage',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    AgChartsModule,
    AgGridAngular
  ],
  templateUrl: './cio-rsp-executive-api-percentage.component.html',
  styleUrl: './cio-rsp-executive-api-percentage.component.scss'
})
export class CioRspExecutiveApiPercentageComponent implements OnInit, OnChanges {
  @Input() period: string = '';

  // Data for the chart
  apiPercentageData: IRSPAPIPercentage[] = [];

  // Chart options
  options: any;

  // AG Grid column definitions
  columnDefs: ColDef[] = [
    { field: 'DigitalVolRank', headerName: 'Digital Vol Rank', sortable: true, filter: true, width: 150 },
    { field: 'RSPName', headerName: 'RSP Name', sortable: true, filter: true },
    {
      field: 'APIPercentage',
      headerName: 'API %',
      sortable: true,
      filter: true,
      valueFormatter: params => params.value ? `${params.value.toFixed(1)}%` : '0%'
    },
    { field: 'ServiceCountRank', headerName: 'Service Count Rank', sortable: true, filter: true, width: 180 }
  ];

  // AG Grid default column definitions
  defaultColDef: ColDef = {
    flex: 1,
    minWidth: 100,
    resizable: true
  };

  // Selected tab index
  selectedTabIndex: number = 0;

  constructor(private cioRspExecutiveService: CioRspExecutiveService) { }

  ngOnInit(): void {
    if (this.period) {
      this.loadAPIPercentageData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['period'] && !changes['period'].firstChange) {
      this.loadAPIPercentageData();
    }
  }

  // Load API percentage data
  loadAPIPercentageData(): void {
    this.cioRspExecutiveService.getRSPAPIPercentage(this.period).subscribe(
      (data) => {
        this.apiPercentageData = data;
        this.generateChart();
      },
      (error) => {
        console.error('Error loading API percentage data:', error);
      }
    );
  }

  // Generate the chart
  generateChart(): void {
    console.log('Generating API percentage chart with data:', this.apiPercentageData);

    if (!this.apiPercentageData || this.apiPercentageData.length === 0) {
      console.warn('No API percentage data available for chart');
      return;
    }

    // Sort data by digital volume rank
    const sortedData = [...this.apiPercentageData].sort((a, b) => a.DigitalVolRank - b.DigitalVolRank);

    // Create series data with minimal configuration
    this.options = {
      data: sortedData,
      series: [
        {
          type: 'bar',
          xKey: 'RSPName',
          yKey: 'APIPercentage',
        }
      ],
      axes: [
        {
          type: 'category',
          position: 'bottom',
        },
        {
          type: 'number',
          position: 'left',
          min: 0,
          max: 100,
        },
      ],
    };

    console.log('API percentage chart options set:', this.options);
  }
}
