@if (isLoading) { 
    <mat-spinner></mat-spinner> 
    }
    
    @else {
    
    <mat-card class="example-card">
    
        <form [formGroup]="digitalSvcForm">
    
            <div class="form-row">
    
                <mat-form-field>
                    <mat-label for="service-code">Service Code: </mat-label>
                    <input matInput type="text" id="service-code" type="text" formControlName="ServiceCode">
                </mat-form-field>

                <mat-form-field>
                    <mat-label for="service-name">Service Name: </mat-label>
                    <input matInput type="text" id="service-name" type="text" formControlName="ServiceName">
                </mat-form-field>
    
            </div>
    
            <div class="form-row">
    
                <mat-form-field>
                    <mat-label for="api-name">API Name:</mat-label>
                    <input matInput id="api-name" type="text" formControlName="APIName">
                </mat-form-field>
    
                <mat-form-field>
                    <mat-label for="status">Status:</mat-label>
                    <mat-select id="status" formControlName="Status">
                        <mat-option value="Planned">Planned</mat-option>
                        <mat-option value="Active">Active</mat-option>
                        <mat-option value="Retired">Retired</mat-option>
                      </mat-select>
                </mat-form-field>
    
            </div>

            <div class="form-row">
    
                <mat-form-field>
                    <mat-label for="used-for-connect">UsedForConnect:</mat-label>
                    <mat-select id="used-for-connect" formControlName="UsedForConnect">
                        <mat-option value="Y">Y</mat-option>
                        <mat-option value="N">N</mat-option>
                      </mat-select>
                </mat-form-field>

                <mat-form-field>
                    <mat-label for="used-for-assure">UsedForAssure:</mat-label>
                    <mat-select id="used-for-assure" formControlName="UsedForAssure">
                        <mat-option value="Y">Y</mat-option>
                        <mat-option value="N">N</mat-option>
                      </mat-select>
                </mat-form-field>

                <mat-form-field>
                    <mat-label for="used-for-billing">UsedForBilling:</mat-label>
                    <mat-select id="used-for-billing" formControlName="UsedForBilling">
                        <mat-option value="Y">Y</mat-option>
                        <mat-option value="N">N</mat-option>
                      </mat-select>
                </mat-form-field>

                <mat-form-field>
                    <mat-label for="used-for-other">UsedForOther:</mat-label>
                    <mat-select id="used-for-other" formControlName="UsedForOther">
                        <mat-option value="Y">Y</mat-option>
                        <mat-option value="N">N</mat-option>
                      </mat-select>
                </mat-form-field>
    
            </div>
    
            <div class="form-row">
    
                <mat-form-field>
                    <mat-label for="purpose">Purpose:</mat-label>
                    <input matInput id="purpose" type="text" formControlName="Purpose">
                </mat-form-field>
    
            </div>
    
            <div class="form-row">
    
                <mat-form-field>
                    <mat-label for="description">Description:</mat-label>
                    <textarea matInput id="description" formControlName="Description" rows="3"></textarea>
                </mat-form-field>
    
            </div>

            <div class="form-row">
    
                <mat-form-field>
                    <mat-label for="notes">Notes:</mat-label>
                    <textarea matInput id="notes" formControlName="Notes" rows="3"></textarea>
                </mat-form-field>
    
            </div>

    
            <mat-divider style="margin-top: 10px;"></mat-divider>
    
            <div style="display: flex; justify-content: center;"> 
                <button style="margin-right: 20px" mat-raised-button [disabled]="!DigitalSvc" type="button" (click)="onDeleteClick()">Delete</button>
                <button style="margin-right: 20px" mat-raised-button [disabled]="digitalSvcForm.pristine" type="button" (click)="onCancelClick()">Cancel</button>
                <button mat-raised-button [disabled]="digitalSvcForm.pristine" type="submit" (click)="onSaveClick()">Submit</button>
            </div>
    
        </form>
    
    </mat-card>
    
    }