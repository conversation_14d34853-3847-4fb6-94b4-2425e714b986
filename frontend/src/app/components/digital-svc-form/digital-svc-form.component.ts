import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { DigitalSvcService } from '../../services/digital-svc.service';
import { IDigitalSvc, IYNEnum } from '../../models/models';

import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { ReactiveFormsModule, Validators } from '@angular/forms';
import { FormGroup, FormControl } from '@angular/forms';
import { YesNoDialogComponent } from '../yes-no-dialog/yes-no-dialog.component';


@Component({
  selector: 'app-digital-svc-form',
  imports: [
    MatCardModule,
    MatDividerModule,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatInputModule,
    MatProgressSpinnerModule,
    ReactiveFormsModule
  ],
  templateUrl: './digital-svc-form.component.html',
  styleUrl: './digital-svc-form.component.scss'
})
export class DigitalSvcFormComponent implements OnInit {

  // Output when a new Digital Service has been created
  @Output() digitalSvcCreated: EventEmitter<IDigitalSvc> = new EventEmitter();

  // Output when a Digital Service has been updated
  @Output() digitalSvcUpdated: EventEmitter<IDigitalSvc> = new EventEmitter();

  // Current DigitalSvc record
  DigitalSvc: IDigitalSvc | null = null

  // Form Group for DigitalSvc
  digitalSvcForm = new FormGroup({
    ServiceCode: new FormControl('', Validators.required),
    ServiceName: new FormControl('', Validators.required),
    Status: new FormControl('', Validators.required),
    APIName: new FormControl(''),
    Purpose: new FormControl(''),
    Description: new FormControl(''),
    UsedForConnect: new FormControl('', Validators.required),
    UsedForAssure: new FormControl('', Validators.required),
    UsedForBilling: new FormControl('', Validators.required),
    UsedForOther: new FormControl('', Validators.required),
    Notes: new FormControl(''),
  });

  isLoading: boolean = false

  constructor(
    private snackBar: MatSnackBar, 
    private digitalSvcService: DigitalSvcService,
    public dialog: MatDialog) { }

  ngOnInit(): void {

  }

  onSaveClick() {

    if (this.digitalSvcForm.valid) {

      // If DigitalSvc exists, then update the record
      if (this.DigitalSvc?.id) {
        this.updateDigitalSvcRecord()
      }

      // Else we are creating a new DigitalSvc
      else {
        this.createDigitalSvcRecord()
      }

    } 
    
    else {
      this.showMessage("Please fix errors before resubmitting")
    }

  }

  onCancelClick() {

    // Reset the DigitalSvc form
    this.resetDigitalSvcForm()

  }

  onDeleteClick() {

    // Open a dialog to confirm the delete action
    const dialogRef = this.dialog.open(YesNoDialogComponent, {
      width: '600px',
      data: {
        Title: "Confirm Delete",
        Subtitle: "Confirm you want to process with deleting record",
        Message: "Are you sure you want to delete this record?",
        IconName: "delete"
      },
    });

    dialogRef.afterClosed().subscribe(result => {

      // If user confirms action, then proceed to delete record
      if (result == IYNEnum.Y) {

        //            this.deleteDigitalSvcRecord()
      }

      else {
        this.showMessage("User cancelled delete")
      }

    });

  }

  // Method to get the DigitalSvc record from the back-end and set the form
  public getDigitalSvcRecord(digitalSvcRecordId: string) {

    this.isLoading = true

    this.digitalSvcService.getDigitalSvcRecord(digitalSvcRecordId)
      .subscribe({
        next: (data) => {
          
          // Update the DigitalSvc with the new values
          this.DigitalSvc = data.data
          this.resetDigitalSvcForm()

          this.isLoading = false

        },
        error: () => {

          // Reset the DigitalSvc record
          this.DigitalSvc = null
          this.resetDigitalSvcForm()

          this.showMessage("Error getting DigitalSvc record")

        }
      });


  }

  // Method to clear the current DigitalSvc Record
  public clearDigitalSvcRecord() {

    this.DigitalSvc = null
    this.resetDigitalSvcForm()

  }


  // Method to create the DigitalSvc Record on the back-end
  private createDigitalSvcRecord() {

    // Create the update payload
    let payload:IDigitalSvc = this.getChangedFieldsFromForm()

    // Call the back-end to create the record
    this.digitalSvcService.createDigitalSvcRecord(payload)
      .subscribe({
        next: (data) => {
          
          // Create has been successful, so set the DigitalSvc
          this.DigitalSvc = data.data
          this.resetDigitalSvcForm()

          this.showMessage("Create Successful")

          // Tell the world that a new DigitalSvc has been successfully created
          this.digitalSvcCreated.emit(this.DigitalSvc!)

        },
        error: (error) => {

          // If specific error message returned, then show specific message otherwise show a generic message
          if (error.error && error.error.errormsg) {
            this.showMessage("Error creating DigitalSvc record. " + error.error.errormsg)
            console.error("Error creating DigitalSvc record. " + error.error.errormsg)
          }

          else {
            this.showMessage("Error creating record")
          }
        }
      });

  }
  
  // Method to update the DigitalSvc Record on the back-end
  private updateDigitalSvcRecord() {

    var payload: IDigitalSvc
    var recordId: string

    // Get the id of the record to update
    recordId = this.DigitalSvc?.id || ''

    // Create the update payload
    payload = this.getChangedFieldsFromForm()

    // Call the back-end to update the record
    this.digitalSvcService.updateDigitalSvcRecord(recordId, payload)
      .subscribe({
        next: (data) => {

          // Update has been successful, so update DigitalSvc with the new values
          this.DigitalSvc = data.data
          this.resetDigitalSvcForm()

          this.showMessage("Update Successful")

          // Tell the world that a DigitalSvc has been successfully updated
          this.digitalSvcUpdated.emit(this.DigitalSvc!)

        },
        error: (error) => {

          // If specific error message returned, then show specific message otherwise show a generic message
          if (error.error && error.error.errormsg) {
            this.showMessage("Error updating DigitalSvc record. " + error.error.errormsg)
            console.error("Error updating DigitalSvc record. " + error.error.errormsg)
          }

          else {
            this.showMessage("Error updating record")
          }
        }
      });

  }


  getChangedFieldsFromForm() {

    let tempDigitalSvc:IDigitalSvc = {}

    if (this.digitalSvcForm.controls.ServiceCode.dirty) {
      tempDigitalSvc.ServiceCode = this.digitalSvcForm.controls.ServiceCode.value!
    }
    if (this.digitalSvcForm.controls.ServiceName.dirty) {
      tempDigitalSvc.ServiceName = this.digitalSvcForm.controls.ServiceName.value!
    }
    if (this.digitalSvcForm.controls.Status.dirty) {
      tempDigitalSvc.Status = this.digitalSvcForm.controls.Status.value!
    }
    if (this.digitalSvcForm.controls.APIName.dirty) {
      tempDigitalSvc.APIName = this.digitalSvcForm.controls.APIName.value!
    }
    if (this.digitalSvcForm.controls.Purpose.dirty) {
      tempDigitalSvc.Purpose = this.digitalSvcForm.controls.Purpose.value!
    }
    if (this.digitalSvcForm.controls.Description.dirty) {
      tempDigitalSvc.Description = this.digitalSvcForm.controls.Description.value!
    }
    if (this.digitalSvcForm.controls.UsedForConnect.dirty) {
      tempDigitalSvc.UsedForConnect = this.digitalSvcForm.controls.UsedForConnect.value!
    }
    if (this.digitalSvcForm.controls.UsedForAssure.dirty) {
      tempDigitalSvc.UsedForAssure = this.digitalSvcForm.controls.UsedForAssure.value!
    }
    if (this.digitalSvcForm.controls.UsedForBilling.dirty) {
      tempDigitalSvc.UsedForBilling = this.digitalSvcForm.controls.UsedForBilling.value!
    }
    if (this.digitalSvcForm.controls.UsedForOther.dirty) {
      tempDigitalSvc.UsedForOther = this.digitalSvcForm.controls.UsedForOther.value!
    }    
    if (this.digitalSvcForm.controls.Notes.dirty) {
      tempDigitalSvc.Notes = this.digitalSvcForm.controls.Notes.value!
    }    

    return tempDigitalSvc

  }


  // Method to reset the DigitalSvc Form to the values of the current DigitalSvc record
  resetDigitalSvcForm() {

    // If digital service record exists, then set the form values to the record values
    if (this.DigitalSvc) {

      this.digitalSvcForm.reset({
        ServiceCode: this.DigitalSvc?.ServiceCode ?? '',
        ServiceName: this.DigitalSvc?.ServiceName ?? '',
        Status: this.DigitalSvc?.Status ?? '',
        APIName: this.DigitalSvc?.APIName ?? '',
        Purpose: this.DigitalSvc?.Purpose ?? '',
        Description: this.DigitalSvc?.Description ?? '',
        UsedForConnect: this.DigitalSvc?.UsedForConnect ?? '',
        UsedForAssure: this.DigitalSvc?.UsedForAssure ?? '',
        UsedForBilling: this.DigitalSvc?.UsedForBilling ?? '',
        UsedForOther: this.DigitalSvc?.UsedForOther ?? '',
        Notes: this.DigitalSvc?.Notes ?? '',
      });

    }
    // Else reset the form to empty values
    else {

      this.digitalSvcForm.reset()

    }

  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
