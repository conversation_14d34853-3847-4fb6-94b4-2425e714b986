@if (isLoading) { 
<mat-spinner></mat-spinner> 
}

@else {

<mat-card class="example-card">

    <form [formGroup]="taskForm">

        <div class="form-row-single-col">

            <mat-form-field>
                <mat-label for="access-seeker-id">Access Seeker Id: </mat-label>
                <input matInput type="text" id="access-seeker-id" type="text" formControlName="AccessSeekerId" readonly="true">

                @if (this.allowAccessSeekerSelection) {
                <button 
                type="button" 
                mat-icon-button 
                matSuffix
                matTooltip="Pick an Acccess Seeker"
                (click)="OpenAccessSeekerListDialog()">
                <mat-icon matSuffix class="material-icons md-24">chevron_right</mat-icon>
                </button>
                }

            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="type">Type:</mat-label>
                <input matInput id="type" type="text" formControlName="Type">
            </mat-form-field>


            <mat-form-field>
                <mat-label for="status">Status:</mat-label>
                <mat-select id="status" formControlName="Status">
                    <mat-option value="Open">Open</mat-option>
                    <mat-option value="Closed">Closed</mat-option>
                  </mat-select>
            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="title">Title:</mat-label>
                <input matInput id="title" type="text" formControlName="Title">
            </mat-form-field>

            <mat-form-field>
                <mat-label for="assigned-to">Assigned To:</mat-label>
                <input matInput id="assigned-to" type="text" formControlName="AssignedTo">
            </mat-form-field>

        </div>

        <div class="form-row">

            <mat-form-field>
                <mat-label for="due-date">Due Date:</mat-label>
                <input matInput id="due-date" [matDatepicker]="duepicker" formControlName="DueDate" readonly="true">
                <button type="button" mat-icon-button matSuffix matTooltip="Clear date" (click)="ClearDateFormField('DueDate')">
                    <mat-icon matSuffix class="material-icons md-24">clear</mat-icon>
                </button>
                <mat-datepicker-toggle matSuffix [for]="duepicker" matTooltip="Select date"></mat-datepicker-toggle>
                <mat-datepicker #duepicker></mat-datepicker>
            </mat-form-field>

            <mat-form-field>
                <mat-label for="completed-date">Completed Date:</mat-label>
                <input matInput id="completed-date" [matDatepicker]="completedpicker" formControlName="CompletedDate" readonly="true">
                <button type="button" mat-icon-button matSuffix matTooltip="Clear date" (click)="ClearDateFormField('CompletedDate')">
                    <mat-icon matSuffix class="material-icons md-24">clear</mat-icon>
                </button>
                <mat-datepicker-toggle matSuffix [for]="completedpicker" matTooltip="Select date"></mat-datepicker-toggle>
                <mat-datepicker #completedpicker></mat-datepicker>
            </mat-form-field>

        </div>

        <mat-form-field>
            <mat-label for="notes">Notes:</mat-label>
            <textarea matInput id="notes" formControlName="Notes" rows="4"></textarea>
        </mat-form-field>

        <mat-divider style="margin-top: 10px;"></mat-divider>

        <div style="display: flex; justify-content: center;"> 
            <button style="margin-right: 20px" mat-raised-button [disabled]="!Task" type="button" (click)="onDeleteClick()">Delete</button>
            <button style="margin-right: 20px" mat-raised-button [disabled]="taskForm.pristine" type="button" (click)="onCancelClick()">Cancel</button>
            <button mat-raised-button [disabled]="taskForm.pristine" type="submit" (click)="onSaveClick()">Submit</button>
        </div>

    </form>

</mat-card>

}