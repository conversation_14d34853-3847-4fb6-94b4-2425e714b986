import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TaskService } from '../../services/task.service';
import { IAccessSeeker, ITask, ITaskSkeleton, IYNEnum } from '../../models/models';

import { AccessSeekerListDialogComponent } from '../access-seeker-list-dialog/access-seeker-list-dialog.component';

import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';

import { ReactiveFormsModule, Validators } from '@angular/forms';
import { FormGroup, FormControl } from '@angular/forms';
import { YesNoDialogComponent } from '../yes-no-dialog/yes-no-dialog.component';

import { MatNativeDateModule } from '@angular/material/core';
import { DateAdapter, MAT_DATE_LOCALE } from '@angular/material/core';
import { CustomDateAdapter } from '../../classes/custom-date-adapter.class';

@Component({
  selector: 'app-task-form',
  imports: [
    MatCardModule,
    MatDividerModule,
    MatButtonModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    ReactiveFormsModule,
    MatNativeDateModule
  ],
  providers: [
    { provide: DateAdapter, useClass: CustomDateAdapter },
    { provide: MAT_DATE_LOCALE, useValue: 'en-AU' } // Optional: Set the locale to en-AU
  ],
  templateUrl: './task-form.component.html',
  styleUrl: './task-form.component.scss'
})
export class TaskFormComponent implements OnInit {

  // Input Task Skeleton (Optional)
  @Input() taskSkeleton: ITaskSkeleton | null = null 

  // Output when a new Task has been created
  @Output() taskCreated: EventEmitter<ITask> = new EventEmitter();

  // Output when a Task has been updated
  @Output() taskUpdated: EventEmitter<ITask> = new EventEmitter();

  // Current Task record
  Task: ITask | null = null

  // Form Group for Task
  taskForm = new FormGroup({
    AccessSeekerRecordId: new FormControl('', Validators.required),
    AccessSeekerId: new FormControl('', Validators.required),
    Title: new FormControl('', Validators.required),
    Type: new FormControl('', Validators.required),
    Status: new FormControl('', Validators.required),
    AssignedTo: new FormControl(''),
    DueDate: new FormControl<Date | null>(null),
    CompletedDate: new FormControl<Date | null>(null),
    Notes: new FormControl(''),
  });

  allowAccessSeekerSelection: boolean = true

  isLoading: boolean = false

  constructor(
    private snackBar: MatSnackBar, 
    private taskService: TaskService,
    public dialog: MatDialog) { }

  ngOnInit(): void {

    // If a Task skeleton was passed in with an id, then it must be an existing record (so get the record)
    if (this.taskSkeleton && this.taskSkeleton.id != '') {

      if (this.taskSkeleton.id) {
        this.getTaskRecord(this.taskSkeleton.id);
      }
    }

    // Else if a Task skeleton was passed in without an id, then it must be a create
    // set the form based on the skeleton
    else if (this.taskSkeleton && this.taskSkeleton.id == '') {
      this.Task = this.taskSkeleton
      this.resetTaskForm()

      // Prevent the user from selecting an Access Seeker
      // as it has been provided in the skeleton
      this.allowAccessSeekerSelection = false

    }

  }

  onSaveClick() {

    if (this.taskForm.valid) {

      // If Task exists, then update the record
      if (this.Task?.id) {
        this.updateTaskRecord()
      }

      // Else we are creating a new Task
      else {

        // If the Access Seeker selection is not allowed, then mark the fields as dirty
        // This is needed to ensure that these fields are sent to the back-end on a create.
        if (this.allowAccessSeekerSelection == false) {
          this.taskForm.controls.AccessSeekerRecordId.markAsDirty()
          this.taskForm.controls.AccessSeekerId.markAsDirty()
        }

        this.createTaskRecord()
      }

    } 
    
    else {
      this.showMessage("Please fix errors before resubmitting")
    }

  }

  onCancelClick() {

    // Reset the Task form
    this.resetTaskForm()

  }

  onDeleteClick() {

    // Open a dialog to confirm the delete action
    const dialogRef = this.dialog.open(YesNoDialogComponent, {
      width: '600px',
      data: {
        Title: "Confirm Delete",
        Subtitle: "Confirm you want to process with deleting record",
        Message: "Are you sure you want to delete this record?",
        IconName: "delete"
      },
    });

    dialogRef.afterClosed().subscribe(result => {

      // If user confirms action, then proceed to delete record
      if (result == IYNEnum.Y) {

        //            this.deleteTaskRecord()
      }

      else {
        this.showMessage("User cancelled delete")
      }

    });

  }

  // Method to get the Task record from the back-end and set the form
  public getTaskRecord(taskRecordId: string) {

    this.isLoading = true

    this.taskService.getTaskRecord(taskRecordId)
      .subscribe({
        next: (data) => {
          
          // Update the Task with the new values
          this.Task = data.data
          this.resetTaskForm()

          this.isLoading = false
          
        },
        error: () => {

          // Reset the Task record
          this.Task = null
          this.resetTaskForm()

          this.showMessage("Error getting Task record")

        }
      });


  }

  // Method to clear the current Task Record
  public clearTaskRecord() {

    this.Task = null
    this.resetTaskForm()

  }


  // Method to create the Task Record on the back-end
  private createTaskRecord() {

    // Create the update payload
    let payload:ITask = this.getChangedFieldsFromForm()

    // Call the back-end to create the record
    this.taskService.createTaskRecord(payload)
      .subscribe({
        next: (data) => {
          
          // Create has been successful, so set the Task
          this.Task = data.data
          this.resetTaskForm()

          this.showMessage("Create Successful")

          // Tell the world that a new Task has been successfully created
          this.taskCreated.emit(this.Task!)

        },
        error: (error) => {

          // If specific error message returned, then show specific message otherwise show a generic message
          if (error.error && error.error.errormsg) {
            this.showMessage("Error creating Task record. " + error.error.errormsg)
            console.error("Error creating Task record. " + error.error.errormsg)
          }

          else {
            this.showMessage("Error creating record")
          }
        }
      });

  }
  
  // Method to update the Task Record on the back-end
  private updateTaskRecord() {

    var payload: ITask
    var recordId: string

    // Get the id of the record to update
    recordId = this.Task?.id || ''

    // Create the update payload
    payload = this.getChangedFieldsFromForm()

    // Call the back-end to update the record
    this.taskService.updateTaskRecord(recordId, payload)
      .subscribe({
        next: (data) => {

          // Update has been successful, so update Task with the new values
          this.Task = data.data
          this.resetTaskForm()

          this.showMessage("Update Successful")

          // Tell the world that a task has been successfully updated
          this.taskUpdated.emit(this.Task!)

        },
        error: (error) => {

          // If specific error message returned, then show specific message otherwise show a generic message
          if (error.error && error.error.errormsg) {
            this.showMessage("Error updating Task record. " + error.error.errormsg)
            console.error("Error updating Task record. " + error.error.errormsg)
          }

          else {
            this.showMessage("Error updating record")
          }
        }
      });

  }


  getChangedFieldsFromForm() {

    let tempTask:ITask = {}

    if (this.taskForm.controls.AccessSeekerRecordId.dirty) {
      tempTask.AccessSeekerRecordId = this.taskForm.controls.AccessSeekerRecordId.value!
    }
    if (this.taskForm.controls.AccessSeekerId.dirty) {
      tempTask.AccessSeekerId = this.taskForm.controls.AccessSeekerId.value!
    }
    if (this.taskForm.controls.Title.dirty) {
      tempTask.Title = this.taskForm.controls.Title.value!
    }
    if (this.taskForm.controls.Type.dirty) {
      tempTask.Type = this.taskForm.controls.Type.value!
    }
    if (this.taskForm.controls.Status.dirty) {
      tempTask.Status = this.taskForm.controls.Status.value!
    }
    if (this.taskForm.controls.AssignedTo.dirty) {
      tempTask.AssignedTo = this.taskForm.controls.AssignedTo.value!
    }
    if (this.taskForm.controls.DueDate.dirty) {
      tempTask.DueDate = this.dateToString(this.taskForm.controls.DueDate.value)
    }
    if (this.taskForm.controls.CompletedDate.dirty) {
      tempTask.CompletedDate = this.dateToString(this.taskForm.controls.CompletedDate.value)
    }
    if (this.taskForm.controls.Notes.dirty) {
      tempTask.Notes = this.taskForm.controls.Notes.value!
    }

    return tempTask

  }


  // Method to reset the Task Form to the values of the current Task record
  resetTaskForm() {

    // If task record exists, then set the form values to the record values
    if (this.Task) {

      this.taskForm.reset({
        AccessSeekerRecordId: this.Task?.AccessSeekerRecordId ?? '',
        AccessSeekerId: this.Task?.AccessSeekerId ?? '',
        Title: this.Task?.Title ?? '',
        Type: this.Task?.Type ?? '',
        Status: this.Task?.Status ?? '',
        AssignedTo: this.Task?.AssignedTo ?? '',
        DueDate: this.Task?.DueDate ? new Date(this.Task.DueDate) : null,
        CompletedDate: this.Task?.CompletedDate ? new Date(this.Task.CompletedDate) : null,
        Notes: this.Task?.Notes ?? '',
      });

    }
    // Else reset the form to empty values
    else {

      this.taskForm.reset()

    }

  }

  OpenAccessSeekerListDialog() {
    
    // Open the Access Seeker List Dialog
    const dialogRef = this.dialog.open(AccessSeekerListDialogComponent, {
      width: '60%',
      height: 'fit',
      data: {},
    });

    // When the dialog is closed, get the selected Access Seeker
    dialogRef.afterClosed().subscribe((result: IAccessSeeker) => {

      // If a result was returned, then set the Access Seeker
      if (result) {

        // Set the Access Seeker Record Id in the form field
        this.taskForm.controls.AccessSeekerRecordId.setValue(result.id!);
        this.taskForm.controls.AccessSeekerRecordId.markAsDirty();

        // Set the Access Seeker Id in the form field
        this.taskForm.controls.AccessSeekerId.setValue(result.AccessSeekerId!);
        this.taskForm.controls.AccessSeekerId.markAsDirty();

      }

    });
  }

  dateToString(date: Date | null): string {

      var dateString:string = ''

      // If Due Date is set, then set covert it to a string in the format YYYY-MM-DD
      // This ensures that the date is not changed due to timezone differences
      // and can be stored in the back-end in a consistent format
      if (date) {

        dateString = date.getFullYear() + '-' + ("0" + (date.getMonth() + 1)).slice(-2) + '-' + ("0" + date.getDate()).slice(-2)
      }
      // Else set the Due Date to an empty string
      else {

        dateString = ''
      }

      return dateString

  }

  // Method to clear a date form field
  ClearDateFormField(formFieldControlName: string) {
    this.taskForm.get(formFieldControlName)?.setValue(null);
    this.taskForm.get(formFieldControlName)?.markAsDirty();
  }

  // Method to show a message in the material snackbar
  showMessage(message: string) {
    this.snackBar.open(message, 'OK', {
      duration: 3000,
    });
  }


}
