<mat-card class="example-card">

    <mat-card-title class="example-card-title">
        <mat-icon class="material-icons md-24" color="primary">contacts</mat-icon>
        Contacts
    </mat-card-title>
    
    <mat-divider></mat-divider>

    <mat-card-content class="example-card-content">

        <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" [(selectedIndex)]="selectedTabIndex">

            <mat-tab label="List View">

                <app-contact-list 
                    [showCreateButton]="true"
                    (createRecordClicked)="onCreateRecordClick()"
                    (selectionChanged)="onSelectionChanged($event)">
                </app-contact-list>

            </mat-tab>

            <mat-tab label="Detail View">

                <app-contact-form></app-contact-form>

            </mat-tab>  

        </mat-tab-group>

    </mat-card-content>
    
</mat-card>
