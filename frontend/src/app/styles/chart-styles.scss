/* Common chart styles */
.chart-container {
  height: 500px;
  width: 100%;
  margin: 20px 0;
  display: block;
  position: relative;
}

.data-container {
  width: 100%;
  margin: 20px 0;
}

/* Ensure mat-tab-group takes full width */
.mat-mdc-tab-group {
  width: 100%;
}

/* Ensure mat-tab-body takes full width and height */
.mat-mdc-tab-body-content {
  height: 100%;
  width: 100%;
  overflow: visible !important;
}

/* Ensure chart parent containers take full width */
.mat-mdc-card-content {
  width: 100%;
  padding: 16px;
  min-height: 550px;
}

/* Fix for AG Charts container */
ag-charts {
  display: block !important;
  width: 100% !important;
  height: 500px !important;
  min-height: 500px !important;
}
