import { NativeDateAdapter } from '@angular/material/core';
import { DateAdapter } from '@angular/material/core';
import { MAT_DATE_LOCALE } from '@angular/material/core';

export class CustomDateAdapter extends NativeDateAdapter {
  override format(date: any, displayFormat: any): string {
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  }
}