import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatRippleModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';

// Import custom components
import { SideNavbarComponent } from './components/side-navbar/side-navbar.component';
import { AboutAppDialogComponent } from './components/about-app-dialog/about-app-dialog.component';

@Component({
    selector: 'app-root',
    imports: [
        CommonModule,
        RouterOutlet,
        MatToolbarModule,
        MatSidenavModule,
        MatIconModule,
        MatMenuModule,
        SideNavbarComponent,
        MatRippleModule
    ],
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss']
})
export class AppComponent {

  title = 'RSPTracker';

  constructor(public dialog: MatDialog) { }


  openAboutAppDialog() {

    // Open the About App Dialog
    const dialogRef = this.dialog.open(AboutAppDialogComponent, {
      width: '700px',
      data: {},
    });

    dialogRef.afterClosed().subscribe(result => {

      // Nothing to do...

    });

  }

}
