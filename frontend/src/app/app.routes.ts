import { Routes } from '@angular/router';
import { AccessSeekerComponent } from './components/access-seeker/access-seeker.component';
import { NoteComponent } from './components/note/note.component';
import { ContactComponent } from './components/contact/contact.component';
import { TaskComponent } from './components/task/task.component';
import { ProjectComponent } from './components/project/project.component';
import { DigitalSvcComponent } from './components/digital-svc/digital-svc.component';
import { DigitalSvcVersionComponent } from './components/digital-svc-version/digital-svc-version.component';
import { DigitalUsageDashboardComponent } from './components/digital-usage-dashboard/digital-usage-dashboard.component';
import { DigitalUsageRspDashboardComponent } from './components/digital-usage-rsp-dashboard/digital-usage-rsp-dashboard.component';
import { DigitalUsageSvcDashboardComponent } from './components/digital-usage-svc-dashboard/digital-usage-svc-dashboard.component';
import { CioRspExecutiveComponent } from './components/cio-rsp-executive/cio-rsp-executive.component';
import { TestComponent } from './components/test/test.component';

export const routes: Routes = [
    { path: 'accessseeker', component: AccessSeekerComponent },
    { path: 'note', component: NoteComponent },
    { path: 'contact', component: ContactComponent },
    { path: 'task', component: TaskComponent },
    { path: 'project', component: ProjectComponent },
    { path: 'digitalsvc', component: DigitalSvcComponent },
    { path: 'digitalsvcversion', component: DigitalSvcVersionComponent },
    { path: 'digitalusagedashboard', component: DigitalUsageDashboardComponent },
    { path: 'digitalusagerspdashboard', component: DigitalUsageRspDashboardComponent },
    { path: 'digitalusagesvcdashboard', component: DigitalUsageSvcDashboardComponent },
    { path: 'cio-rsp-executive', component: CioRspExecutiveComponent },
    { path: 'test', component: TestComponent },
];
