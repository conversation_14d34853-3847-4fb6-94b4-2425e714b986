.example-container {
    position: absolute;
    top: 60px;
    bottom: 60px;
    left: 0;
    right: 0;
  }
  
  .example-sidenav {
    display: flex;
    width: 250px;
    background: whitesmoke
  }
  
  .example-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }
  
  .example-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }

  /* Rules for sizing the icon. */
.material-icons.md-18 { font-size: 18px; font-style: normal; color: white; vertical-align: text-top;}
.material-icons.md-24 { font-size: 24px; font-style: normal; color: white; vertical-align: text-top;}
.material-icons.md-36 { font-size: 36px; font-style: normal; color: white; vertical-align: text-top;}
.material-icons.md-48 { font-size: 48px; font-style: normal; color: white; vertical-align: text-top;}

.menu-button {
  padding-right: 20px;
  background-color: #673ab7;
  color: white;
  border-style: none;
}