FROM ccoe-docker.apro.nbnco.net.au/stable/debian:bookworm-slim_24-02-2025 AS nbnbase
FROM pos-docker.apro.nbnco.net.au/pos/rspsysops/node:23.11.0-bookworm-slim
LABEL maintainer="Digital Services Enablement <<EMAIL>>"

COPY --from=nbnbase /usr/share/ca-certificates /usr/share/ca-certificates
COPY --from=nbnbase /usr/local/share/ca-certificates /usr/local/share/ca-certificates
COPY --from=nbnbase /etc/ssl/certs /etc/ssl/certs
COPY --from=nbnbase /etc/apt/sources.list /etc/apt/sources.list

COPY --chmod=755 artefacts/entrypoint.sh /usr/local/bin/entrypoint.sh
COPY artefacts/nginx.conf /tmp/nginx.conf
COPY src /tmp/src
COPY .env /tmp/.env
COPY angular.json /tmp/angular.json
COPY package.json /tmp/package.json
COPY tsconfig.json /tmp/tsconfig.json
COPY tsconfig.app.json /tmp/tsconfig.app.json
COPY tsconfig.spec.json /tmp/tsconfig.spec.json

WORKDIR /tmp

SHELL ["/bin/bash", "-c"]
RUN \
    rm -rf /etc/apt/sources.list.d/* && \
    apt-get update && \
    apt-get install -y --no-install-recommends nginx && \
    awk -F= '{print $1 "=" $1}' .env > .env.production && \
    npm config -g set cafile /etc/ssl/certs/nbn_cacert.pem && \
    npm config -g set registry https://apro.nbnco.net.au/api/npm/pos-npm-virtual && \
    npm install && \
    npm run build && \
    cp nginx.conf /etc/nginx/nginx.conf && \
    rm -rf /usr/share/nginx/html/* && \
    cp -rf dist/rsptracker/browser/* /usr/share/nginx/html && \
    ln -sf /dev/stdout /var/log/nginx/access.log && \
    ln -sf /dev/stderr /var/log/nginx/error.log && \
    chmod 777 /dev/stdout /dev/stderr && \
    apt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false && \
    rm -rf /var/lib/apt/lists/* && \
    rm -rf /tmp/*

WORKDIR /

USER 1000

ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
