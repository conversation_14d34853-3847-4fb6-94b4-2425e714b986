{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "cli": {"analytics": "off"}, "projects": {"RSPTracker": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@ngx-env/builder:application", "options": {"outputPath": "dist/rsptracker", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/deeppurple-amber.css", "src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@ngx-env/builder:dev-server", "configurations": {"production": {"buildTarget": "RSPTracker:build:production"}, "development": {"buildTarget": "RSPTracker:build:development"}}, "defaultConfiguration": "development", "options": {}}, "extract-i18n": {"builder": "@ngx-env/builder:extract-i18n", "options": {"buildTarget": "RSPTracker:build"}}, "test": {"builder": "@ngx-env/builder:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/deeppurple-amber.css", "src/styles.scss"], "scripts": [], "builderMode": "browser"}}}}}}