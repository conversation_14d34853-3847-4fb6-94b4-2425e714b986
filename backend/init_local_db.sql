-- Initialize local database for development
-- This script creates the database and tables needed for local development

-- <PERSON><PERSON> database if it doesn't exist
CREATE DATABASE IF NOT EXISTS mydb;
USE mydb;

-- Create AccessSeeker table
CREATE TABLE IF NOT EXISTS AccessSeeker (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	AccessSeekerId varchar(45) NULL,
	Status varchar(25) NULL,
	Name varchar(100) NULL,
	Alias varchar(100) NULL,
	Type varchar(50) NULL,
	ParentAccessSeekerRecordId INT NULL,
	Category1 varchar(100) NULL,
	Category2 varchar(100) NULL,
	Website varchar(100) NULL,
	About varchar(500) NULL,
	Comments varchar(500) NULL,
	SDM varchar(80) NULL,
	AGM varchar(80) NULL,
	CDM varchar(80) NULL,
	CONSTRAINT AccessSeeker_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;

-- Create Note table
CREATE TABLE IF NOT EXISTS Note (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	AccessSeekerRecordId INT NULL,
	Type varchar(50) NULL,
	Title varchar(100) NULL,
	Sequence INT NULL,
	Description varchar(2000) NULL,
	CONSTRAINT Note_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;

-- Create Contact table
CREATE TABLE IF NOT EXISTS Contact (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	AccessSeekerRecordId INT NULL,
	FirstName varchar(50) NULL,
	LastName varchar(50) NULL,
	Role varchar(50) NULL,
	Phone varchar(50) NULL,
	Email varchar(200) NULL,
	Notes varchar(500) NULL,
	CONSTRAINT Contact_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;

-- Create Task table
CREATE TABLE IF NOT EXISTS Task (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	AccessSeekerRecordId INT NULL,
	Title varchar(100) NULL,
	Type varchar(50) NULL,
	Status varchar(25) NULL,
	AssignedTo varchar(100) NULL,
	DueDate DATE NULL,
	CompletedDate DATE NULL,
	Notes varchar(500) NULL,	
	CONSTRAINT Task_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;

-- Create Project table
CREATE TABLE IF NOT EXISTS Project (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	AccessSeekerRecordId INT NULL,
	Name varchar(100) NULL,
	Org varchar(50) NULL,
	Category1 varchar(100),
	Category2 varchar(100),
	Status varchar(25) NULL,
	PlannedStartDate DATE NULL,
	ActualStartDate DATE NULL,
	PlannedEndDate DATE NULL,
	ActualEndDate DATE NULL,
	Description varchar(500) NULL,
	Notes varchar(500) NULL,
	CONSTRAINT Project_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;

-- Create DigitalService table
CREATE TABLE IF NOT EXISTS DigitalService (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	ServiceCode varchar(50) NULL,
	ServiceName varchar(50) NULL,
	Status varchar(25) NULL,
	APIName varchar(50) NULL,
	Purpose varchar(200) NULL,
	Description varchar(500) NULL,
	UsedForConnect varchar(1) NULL,
	UsedForAssure varchar(1) NULL,
	UsedForBilling varchar(1) NULL,
	UsedForOther varchar(1) NULL,
	Notes varchar(500) NULL,
	CONSTRAINT DigitalService_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;

-- Create DigitalServiceVersion table
CREATE TABLE IF NOT EXISTS DigitalServiceVersion (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	DigitalServiceRecordId INT NULL,
	Version varchar(2) NULL,
	Status varchar(25) NULL,
	ReleaseDate DATE NULL,
	Description varchar(500) NULL,
	CONSTRAINT DigitalServiceVersion_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;

-- Insert some sample data for testing
-- Sample AccessSeeker
INSERT INTO AccessSeeker (AccessSeekerId, Status, Name, Alias, Type, Category1, Category2, Website, About, Comments, created, created_by)
VALUES ('AS001', 'Active', 'Sample Access Seeker', 'SAS', 'RSP', 'Category A', 'Subcategory 1', 'https://example.com', 'This is a sample access seeker for testing', 'Test comments', NOW(), 'system');

-- Sample DigitalService
INSERT INTO DigitalService (ServiceCode, ServiceName, Status, APIName, Purpose, Description, UsedForConnect, UsedForAssure, UsedForBilling, UsedForOther, created, created_by)
VALUES ('SVC001', 'Sample Service', 'Active', 'SampleAPI', 'Testing', 'This is a sample digital service for testing', 'Y', 'Y', 'N', 'N', NOW(), 'system');

-- Sample DigitalServiceVersion
INSERT INTO DigitalServiceVersion (DigitalServiceRecordId, Version, Status, ReleaseDate, Description, created, created_by)
VALUES (1, '1', 'Active', NOW(), 'Initial version', NOW(), 'system');

-- Sample Project
INSERT INTO Project (AccessSeekerRecordId, Name, Org, Status, Description, created, created_by)
VALUES (1, 'Sample Project', 'Test Org', 'Active', 'This is a sample project for testing', NOW(), 'system');

-- Sample Task
INSERT INTO Task (AccessSeekerRecordId, Title, Type, Status, AssignedTo, DueDate, Notes, created, created_by)
VALUES (1, 'Sample Task', 'Development', 'Open', 'Test User', NOW(), 'This is a sample task for testing', NOW(), 'system');

-- Sample Note
INSERT INTO Note (AccessSeekerRecordId, Type, Title, Description, created, created_by)
VALUES (1, 'General', 'Sample Note', 'This is a sample note for testing', NOW(), 'system');

-- Sample Contact
INSERT INTO Contact (AccessSeekerRecordId, FirstName, LastName, Role, Email, Phone, Notes, created, created_by)
VALUES (1, 'John', 'Doe', 'Manager', '<EMAIL>', '************', 'This is a sample contact for testing', NOW(), 'system');
