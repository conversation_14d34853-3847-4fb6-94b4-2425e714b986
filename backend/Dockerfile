FROM pos-docker.apro.nbnco.net.au/pos/python:3.13.0-20250311-152325
LABEL maintainer="Digital Services Enablement <<EMAIL>>"

COPY requirements.txt /tmp/requirements.txt
COPY app /app

SHELL ["/bin/bash", "-c"]
WORKDIR /tmp

RUN \
  pip3 install --upgrade pip && \
  pip3 install -r requirements.txt && \
  groupadd -g 1000 insights && \
  useradd -u 1000 insights -g insights -s /usr/sbin/nologin -m && \
  rm -rf *

ENV FLASK_APP=/app/app.py

WORKDIR /app
USER 1000
ENTRYPOINT ["flask", "run", "--host=0.0.0.0"]