# ****************************************************************************
# Author: Name
# Desc: Business Services for Digital Usage
#
#   - BusinessFunction1
#   - BusinessFunction2
#
# ****************************************************************************

from flask import request
from flask import Response
from flask import jsonify
from DBHandler import get_connection
from AGGridServices import generateWhereSQL
from AGGridServices import generateOrderBySQL
from AGGridServices import generateLimitOffsetSQL
import logging
import json
from decimal import Decimal

logger = logging.getLogger()

# ****************************************************************************
# Get Digital Usage History
# ****************************************************************************
def GetDigitalUsageHistory():

    logger.info('Inside GetDigitalUsageHistory')
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate the query
        digital_usage_history_sql = """
        select
            distinct(AllPeriods.Period) as Period,
            AllTxn.TotalTxns as TotalTxns,
            APITxn.TotalTxns as TotalAPITxns,
            PortalTxn.TotalTxns as TotalPortalTxns
        from
            TempDigitalUsage AllPeriods
        left outer join 
            (select Period, sum(Total) as TotalTxns from TempDigitalUsage dudv group by Period) as AllTxn on AllPeriods.Period = AllTxn.Period
        left outer join 
            (select Period, sum(Total) as TotalTxns from TempDigitalUsage dudv where businessChannel = 'APIGWY' group by Period) as APITxn on AllPeriods.Period = APITxn.Period 
        left outer join 
            (select Period, sum(Total) as TotalTxns from TempDigitalUsage dudv where businessChannel = 'ServicePortal' group by Period) as PortalTxn on AllPeriods.Period = PortalTxn.Period
        """

        cursor.execute(digital_usage_history_sql)
        records = cursor.fetchall()
  
    response = json.dumps({"data": records}, default=myconverter)
    return response


# ****************************************************************************
# Get Digital Usage by RSP for a given period
# ****************************************************************************
def GetDigitalUsageByRSP(period: str):

    logger.info('Inside GetDigitalUsageByRSP')
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate the query
        digital_usage_by_rsp_sql = """
            select
                as2.AccessSeekerId,
                as2.Name,
                as2.Category1,
                rank() over (ORDER by AllTxn.TotalTxns desc) as AllDigitalVolRank,
                rank() over (ORDER by APITxn.TotalTxns desc) as APITxnVolRank,
                rank() over (ORDER by PortalTxn.TotalTxns desc) as PortalTxnVolRank,
                APITxn.TotalTxns as TotalAPITxns,
                PortalTxn.TotalTxns as TotalPortalTxns,
                AllTxn.TotalTxns as TotalTxns,
                round((1.0 * APITxn.TotalTxns) / AllTxn.TotalTxns, 2) * 100 as APIPercentOfTxns
            from
                AccessSeeker as2
            left outer join 
            (select accessSeekerId, sum(Total) as TotalTxns from TempDigitalUsage du where period = %(period)s group by accessSeekerId) as AllTxn on as2.AccessSeekerId = AllTxn.accessSeekerId
            left outer join 
            (select accessSeekerId, sum(Total) as TotalTxns from TempDigitalUsage du where period = %(period)s and businessChannel = 'APIGWY' group by accessSeekerId) as APITxn on AllTxn.accessSeekerId = APITxn.accessSeekerId
            left outer join 
            (select accessSeekerId, sum(Total) as TotalTxns from TempDigitalUsage du where period = %(period)s and businessChannel = 'ServicePortal' group by accessSeekerId) as PortalTxn on AllTxn.accessSeekerId = PortalTxn.accessSeekerId
            order by AllTxn.TotalTxns desc
            """

        cursor.execute(digital_usage_by_rsp_sql,  {"period": period})
        records = cursor.fetchall()
  
    response = json.dumps({"data": records}, default=myconverter)
    return response

# ****************************************************************************
# Get Digital Usage by Service for a given period
# ****************************************************************************
def GetDigitalUsageByService(period: str):

    logger.info('Inside GetDigitalUsageByService')
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate the query
        digital_usage_by_service_sql = """
            select
                ds.ServiceName,
                ds.APIName,
                rank() over (ORDER by AllTxn.TotalTxns desc) as AllDigitalVolRank,
                rank() over (ORDER by APITxn.TotalTxns desc) as APITxnVolRank,
                rank() over (ORDER by PortalTxn.TotalTxns desc) as PortalTxnVolRank,
                APITxn.TotalTxns as TotalAPITxns,
                PortalTxn.TotalTxns as TotalPortalTxns,
                AllTxn.TotalTxns as TotalTxns,
                round((1.0 * APITxn.TotalTxns) / AllTxn.TotalTxns, 2) * 100 as APIPercentOfTxns
            from
                DigitalService ds
            left outer join 
            (select serviceName, sum(Total) as TotalTxns from TempDigitalUsage du where period = %(period)s group by serviceName) as AllTxn on ds.APIName = AllTxn.serviceName
            left outer join 
            (select serviceName, sum(Total) as TotalTxns from TempDigitalUsage du where period = %(period)s and businessChannel = 'APIGWY' group by serviceName) as APITxn on AllTxn.serviceName = APITxn.serviceName
            left outer join 
            (select serviceName, sum(Total) as TotalTxns from TempDigitalUsage du where period = %(period)s and businessChannel = 'ServicePortal' group by serviceName) as PortalTxn on AllTxn.serviceName = PortalTxn.serviceName
            order by AllTxn.TotalTxns desc
            """

        cursor.execute(digital_usage_by_service_sql,  {"period": period})
        records = cursor.fetchall()
  
    response = json.dumps({"data": records}, default=myconverter)
    return response

# ****************************************************************************
# Get details of the logged in user
# Note: This should be retrieved from the Access Token, not querystring params
# ****************************************************************************
def GetLoggedInUser(request):
    
    logger.info('Inside GetLoggedInUser')
    
    # Initialise variables
    userName = 'Undefined'

    # Get the username from the user profile in the querystring parameters
    if request.args.get('userProfile'):
        userProfile = json.loads(request.args.get('userProfile'))
        userName = userProfile.get('userName')
    
    return userName

def myconverter(o):
    if isinstance(o, Decimal):
        return o.__float__()
