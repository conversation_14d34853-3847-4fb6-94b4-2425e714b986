from pydantic import Ski<PERSON>V<PERSON><PERSON>, Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from functools import cache
from typing import Annotated, Any


class AppConfig(BaseSettings):
    model_config: SettingsConfigDict = SettingsConfigDict(frozen=True)

    cors_origins: str
    logging_config_path: str

    db_host: str
    db_port: int
    db_user: str
    db_password: str
    db_name: str

@cache
def get_app_config() -> AppConfig:
    return AppConfig()
