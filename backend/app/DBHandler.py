import logging

import pymysql
from config import get_app_config


def get_connection():
    try:
        app_config = get_app_config()

        conn = pymysql.connect(
            host=app_config.db_host,
            port=app_config.db_port,
            user=app_config.db_user,
            passwd=app_config.db_password,
            db=app_config.db_name,
            connect_timeout=5,
            cursorclass=pymysql.cursors.DictCursor,
            autocommit=True,
        )

        return conn

    except Exception as e:
        logging.error(f"Database connection error: {str(e)}")
        return None
