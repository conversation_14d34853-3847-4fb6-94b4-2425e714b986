# ****************************************************************************
# Author: Name
# Desc: Common services to process AG Grid Filtering, Sorting and Paging
#       and generate the relevent SQL clauses
#
# To-do: Need to update filtering to handle different filterTypes.
# ****************************************************************************
import json

def generateWhereSQL(payload, filterModelFieldMap = {}):
    
    wherecondition = ' WHERE 1=1';
    
    if 'filterModel' in payload:
        filterModel =  payload['filterModel']
        
        # Debugging
        # print("FilterModelA: " + json.dumps(filterModel));
        
        for field, fieldfiltermodel in filterModel.items():
            
            # Update the field if it has been provided in the filterModelFieldMap
            if field in filterModelFieldMap:
                # print("Inside generateWhereSQL. About to update field from ", field, " to ", filterModelFieldMap[field])
                field = filterModelFieldMap[field]
                
            if fieldfiltermodel['filter'] == "":
                wherecondition = wherecondition + ' and (' + field + ' IS NULL or ' + field + ' = "")'
            else:
                if fieldfiltermodel['type'] == "equals":
                    wherecondition = wherecondition + ' and ' + field + '=\'' + str(fieldfiltermodel['filter']) + '\''
                elif fieldfiltermodel['type'] == "contains":
                    wherecondition = wherecondition + ' and ' + field + ' like \'%' + str(fieldfiltermodel['filter']) + '%\''
            
    return wherecondition

def generateOrderBySQL(payload):
    
    sortcondition = '';
    iTempCount = 0;
    
    if 'sortModel' in payload:
        sortModel =  payload['sortModel']
        
        for sortitem in sortModel:
            
            if iTempCount == 0:
                sortcondition = ' ORDER BY ' + sortitem['colId'] + ' ' + sortitem['sort']
            else:
                sortcondition = sortcondition + ', ' + sortitem['colId'] + ' ' + sortitem['sort']
            
            iTempCount = iTempCount + 1
            
    return sortcondition
    
def generateLimitOffsetSQL(payload):

    Offset = payload.get('startRow', 0)
    Limit = payload.get('endRow', 0) - payload.get('startRow', 0)
    
    # Limit the results based on the paging parameters
    limitcondition = " LIMIT " + str(Limit) + " OFFSET " + str(Offset) 
            
    return limitcondition