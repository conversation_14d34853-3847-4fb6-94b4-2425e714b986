from DBHandler import get_connection

def test_connection():
    print("Attempting to connect to the database...")
    conn = get_connection()
    
    if conn is None:
        print("Failed to connect to the database")
        return
    
    try:
        print("Successfully connected to the database!")
        
        # Create a cursor
        cur = conn.cursor()
        
        # Test query - list all databases
        cur.execute("SELECT datname FROM pg_database")
        databases = cur.fetchall()
        
        print("\nAvailable databases:")
        for db in databases:
            print(f"- {db[0]}")
            
    except Exception as e:
        print(f"Error while testing connection: {str(e)}")
    finally:
        if conn:
            conn.close()
            print("\nDatabase connection closed")

if __name__ == "__main__":
    test_connection() 