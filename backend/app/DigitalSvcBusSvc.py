# ****************************************************************************
# Author: Name
# Desc: Business Services for DigitalSvc
#
#   Record Manager functions
#   - GetDigitalSvcs           --> Get List
#   - GetDigitalSvc            --> Get Record
#   - AddDigitalSvc            --> Add Record
#   - UpdateDigitalSvc         --> Update Record
#   - DeleteDigitalSvc         --> Delete Record
#
#   Other functions for the Business Service
#   - BusinessFunction1
#   - BusinessFunction2
#
# ****************************************************************************

from flask import request
from flask import Response
from flask import jsonify
from DBHandler import get_connection
from AGGridServices import generateWhereSQL
from AGGridServices import generateOrderBySQL
from AGGridServices import generateLimitOffsetSQL
import logging
import json

logger = logging.getLogger()

# ****************************************************************************
# Get List of <Entity> records (e.g. GetAssetCapJobs)
# ****************************************************************************
def GetDigitalSvcs(request):

    logger.info('Inside GetDigitalSvcs')

    # Get the payload which contains the parameters to query data 
    # including AG Grid filter model, sort model and paging details
    payload = request.json
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        sql = """select
                    SQL_CALC_FOUND_ROWS 
                    DigitalService.id,
                    DigitalService.ServiceCode,
                    DigitalService.ServiceName,
                    DigitalService.Status,
                    DigitalService.APIName,
                    DigitalService.Purpose,
                    DigitalService.UsedForConnect,
                    DigitalService.UsedForAssure,
                    DigitalService.UsedForBilling,
                    DigitalService.UsedForOther,
                    DigitalService.created_by,
                    DigitalService.created,
                    DigitalService.modified_by,
                    DigitalService.modified
                from
                    DigitalService"""

        # Select records based on the filtering criteria
        sql = sql + generateWhereSQL(payload)
        
        # Sort records based on the sort criteria
        sql = sql + generateOrderBySQL(payload)
        
        # Limit the results based on the paging parameters
        sql = sql + generateLimitOffsetSQL(payload)        

        cursor.execute(sql)
        records = cursor.fetchall()
        
        # Get the total number of records
        sql = "SELECT FOUND_ROWS() as totalRecords"
        cursor.execute(sql)
        totalRecordsResult = cursor.fetchone()
        totalRecords = totalRecordsResult['totalRecords']  
        
    response = jsonify({"totalRecords": totalRecords, "records": records})
    return response

# ****************************************************************************
# Get a specific DigitalSvc Record
# ****************************************************************************
def GetDigitalSvc(Id):

    logger.info('Inside GetDigitalSvc. Id: ' + str(Id))

    conn = get_connection()
    
    with conn.cursor() as cursor:

        sql = """select
                    DigitalService.id,
                    DigitalService.ServiceCode,
                    DigitalService.ServiceName,
                    DigitalService.Status,
                    DigitalService.APIName,
                    DigitalService.Purpose,
                    DigitalService.Description,
                    DigitalService.UsedForConnect,
                    DigitalService.UsedForAssure,
                    DigitalService.UsedForBilling,
                    DigitalService.UsedForOther,
                    DigitalService.Notes,
                    DigitalService.created_by,
                    DigitalService.created,
                    DigitalService.modified_by,
                    DigitalService.modified
                from
                    DigitalService 
                 where DigitalService.id = %(Id)s"""
        
        cursor.execute(sql, {"Id": Id})
        
        # Get record
        record = cursor.fetchone()
        
    return record

# ****************************************************************************
# Add a new DigitalSvc record
# ****************************************************************************
def AddDigitalSvc(digitalSvc):
    
    logger.info('Inside AddDigitalSvc')

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlFieldList = ""
    sqlFieldValues = ""
    sqlQueryParams = {}

    if "ServiceCode" in digitalSvc:
        sqlFieldList += "ServiceCode, "
        sqlFieldValues += "%(ServiceCode)s, "
        sqlQueryParams["ServiceCode"] = digitalSvc["ServiceCode"]
    if "ServiceName" in digitalSvc:
        sqlFieldList += "ServiceName, "
        sqlFieldValues += "%(ServiceName)s, "
        sqlQueryParams["ServiceName"] = digitalSvc["ServiceName"]	
    if "Status" in digitalSvc:
        sqlFieldList += "Status, "
        sqlFieldValues += "%(Status)s, "
        sqlQueryParams["Status"] = digitalSvc["Status"]
    if "APIName" in digitalSvc:
        sqlFieldList += "APIName, "
        sqlFieldValues += "%(APIName)s, "
        sqlQueryParams["APIName"] = digitalSvc["APIName"]
    if "Purpose" in digitalSvc:
        sqlFieldList += "Purpose, "
        sqlFieldValues += "%(Purpose)s, "
        sqlQueryParams["Purpose"] = digitalSvc["Purpose"]
    if "Description" in digitalSvc:
        sqlFieldList += "Description, "
        sqlFieldValues += "%(Description)s, "
        sqlQueryParams["Description"] = digitalSvc["Description"]	
    if "UsedForConnect" in digitalSvc:
        sqlFieldList += "UsedForConnect, "
        sqlFieldValues += "%(UsedForConnect)s, "
        sqlQueryParams["UsedForConnect"] = digitalSvc["UsedForConnect"]	
    if "UsedForAssure" in digitalSvc:
        sqlFieldList += "UsedForAssure, "
        sqlFieldValues += "%(UsedForAssure)s, "
        sqlQueryParams["UsedForAssure"] = digitalSvc["UsedForAssure"]	
    if "UsedForBilling" in digitalSvc:
        sqlFieldList += "UsedForBilling, "
        sqlFieldValues += "%(UsedForBilling)s, "
        sqlQueryParams["UsedForBilling"] = digitalSvc["UsedForBilling"]	
    if "UsedForOther" in digitalSvc:
        sqlFieldList += "UsedForOther, "
        sqlFieldValues += "%(UsedForOther)s, "
        sqlQueryParams["UsedForOther"] = digitalSvc["UsedForOther"]	
    if "Notes" in digitalSvc:
        sqlFieldList += "Notes, "
        sqlFieldValues += "%(Notes)s, "
        sqlQueryParams["Notes"] = digitalSvc["Notes"]

    sqlFieldList += "created_by, "
    sqlFieldValues += "%(LoggedInUser)s, "
    sqlFieldList += "created "
    sqlFieldValues += "NOW() "

    sqlQueryParams["LoggedInUser"] = LoggedInUser
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate insert SQL
        sql = "INSERT INTO DigitalService (" + sqlFieldList + ") VALUES (" + sqlFieldValues + ")"

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)
        
        # If record sucessfully added
        if cursor.rowcount > 0:

            # Get the updated record
            addedDigitalSvc = GetDigitalSvc(cursor.lastrowid)

        # Else record was not successfully added
        else:
            
            # Get the updated record
            addedDigitalSvc = None

    return addedDigitalSvc

# ****************************************************************************
# Update a DigitalSvc Record
# ****************************************************************************
def UpdateDigitalSvc(id, patchValues):
    
    logger.info('Inside UpdateDigitalSvc')

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlQueryParams = {}

    conn = get_connection()
    
    with conn.cursor() as cursor:
               
        # Generate update SQL
        sql = "UPDATE DigitalService SET "

        if "ServiceCode" in patchValues:
            sql += "ServiceCode = %(ServiceCode)s, "
            sqlQueryParams["ServiceCode"] = patchValues["ServiceCode"]
        if "ServiceName" in patchValues:
            sql += "ServiceName = %(ServiceName)s, "
            sqlQueryParams["ServiceName"] = patchValues["ServiceName"]	
        if "Status" in patchValues:
            sql += "Status = %(Status)s, "
            sqlQueryParams["Status"] = patchValues["Status"]	
        if "APIName" in patchValues:
            sql += "APIName = %(APIName)s, "
            sqlQueryParams["APIName"] = patchValues["APIName"]	
        if "Purpose" in patchValues:
            sql += "Purpose = %(Purpose)s, "
            sqlQueryParams["Purpose"] = patchValues["Purpose"]	
        if "Description" in patchValues:
            sql += "Description = %(Description)s, "
            sqlQueryParams["Description"] = patchValues["Description"]	
        if "UsedForConnect" in patchValues:
            sql += "UsedForConnect = %(UsedForConnect)s, "
            sqlQueryParams["UsedForConnect"] = patchValues["UsedForConnect"]	
        if "UsedForAssure" in patchValues:
            sql += "UsedForAssure = %(UsedForAssure)s, "
            sqlQueryParams["UsedForAssure"] = patchValues["UsedForAssure"]	
        if "UsedForBilling" in patchValues:
            sql += "UsedForBilling = %(UsedForBilling)s, "
            sqlQueryParams["UsedForBilling"] = patchValues["UsedForBilling"]	
        if "UsedForOther" in patchValues:
            sql += "UsedForOther = %(UsedForOther)s, "
            sqlQueryParams["UsedForOther"] = patchValues["UsedForOther"]	
        if "Notes" in patchValues:
            sql += "Notes = %(Notes)s, "
            sqlQueryParams["Notes"] = patchValues["Notes"]

        sql += "modified_by = %(LoggedInUser)s, "
        sql += "modified = NOW() "
        sql += "WHERE id = %(Id)s "

        logger.info('UpdateDigitalSvc SQL: ' + sql)

        sqlQueryParams["Id"] = id
        sqlQueryParams["LoggedInUser"] = LoggedInUser

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)
        
        # If record sucessfully added
        if cursor.rowcount > 0:
            
            # Get the updated record
            updatedDigitalSvc = GetDigitalSvc(id)

        # Else record was not successfully updated
        else:
            
            # Get the updated record
            updatedDigitalSvc = None

    return updatedDigitalSvc
    
# ****************************************************************************
# Delete a DigitalSvc Record
# ****************************************************************************
def DeleteDigitalSvc(request):

    logger.info('Inside DeleteDigitalSvc')

    # Get the message body which contains the data to update 
    payload = request.json

    # Get the Id of the record to delete    
    Id = payload["id"]
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate delete SQL
        sql = """DELETE FROM DigitalService 
                 WHERE id = %(Id)s"""

        # Execute delete SQL with appropriate parameters
        cursor.execute(sql, {"Id": Id})
        
        # If record sucessfully deleted
        if cursor.rowcount > 0:

            # Generate the success response message
            response = jsonify({ "data": {"id": str(Id) }, "message": "Successfully deleted DigitalSvc Record: " + str(Id)})

        # Else record was not successfully deleted
        else:
            
            logger.info("Error deleting DigitalSvc with id = " + str(Id))

            # Generate the error response
            errResponseMsg = json.dumps({"code": 1, "error": "Error deleting DigitalSvc with id = " + str(Id)})

            # Return a 500 error response
            return Response(errResponseMsg, status=500, mimetype='application/json')
               
    return response


# ****************************************************************************
# Get details of the logged in user
# Note: This should be retrieved from the Access Token, not querystring params
# ****************************************************************************
def GetLoggedInUser(request):
    
    logger.info('Inside GetLoggedInUser')
    
    # Initialise variables
    userName = 'Undefined'

    # Get the username from the user profile in the querystring parameters
    if request.args.get('userProfile'):
        userProfile = json.loads(request.args.get('userProfile'))
        userName = userProfile.get('userName')
    
    return userName
