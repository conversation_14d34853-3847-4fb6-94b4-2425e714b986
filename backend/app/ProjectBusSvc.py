# ****************************************************************************
# Author: Name
# Desc: Business Services for Project
#
#   Record Manager functions
#   - GetProjects           --> Get List
#   - GetProject            --> Get Record
#   - AddProject            --> Add Record
#   - UpdateProject         --> Update Record
#   - DeleteProject         --> Delete Record
#
#   Other functions for the Business Service
#   - BusinessFunction1
#   - BusinessFunction2
#
# ****************************************************************************

from flask import request
from flask import Response
from flask import jsonify
from DBHandler import get_connection
from AGGridServices import generateWhereSQL
from AGGridServices import generateOrderBySQL
from AGGridServices import generateLimitOffsetSQL
import logging
import json

logger = logging.getLogger()

# ****************************************************************************
# Get List of <Entity> records (e.g. GetAssetCapJobs)
# ****************************************************************************
def GetProjects(request):

    logger.info('Inside GetProjects')

    # Get the payload which contains the parameters to query data 
    # including AG Grid filter model, sort model and paging details
    payload = request.json
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        sql = """select
                    SQL_CALC_FOUND_ROWS 
                    Project.id,
                    Project.AccessSeekerRecordId,
                    AccessSeeker.AccessSeekerId,
                    Project.Name,
                    Project.Org,
                    Project.Category1,
                    Project.Category2,
                    Project.Status,
                    Project.PlannedStartDate,
                    Project.ActualStartDate,
                    Project.PlannedEndDate,
                    Project.ActualEndDate,
                    Project.Description,
                    Project.created_by,
                    Project.created,
                    Project.modified_by,
                    Project.modified
                from
                    Project 
                    left outer join AccessSeeker on Project.AccessSeekerRecordId = AccessSeeker.id"""

        # Select records based on the filtering criteria
        sql = sql + generateWhereSQL(payload)
        
        # Sort records based on the sort criteria
        sql = sql + generateOrderBySQL(payload)
        
        # Limit the results based on the paging parameters
        sql = sql + generateLimitOffsetSQL(payload)        

        cursor.execute(sql)
        records = cursor.fetchall()
        
        # Get the total number of records
        sql = "SELECT FOUND_ROWS() as totalRecords"
        cursor.execute(sql)
        totalRecordsResult = cursor.fetchone()
        totalRecords = totalRecordsResult['totalRecords']  
        
    response = jsonify({"totalRecords": totalRecords, "records": records})
    return response

# ****************************************************************************
# Get a specific Project Record
# ****************************************************************************
def GetProject(Id):

    logger.info('Inside GetProject. Id: ' + str(Id))

    conn = get_connection()
    
    with conn.cursor() as cursor:

        sql = """select
                    Project.id,
                    Project.AccessSeekerRecordId,
                    AccessSeeker.AccessSeekerId,
                    Project.Name,
                    Project.Org,
                    Project.Category1,
                    Project.Category2,
                    Project.Status,
                    DATE_FORMAT(Project.PlannedStartDate, '%%Y-%%m-%%d') as PlannedStartDate,
                    DATE_FORMAT(Project.ActualStartDate, '%%Y-%%m-%%d') as ActualStartDate,
                    DATE_FORMAT(Project.PlannedEndDate, '%%Y-%%m-%%d') as PlannedEndDate,
                    DATE_FORMAT(Project.ActualEndDate, '%%Y-%%m-%%d') as ActualEndDate,
                    Project.Description,
                    Project.Notes,
                    Project.created_by,
                    Project.created,
                    Project.modified_by,
                    Project.modified
                from
                    Project 
                    left outer join AccessSeeker on Project.AccessSeekerRecordId = AccessSeeker.id
                 where Project.id = %(Id)s"""
        
        cursor.execute(sql, {"Id": Id})
        
        # Get record
        record = cursor.fetchone()
        
    return record

# ****************************************************************************
# Add a new Project record
# ****************************************************************************
def AddProject(project):
    
    logger.info('Inside AddProject')

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlFieldList = ""
    sqlFieldValues = ""
    sqlQueryParams = {}

    if "AccessSeekerRecordId" in project:
        sqlFieldList += "AccessSeekerRecordId, "
        sqlFieldValues += "%(AccessSeekerRecordId)s, "
        sqlQueryParams["AccessSeekerRecordId"] = project["AccessSeekerRecordId"]
    if "Name" in project:
        sqlFieldList += "Name, "
        sqlFieldValues += "%(Name)s, "
        sqlQueryParams["Name"] = project["Name"]
    if "Org" in project:
        sqlFieldList += "Org, "
        sqlFieldValues += "%(Org)s, "
        sqlQueryParams["Org"] = project["Org"]
    if "Category1" in project:
        sqlFieldList += "Category1, "
        sqlFieldValues += "%(Category1)s, "
        sqlQueryParams["Category1"] = project["Category1"]
    if "Category2" in project:
        sqlFieldList += "Category2, "
        sqlFieldValues += "%(Category2)s, "
        sqlQueryParams["Category2"] = project["Category2"]
    if "Status" in project:
        sqlFieldList += "Status, "
        sqlFieldValues += "%(Status)s, "
        sqlQueryParams["Status"] = project["Status"]
    if "PlannedStartDate" in project:
        sqlFieldList += "PlannedStartDate, "
        sqlFieldValues += "%(PlannedStartDate)s, "
        sqlQueryParams["PlannedStartDate"] = project["PlannedStartDate"]	
    if "ActualStartDate" in project:
        sqlFieldList += "ActualStartDate, "
        sqlFieldValues += "%(ActualStartDate)s, "
        sqlQueryParams["ActualStartDate"] = project["ActualStartDate"]	
    if "PlannedEndDate" in project:
        sqlFieldList += "PlannedEndDate, "
        sqlFieldValues += "%(PlannedEndDate)s, "
        sqlQueryParams["PlannedEndDate"] = project["PlannedEndDate"]	
    if "ActualEndDate" in project:
        sqlFieldList += "ActualEndDate, "
        sqlFieldValues += "%(ActualEndDate)s, "
        sqlQueryParams["ActualEndDate"] = project["ActualEndDate"]		
    if "Description" in project:
        sqlFieldList += "Description, "
        sqlFieldValues += "%(Description)s, "
        sqlQueryParams["Description"] = project["Description"]
    if "Notes" in project:
        sqlFieldList += "Notes, "
        sqlFieldValues += "%(Notes)s, "
        sqlQueryParams["Notes"] = project["Notes"]	
	
    sqlFieldList += "created_by, "
    sqlFieldValues += "%(LoggedInUser)s, "
    sqlFieldList += "created "
    sqlFieldValues += "NOW() "

    sqlQueryParams["LoggedInUser"] = LoggedInUser
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate insert SQL
        sql = "INSERT INTO Project (" + sqlFieldList + ") VALUES (" + sqlFieldValues + ")"

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)
        
        # If record sucessfully added
        if cursor.rowcount > 0:

            # Get the updated record
            addedProject = GetProject(cursor.lastrowid)

        # Else record was not successfully added
        else:
            
            # Get the updated record
            addedProject = None

    return addedProject

# ****************************************************************************
# Update a Project Record
# ****************************************************************************
def UpdateProject(id, patchValues):
    
    logger.info('Inside UpdateProject')

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlQueryParams = {}

    conn = get_connection()
    
    with conn.cursor() as cursor:
               
        # Generate insert SQL
        sql = "UPDATE Project SET "

        if "AccessSeekerRecordId" in patchValues:
            sql += "AccessSeekerRecordId = %(AccessSeekerRecordId)s, "
            sqlQueryParams["AccessSeekerRecordId"] = patchValues["AccessSeekerRecordId"]
        if "Name" in patchValues:
            sql += "Name = %(Name)s, "
            sqlQueryParams["Name"] = patchValues["Name"]
        if "Org" in patchValues:
            sql += "Org = %(Org)s, "
            sqlQueryParams["Org"] = patchValues["Org"]
        if "Category1" in patchValues:
            sql += "Category1 = %(Category1)s, "
            sqlQueryParams["Category1"] = patchValues["Category1"]	
        if "Category2" in patchValues:
            sql += "Category2 = %(Category2)s, "
            sqlQueryParams["Category2"] = patchValues["Category2"]	
        if "Status" in patchValues:
            sql += "Status = %(Status)s, "
            sqlQueryParams["Status"] = patchValues["Status"]	    
        if "PlannedStartDate" in patchValues:
            if patchValues["PlannedStartDate"] != '':
                sql += "PlannedStartDate = %(PlannedStartDate)s, "
                sqlQueryParams["PlannedStartDate"] = patchValues["PlannedStartDate"]
            else:
                sql += "PlannedStartDate = NULL, "
        if "ActualStartDate" in patchValues:
            if patchValues["ActualStartDate"] != '':
                sql += "ActualStartDate = %(ActualStartDate)s, "
                sqlQueryParams["ActualStartDate"] = patchValues["ActualStartDate"]
            else:
                sql += "ActualStartDate = NULL, "
        if "PlannedEndDate" in patchValues:
            if patchValues["PlannedEndDate"] != '':
                sql += "PlannedEndDate = %(PlannedEndDate)s, "
                sqlQueryParams["PlannedEndDate"] = patchValues["PlannedEndDate"]
            else:
                sql += "PlannedEndDate = NULL, "
        if "ActualEndDate" in patchValues:
            if patchValues["ActualEndDate"] != '':
                sql += "ActualEndDate = %(ActualEndDate)s, "
                sqlQueryParams["ActualEndDate"] = patchValues["ActualEndDate"]
            else:
                sql += "ActualEndDate = NULL, "	
        if "Description" in patchValues:
            sql += "Description = %(Description)s, "
            sqlQueryParams["Description"] = patchValues["Description"]	
        if "Notes" in patchValues:
            sql += "Notes = %(Notes)s, "
            sqlQueryParams["Notes"] = patchValues["Notes"]	

        sql += "modified_by = %(LoggedInUser)s, "
        sql += "modified = NOW() "
        sql += "WHERE id = %(Id)s "

        logger.info('UpdateProject SQL: ' + sql)

        sqlQueryParams["Id"] = id
        sqlQueryParams["LoggedInUser"] = LoggedInUser

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)
        
        # If record sucessfully added
        if cursor.rowcount > 0:
            
            # Get the updated record
            updatedProject = GetProject(id)

        # Else record was not successfully updated
        else:
            
            # Get the updated record
            updatedProject = None

    return updatedProject
    
# ****************************************************************************
# Delete a Project Record
# ****************************************************************************
def DeleteProject(request):

    logger.info('Inside DeleteProject')

    # Get the message body which contains the data to update 
    payload = request.json

    # Get the Id of the record to delete    
    Id = payload["id"]
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate delete SQL
        sql = """DELETE FROM Project 
                 WHERE id = %(Id)s"""

        # Execute delete SQL with appropriate parameters
        cursor.execute(sql, {"Id": Id})
        
        # If record sucessfully deleted
        if cursor.rowcount > 0:

            # Generate the success response message
            response = jsonify({ "data": {"id": str(Id) }, "message": "Successfully deleted Project Record: " + str(Id)})

        # Else record was not successfully deleted
        else:
            
            logger.info("Error deleting Project with id = " + str(Id))

            # Generate the error response
            errResponseMsg = json.dumps({"code": 1, "error": "Error deleting Project with id = " + str(Id)})

            # Return a 500 error response
            return Response(errResponseMsg, status=500, mimetype='application/json')
               
    return response


# ****************************************************************************
# Get details of the logged in user
# Note: This should be retrieved from the Access Token, not querystring params
# ****************************************************************************
def GetLoggedInUser(request):
    
    logger.info('Inside GetLoggedInUser')
    
    # Initialise variables
    userName = 'Undefined'

    # Get the username from the user profile in the querystring parameters
    if request.args.get('userProfile'):
        userProfile = json.loads(request.args.get('userProfile'))
        userName = userProfile.get('userName')
    
    return userName
