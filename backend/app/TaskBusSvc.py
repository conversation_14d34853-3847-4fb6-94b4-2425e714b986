# ****************************************************************************
# Author: Name
# Desc: Business Services for Task
#
#   Record Manager functions
#   - GetTasks           --> Get List
#   - GetTask            --> Get Record
#   - AddTask            --> Add Record
#   - UpdateTask         --> Update Record
#   - DeleteTask         --> Delete Record
#
#   Other functions for the Business Service
#   - BusinessFunction1
#   - BusinessFunction2
#
# ****************************************************************************

from flask import request
from flask import Response
from flask import jsonify
from DBHandler import get_connection
from AGGridServices import generateWhereSQL
from AGGridServices import generateOrderBySQL
from AGGridServices import generateLimitOffsetSQL
import logging
import json

import datetime

logger = logging.getLogger()

# ****************************************************************************
# Get List of <Entity> records (e.g. GetAssetCapJobs)
# ****************************************************************************
def GetTasks(request):

    logger.info('Inside GetTasks')

    # Get the payload which contains the parameters to query data 
    # including AG Grid filter model, sort model and paging details
    payload = request.json
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        sql = """select
                    SQL_CALC_FOUND_ROWS 
                    Task.id,
                    Task.AccessSeekerRecordId,
                    AccessSeeker.AccessSeekerId,
                    Task.Title,
                    Task.Type,
                    Task.Status,
                    Task.AssignedTo,
                    Task.DueDate,
                    Task.CompletedDate,
                    Task.created_by,
                    Task.created,
                    Task.modified_by,
                    Task.modified
                from
                    Task 
                    left outer join AccessSeeker on Task.AccessSeekerRecordId = AccessSeeker.id"""

        # Select records based on the filtering criteria
        sql = sql + generateWhereSQL(payload)
        
        # Sort records based on the sort criteria
        sql = sql + generateOrderBySQL(payload)
        
        # Limit the results based on the paging parameters
        sql = sql + generateLimitOffsetSQL(payload)        

        cursor.execute(sql)
        records = cursor.fetchall()
        
        # Get the total number of records
        sql = "SELECT FOUND_ROWS() as totalRecords"
        cursor.execute(sql)
        totalRecordsResult = cursor.fetchone()
        totalRecords = totalRecordsResult['totalRecords']  
        
    response = jsonify({"totalRecords": totalRecords, "records": records})
    return response

# ****************************************************************************
# Get a specific Task Record
# ****************************************************************************
def GetTask(Id):

    logger.info('Inside GetTask. Id: ' + str(Id))

    conn = get_connection()
    
    with conn.cursor() as cursor:

        sql = """select
                    Task.id,
                    Task.AccessSeekerRecordId,
                    AccessSeeker.AccessSeekerId,
                    Task.Title,
                    Task.Type,
                    Task.Status,
                    Task.AssignedTo,
                    DATE_FORMAT(Task.DueDate, '%%Y-%%m-%%d') as DueDate,
                    DATE_FORMAT(Task.CompletedDate, '%%Y-%%m-%%d') as CompletedDate,
                    Task.Notes,
                    Task.created_by,
                    Task.created,
                    Task.modified_by,
                    Task.modified
                from
                    Task 
                    left outer join AccessSeeker on Task.AccessSeekerRecordId = AccessSeeker.id
                 where Task.id = %(Id)s"""
        
        cursor.execute(sql, {"Id": Id})
        
        # Get record
        record = cursor.fetchone()

        logging.info('GetTask Record: ' + str(record))
        
    return record

# ****************************************************************************
# Add a new Task record
# ****************************************************************************
def AddTask(task):
    
    logger.info('Inside AddTask')

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlFieldList = ""
    sqlFieldValues = ""
    sqlQueryParams = {}

    if "AccessSeekerRecordId" in task:
        sqlFieldList += "AccessSeekerRecordId, "
        sqlFieldValues += "%(AccessSeekerRecordId)s, "
        sqlQueryParams["AccessSeekerRecordId"] = task["AccessSeekerRecordId"]
    if "Title" in task:
        sqlFieldList += "Title, "
        sqlFieldValues += "%(Title)s, "
        sqlQueryParams["Title"] = task["Title"]
    if "Type" in task:
        sqlFieldList += "Type, "
        sqlFieldValues += "%(Type)s, "
        sqlQueryParams["Type"] = task["Type"]
    if "Status" in task:
        sqlFieldList += "Status, "
        sqlFieldValues += "%(Status)s, "
        sqlQueryParams["Status"] = task["Status"]
    if "AssignedTo" in task:
        sqlFieldList += "AssignedTo, "
        sqlFieldValues += "%(AssignedTo)s, "
        sqlQueryParams["AssignedTo"] = task["AssignedTo"]
    if "DueDate" in task:
        sqlFieldList += "DueDate, "
        sqlFieldValues += "%(DueDate)s, "
        sqlQueryParams["DueDate"] = task["DueDate"].strftime('%Y-%m-%d')
    if "CompletedDate" in task:
        sqlFieldList += "CompletedDate, "
        sqlFieldValues += "%(CompletedDate)s, "
        sqlQueryParams["CompletedDate"] = task["CompletedDate"]
    if "Notes" in task:
        sqlFieldList += "Notes, "
        sqlFieldValues += "%(Notes)s, "
        sqlQueryParams["Notes"] = task["Notes"]

    sqlFieldList += "created_by, "
    sqlFieldValues += "%(LoggedInUser)s, "
    sqlFieldList += "created "
    sqlFieldValues += "NOW() "

    sqlQueryParams["LoggedInUser"] = LoggedInUser
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate insert SQL
        sql = "INSERT INTO Task (" + sqlFieldList + ") VALUES (" + sqlFieldValues + ")"

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)
        
        # If record sucessfully added
        if cursor.rowcount > 0:

            # Get the updated record
            addedTask = GetTask(cursor.lastrowid)

        # Else record was not successfully added
        else:
            
            # Get the updated record
            addedTask = None

    return addedTask

# ****************************************************************************
# Update a Task Record
# ****************************************************************************
def UpdateTask(id, patchValues):
    
    logger.info('Inside UpdateTask')

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlQueryParams = {}

    logging.info('UpdateTask request: ' + str(request))

    conn = get_connection()
    
    with conn.cursor() as cursor:
               
        # Generate insert SQL
        sql = "UPDATE Task SET "

        if "AccessSeekerRecordId" in patchValues:
            sql += "AccessSeekerRecordId = %(AccessSeekerRecordId)s, "
            sqlQueryParams["AccessSeekerRecordId"] = patchValues["AccessSeekerRecordId"]
        if "Title" in patchValues:
            sql += "Title = %(Title)s, "
            sqlQueryParams["Title"] = patchValues["Title"]
        if "Type" in patchValues:
            sql += "Type = %(Type)s, "
            sqlQueryParams["Type"] = patchValues["Type"]
        if "Status" in patchValues:
            sql += "Status = %(Status)s, "
            sqlQueryParams["Status"] = patchValues["Status"]
        if "AssignedTo" in patchValues:
            sql += "AssignedTo = %(AssignedTo)s, "
            sqlQueryParams["AssignedTo"] = patchValues["AssignedTo"]
        if "DueDate" in patchValues:
            if patchValues["DueDate"] != '':
                sql += "DueDate = %(DueDate)s, "
                sqlQueryParams["DueDate"] = patchValues["DueDate"]
            else:
                sql += "DueDate = NULL, "
        if "CompletedDate" in patchValues:
            if patchValues["CompletedDate"] != '':
                sql += "CompletedDate = %(CompletedDate)s, "
                sqlQueryParams["CompletedDate"] = patchValues["CompletedDate"]
            else:
                sql += "CompletedDate = NULL, "
        if "Notes" in patchValues:
            sql += "Notes = %(Notes)s, "
            sqlQueryParams["Notes"] = patchValues["Notes"]


        sql += "modified_by = %(LoggedInUser)s, "
        sql += "modified = NOW() "
        sql += "WHERE id = %(Id)s "

        logger.info('UpdateTask SQL: ' + sql)

        sqlQueryParams["Id"] = id
        sqlQueryParams["LoggedInUser"] = LoggedInUser

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)
        
        # If record sucessfully added
        if cursor.rowcount > 0:
            
            # Get the updated record
            updatedTask = GetTask(id)

        # Else record was not successfully updated
        else:
            
            # Get the updated record
            updatedTask = None

    return updatedTask
    
# ****************************************************************************
# Delete a Task Record
# ****************************************************************************
def DeleteTask(request):

    logger.info('Inside DeleteTask')

    # Get the message body which contains the data to update 
    payload = request.json

    # Get the Id of the record to delete    
    Id = payload["id"]
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate delete SQL
        sql = """DELETE FROM Task 
                 WHERE id = %(Id)s"""

        # Execute delete SQL with appropriate parameters
        cursor.execute(sql, {"Id": Id})
        
        # If record sucessfully deleted
        if cursor.rowcount > 0:

            # Generate the success response message
            response = jsonify({ "data": {"id": str(Id) }, "message": "Successfully deleted Task Record: " + str(Id)})

        # Else record was not successfully deleted
        else:
            
            logger.info("Error deleting Task with id = " + str(Id))

            # Generate the error response
            errResponseMsg = json.dumps({"code": 1, "error": "Error deleting Task with id = " + str(Id)})

            # Return a 500 error response
            return Response(errResponseMsg, status=500, mimetype='application/json')
               
    return response


# ****************************************************************************
# Get details of the logged in user
# Note: This should be retrieved from the Access Token, not querystring params
# ****************************************************************************
def GetLoggedInUser(request):
    
    logger.info('Inside GetLoggedInUser')
    
    # Initialise variables
    userName = 'Undefined'

    # Get the username from the user profile in the querystring parameters
    if request.args.get('userProfile'):
        userProfile = json.loads(request.args.get('userProfile'))
        userName = userProfile.get('userName')
    
    return userName



