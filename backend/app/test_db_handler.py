from DBHandler import DBHandler
import os
from dotenv import load_dotenv

def test_db_handler():
    # Verify environment variables are loaded
    load_dotenv()
    print("\nVerifying connection parameters:")
    print(f"DB_HOST: {os.getenv('DB_HOST')}")
    print(f"DB_PORT: {os.getenv('DB_PORT')}")
    print(f"DB_USER: {os.getenv('DB_USER')}")
    print(f"DB_NAME: {os.getenv('DB_NAME')}")
    print(f"DB_PASSWORD: {'*' * len(os.getenv('DB_PASSWORD') or '')}")  # Hide actual password
    
    db = DBHandler()
    
    print("\nTesting database connection and operations...")
    
    # Test 1: List databases
    print("\n1. Testing database listing:")
    databases = db.execute_query("SELECT datname FROM pg_database")
    print("Available databases:")
    for database in databases:
        print(f"- {database['datname']}")
    
    # Test 2: Get PostgreSQL version
    print("\n2. Testing version query:")
    version = db.execute_single("SELECT version()")
    print(f"PostgreSQL version: {version['version']}")
    
    # Test 3: Test transaction handling
    print("\n3. Testing transaction handling:")
    try:
        # First create the table
        db.execute("""
            CREATE TEMP TABLE test_table (
                id SERIAL PRIMARY KEY,
                name VARCHAR(50)
            )
        """)
        
        # Then insert data in a separate transaction
        db.execute_many(
            "INSERT INTO test_table (name) VALUES (%s)",
            [('Test 1',), ('Test 2',), ('Test 3',)]
        )
        
        # Verify the data in a separate query
        results = db.execute_query("SELECT * FROM test_table ORDER BY id")
        if results:
            print("Inserted data:")
            for row in results:
                print(f"- ID: {row['id']}, Name: {row['name']}")
        else:
            print("No data found in test_table")
            
    except Exception as e:
        print(f"Error during transaction test: {str(e)}")
    finally:
        # Cleanup: Drop the temporary table
        try:
            db.execute("DROP TABLE IF EXISTS test_table")
            print("\nCleanup completed: Temporary table dropped")
        except Exception as e:
            print(f"Error during cleanup: {str(e)}")
    
    print("\nAll tests completed!")

if __name__ == "__main__":
    test_db_handler() 