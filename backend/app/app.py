import json
import logging
import logging.config

from config import get_app_config
from util import load_yaml_file
from flask import Flask, request, Response, jsonify
from flask_cors import CORS


from AccessSeekerBusSvc import GetAccessSeekers, GetAccessSeeker, AddAccessSeeker, UpdateAccessSeeker
from NoteBusSvc import GetNotes, GetNote, AddNote, UpdateNote
from ContactBusSvc import GetContacts, GetContact, AddContact, UpdateContact
from TaskBusSvc import GetTasks, GetTask, AddTask, UpdateTask
from ProjectBusSvc import GetProjects, GetProject, AddProject, UpdateProject
from DigitalSvcBusSvc import GetDigitalSvcs, GetDigitalSvc, AddDigitalSvc, UpdateDigitalSvc
from DigitalSvcVersionBusSvc import GetDigitalSvcVersions, GetDigitalSvcVersion, AddDigitalSvcVersion, UpdateDigitalSvcVersion
from DigitalUsageChartBusSvc import GetDigitalUsageHistory, GetDigitalUsageByRSP, GetDigitalUsageByService, GetDigitalUsageForRSP, GetDigitalUsageForServices


# ****************************************************************************
# Initialise the app
# ****************************************************************************

# Load configuration
app_config = get_app_config()

# Setup logging
logging.config.dictConfig(load_yaml_file(app_config.logging_config_path))

# Setup allowed Origins
AllowedOrigins = app_config.cors_origins.split(",")

# Setup the app and basic CORs settings
app = Flask(__name__)
cors = CORS(app, resources={r"/*": {"origins": AllowedOrigins}})

# ****************************************************************************
# Setup Error Handlers
# ****************************************************************************

# Handler for 404 errors
@app.errorhandler(404)
def page_not_found(e):

    # log the exception
    logging.exception('Route not found: ' + repr(e))
    return Response('Route not found', status=404, mimetype='application/json')

# Handler for unhandled exceptions
@app.errorhandler(Exception)
def handle_exception(e):

    # log the exception
    logging.exception('Unhandled error: ' + repr(e))

# ****************************************************************************
# Route handlers for general APIs (remove later?)
# ****************************************************************************

# Handle the / route
@app.route('/')
def hello_world():
    app.logger.info('Testing logging')
    return 'Hello, World'

# ****************************************************************************
# Route handlers for health checks
# ****************************************************************************

# Handle the / route
@app.route('/healthz')
def health():
    return 'Ok'

# ****************************************************************************
# Route handlers for the Access Seeker APIs
# ****************************************************************************

# Handle the /Test/AccessSeekerList route (GetList)
@app.route('/Test/AccessSeekerList', methods=['POST'])

def APIGetAccessSeekers():

    return GetAccessSeekers(request)

# Handle the /Test/AccessSeeker/<Id> route (GetRecord)
@app.route('/Test/AccessSeeker/<Id>', methods=['GET'])

def APIGetAccessSeeker(Id):

    AccessSeeker = GetAccessSeeker(Id)
    
    # Generate response message containing record
    response = jsonify({"data": AccessSeeker})

    return response

# Handle the /Test/AccessSeeker route (CreateRecord)
@app.route('/Test/AccessSeeker', methods=['POST'])

def APIAddAccessSeeker():

    # Get the message body which contains the data to update 
    accessSeeker = request.json

    # Add the Access Seeker Record
    AddedAccessSeeker = AddAccessSeeker(accessSeeker)   

    # Generate response message containing record
    response = jsonify({"data": AddedAccessSeeker})

    return response

# Handle the /Test/AccessSeeker/<Id> route (UpdateRecord)
@app.route('/Test/AccessSeeker/<Id>', methods=['PATCH'])

def APIUpdateAccessSeeker(Id):

    # Get the message body which contains the data to update 
    patchValues = request.json

    # Update the Access Seeker Record
    UpdatedAccessSeeker = UpdateAccessSeeker(Id, patchValues)

    if UpdatedAccessSeeker:

        # Generate response message containing record
        response = jsonify({"data": UpdatedAccessSeeker})

    else:

        # Generate the error response
        errResponseMsg = json.dumps({"code": 1, "error": "Couldn't find AccessSeeker with id = " + Id})

        # Generate the 400 error response
        response = Response(errResponseMsg, status=400, mimetype='application/json')

    return response


# ****************************************************************************
# Route handlers for the Note APIs
# ****************************************************************************

# Handle the /Test/NoteList route (GetList)
@app.route('/Test/NoteList', methods=['POST'])

def APIGetNotes():

    return GetNotes(request)

# Handle the /Test/Note/<Id> route (GetRecord)
@app.route('/Test/Note/<Id>', methods=['GET'])

def APIGetNote(Id):

    Note = GetNote(Id)
    
    # Generate response message containing record
    response = jsonify({"data": Note})

    return response

# Handle the /Test/Note route (CreateRecord)
@app.route('/Test/Note', methods=['POST'])

def APIAddNote():

    # Get the message body which contains the data to update 
    note = request.json

    # Add the Note Record
    AddedNote = AddNote(note)   

    # Generate response message containing record
    response = jsonify({"data": AddedNote})

    return response

# Handle the /Test/Note/<Id> route (UpdateRecord)
@app.route('/Test/Note/<Id>', methods=['PATCH'])

def APIUpdateNote(Id):

    # Get the message body which contains the data to update 
    patchValues = request.json

    # Update the Note Record
    UpdatedNote = UpdateNote(Id, patchValues)

    if UpdatedNote:

        # Generate response message containing record
        response = jsonify({"data": UpdatedNote})

    else:

        # Generate the error response
        errResponseMsg = json.dumps({"code": 1, "error": "Couldn't find Note with id = " + Id})

        # Generate the 400 error response
        response = Response(errResponseMsg, status=400, mimetype='application/json')

    return response


# ****************************************************************************
# Route handlers for the Contact APIs
# ****************************************************************************

# Handle the /Test/ContactList route (GetList)
@app.route('/Test/ContactList', methods=['POST'])

def APIGetContacts():

    return GetContacts(request)

# Handle the /Test/Contact/<Id> route (GetRecord)
@app.route('/Test/Contact/<Id>', methods=['GET'])

def APIGetContact(Id):

    Contact = GetContact(Id)
    
    # Generate response message containing record
    response = jsonify({"data": Contact})

    return response

# Handle the /Test/Contact route (CreateRecord)
@app.route('/Test/Contact', methods=['POST'])

def APIAddContact():

    # Get the message body which contains the data to update 
    contact = request.json

    # Add the Contact Record
    AddedContact = AddContact(contact)   

    # Generate response message containing record
    response = jsonify({"data": AddedContact})

    return response

# Handle the /Test/Contact/<Id> route (UpdateRecord)
@app.route('/Test/Contact/<Id>', methods=['PATCH'])

def APIUpdateContact(Id):

    # Get the message body which contains the data to update 
    patchValues = request.json

    # Update the Contact Record
    UpdatedContact = UpdateContact(Id, patchValues)

    if UpdatedContact:

        # Generate response message containing record
        response = jsonify({"data": UpdatedContact})

    else:

        # Generate the error response
        errResponseMsg = json.dumps({"code": 1, "error": "Couldn't find Contact with id = " + Id})

        # Generate the 400 error response
        response = Response(errResponseMsg, status=400, mimetype='application/json')

    return response


# ****************************************************************************
# Route handlers for the Task APIs
# ****************************************************************************

# Handle the /Test/TaskList route (GetList)
@app.route('/Test/TaskList', methods=['POST'])

def APIGetTasks():

    return GetTasks(request)

# Handle the /Test/Task/<Id> route (GetRecord)
@app.route('/Test/Task/<Id>', methods=['GET'])

def APIGetTask(Id):

    Task = GetTask(Id)
    
    # Generate response message containing record
#    response = jsonify({"data": Task})

    response = jsonify({"data": Task})

    return response

# Handle the /Test/Task route (CreateRecord)
@app.route('/Test/Task', methods=['POST'])

def APIAddTask():

    # Get the message body which contains the data to update 
    task = request.json

    # Add the Task Record
    AddedTask = AddTask(task)   

    # Generate response message containing record
    response = jsonify({"data": AddedTask})

    return response

# Handle the /Test/Task/<Id> route (UpdateRecord)
@app.route('/Test/Task/<Id>', methods=['PATCH'])

def APIUpdateTask(Id):

    # Get the message body which contains the data to update 
    patchValues = request.json

    # Update the Task Record
    UpdatedTask = UpdateTask(Id, patchValues)

    if UpdatedTask:

        # Generate response message containing record
        response = jsonify({"data": UpdatedTask})

    else:

        # Generate the error response
        errResponseMsg = json.dumps({"code": 1, "error": "Couldn't find Task with id = " + Id})

        # Generate the 400 error response
        response = Response(errResponseMsg, status=400, mimetype='application/json')

    return response


# ****************************************************************************
# Route handlers for the Project APIs
# ****************************************************************************

# Handle the /Test/ProjectList route (GetList)
@app.route('/Test/ProjectList', methods=['POST'])

def APIGetProjects():

    return GetProjects(request)

# Handle the /Test/Project/<Id> route (GetRecord)
@app.route('/Test/Project/<Id>', methods=['GET'])

def APIGetProject(Id):

    Project = GetProject(Id)
    
    # Generate response message containing record
    response = jsonify({"data": Project})

    return response

# Handle the /Test/Project route (CreateRecord)
@app.route('/Test/Project', methods=['POST'])

def APIAddProject():

    # Get the message body which contains the data to update 
    project = request.json

    # Add the Project Record
    AddedProject = AddProject(project)   

    # Generate response message containing record
    response = jsonify({"data": AddedProject})

    return response

# Handle the /Test/Project/<Id> route (UpdateRecord)
@app.route('/Test/Project/<Id>', methods=['PATCH'])

def APIUpdateProject(Id):

    # Get the message body which contains the data to update 
    patchValues = request.json

    # Update the Project Record
    UpdatedProject = UpdateProject(Id, patchValues)

    if UpdatedProject:

        # Generate response message containing record
        response = jsonify({"data": UpdatedProject})

    else:

        # Generate the error response
        errResponseMsg = json.dumps({"code": 1, "error": "Couldn't find Project with id = " + Id})

        # Generate the 400 error response
        response = Response(errResponseMsg, status=400, mimetype='application/json')

    return response


# ****************************************************************************
# Route handlers for the DigitalSvc APIs
# ****************************************************************************

# Handle the /Test/DigitalSvcList route (GetList)
@app.route('/Test/DigitalSvcList', methods=['POST'])

def APIGetDigitalSvcs():

    return GetDigitalSvcs(request)

# Handle the /Test/DigitalSvc/<Id> route (GetRecord)
@app.route('/Test/DigitalSvc/<Id>', methods=['GET'])

def APIGetDigitalSvc(Id):

    DigitalSvc = GetDigitalSvc(Id)
    
    # Generate response message containing record
    response = jsonify({"data": DigitalSvc})

    return response

# Handle the /Test/DigitalSvc route (CreateRecord)
@app.route('/Test/DigitalSvc', methods=['POST'])

def APIAddDigitalSvc():

    # Get the message body which contains the data to update 
    digitalSvc = request.json

    # Add the DigitalSvc Record
    AddedDigitalSvc = AddDigitalSvc(digitalSvc)   

    # Generate response message containing record
    response = jsonify({"data": AddedDigitalSvc})

    return response

# Handle the /Test/DigitalSvc/<Id> route (UpdateRecord)
@app.route('/Test/DigitalSvc/<Id>', methods=['PATCH'])

def APIUpdateDigitalSvc(Id):

    # Get the message body which contains the data to update 
    patchValues = request.json

    # Update the DigitalSvc Record
    UpdatedDigitalSvc = UpdateDigitalSvc(Id, patchValues)

    if UpdatedDigitalSvc:

        # Generate response message containing record
        response = jsonify({"data": UpdatedDigitalSvc})

    else:

        # Generate the error response
        errResponseMsg = json.dumps({"code": 1, "error": "Couldn't find DigitalSvc with id = " + Id})

        # Generate the 400 error response
        response = Response(errResponseMsg, status=400, mimetype='application/json')

    return response


# ****************************************************************************
# Route handlers for the DigitalSvcVersion APIs
# ****************************************************************************

# Handle the /Test/DigitalSvcVersionList route (GetList)
@app.route('/Test/DigitalSvcVersionList', methods=['POST'])

def APIGetDigitalSvcVersions():

    return GetDigitalSvcVersions(request)

# Handle the /Test/DigitalSvcVersion/<Id> route (GetRecord)
@app.route('/Test/DigitalSvcVersion/<Id>', methods=['GET'])

def APIGetDigitalSvcVersion(Id):

    DigitalSvcVersion = GetDigitalSvcVersion(Id)
    
    # Generate response message containing record
    response = jsonify({"data": DigitalSvcVersion})

    return response

# Handle the /Test/DigitalSvcVersion route (CreateRecord)
@app.route('/Test/DigitalSvcVersion', methods=['POST'])

def APIAddDigitalSvcVersion():

    # Get the message body which contains the data to update 
    digitalSvcVersion = request.json

    # Add the DigitalSvcVersion Record
    AddedDigitalSvcVersion = AddDigitalSvcVersion(digitalSvcVersion)   

    # Generate response message containing record
    response = jsonify({"data": AddedDigitalSvcVersion})

    return response

# Handle the /Test/DigitalSvcVersion/<Id> route (UpdateRecord)
@app.route('/Test/DigitalSvcVersion/<Id>', methods=['PATCH'])

def APIUpdateDigitalSvcVersion(Id):

    # Get the message body which contains the data to update 
    patchValues = request.json

    # Update the DigitalSvcVersion Record
    UpdatedDigitalSvcVersion = UpdateDigitalSvcVersion(Id, patchValues)

    if UpdatedDigitalSvcVersion:

        # Generate response message containing record
        response = jsonify({"data": UpdatedDigitalSvcVersion})

    else:

        # Generate the error response
        errResponseMsg = json.dumps({"code": 1, "error": "Couldn't find DigitalSvcVersion with id = " + Id})

        # Generate the 400 error response
        response = Response(errResponseMsg, status=400, mimetype='application/json')

    return response


# ****************************************************************************
# Route handlers for the Digital Usage Dashboards
# ****************************************************************************

# Handle the /Test/digitalusagehistory route
@app.route('/Test/digitalusagehistory', methods=['GET'])

def APIGetDigitalUsageHistory():
    return GetDigitalUsageHistory()

# Handle the /Test/digitalusagebyrsp route
@app.route('/Test/digitalusagebyrsp', methods=['GET'])

def APIGetDigitalUsageByRSP():

    # Retrieve the query string parameter 'period' from the request
    period = request.args.get('period', default=None, type=str)

    return GetDigitalUsageByRSP(period)

# Handle the /Test/digitalusagebyservice route
@app.route('/Test/digitalusagebyservice', methods=['GET'])

def APIGetDigitalUsageByService():

    # Retrieve the query string parameter 'period' from the request
    period = request.args.get('period', default=None, type=str)

    return GetDigitalUsageByService(period)

# Handle the /Test/digitalusageforrsp route
@app.route('/Test/digitalusageforrsp', methods=['GET'])

def APIGetDigitalUsageForRSP():

    # Retrieve the query string parameter 'period' from the request
    accessSeekerId = request.args.get('accessSeekerId', default=None, type=str)

    return GetDigitalUsageForRSP(accessSeekerId)


# Handle the /Test/digitalusageforservices route
@app.route('/Test/digitalusageforservices', methods=['GET'])

def APIGetDigitalUsageForServices():

    return GetDigitalUsageForServices()

if __name__ == '__main__':
    flask