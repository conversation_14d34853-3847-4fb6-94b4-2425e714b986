# ****************************************************************************
# Author: Name
# Desc: Business Services for DigitalSvc
#
#   Record Manager functions
#   - GetDigitalSvcVersions           --> Get List
#   - GetDigitalSvcVersion            --> Get Record
#   - AddDigitalSvcVersion            --> Add Record
#   - UpdateDigitalSvcVersion         --> Update Record
#   - DeleteDigitalSvcVersion         --> Delete Record
#
#   Other functions for the Business Service
#   - BusinessFunction1
#   - BusinessFunction2
#
# ****************************************************************************

from flask import request
from flask import Response
from flask import jsonify
from DBHandler import get_connection
from AGGridServices import generateWhereSQL
from AGGridServices import generateOrderBySQL
from AGGridServices import generateLimitOffsetSQL
import logging
import json

logger = logging.getLogger()

# ****************************************************************************
# Get List of DigitalSvcVersions records
# ****************************************************************************
def GetDigitalSvcVersions(request):

    logger.info('Inside GetDigitalSvcVersions')

    # Get the payload which contains the parameters to query data 
    # including AG Grid filter model, sort model and paging details
    payload = request.json
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        sql = """select
                    SQL_CALC_FOUND_ROWS 
                    dsv.id,
                    dsv.DigitalServiceRecordId,
                    ds.ServiceName,
                    ds.APIName,
                    dsv.Version,
                    dsv.Status,
                    dsv.ReleaseDate,
                    dsv.created_by,
                    dsv.created,
                    dsv.modified_by,
                    dsv.modified
                from
                    DigitalServiceVersion dsv
                left join DigitalService ds on dsv.DigitalServiceRecordId = ds.id """

        # Select records based on the filtering criteria
        sql = sql + generateWhereSQL(payload)
        
        # Sort records based on the sort criteria
        sql = sql + generateOrderBySQL(payload)
        
        # Limit the results based on the paging parameters
        sql = sql + generateLimitOffsetSQL(payload)        

        cursor.execute(sql)
        records = cursor.fetchall()
        
        # Get the total number of records
        sql = "SELECT FOUND_ROWS() as totalRecords"
        cursor.execute(sql)
        totalRecordsResult = cursor.fetchone()
        totalRecords = totalRecordsResult['totalRecords']  
        
    response = jsonify({"totalRecords": totalRecords, "records": records})
    return response

# ****************************************************************************
# Get a specific DigitalSvcVersion Record
# ****************************************************************************
def GetDigitalSvcVersion(Id):

    logger.info('Inside GetDigitalSvcVersion. Id: ' + str(Id))

    conn = get_connection()
    
    with conn.cursor() as cursor:

        sql = """select
                    dsv.id,
                    dsv.DigitalServiceRecordId,
                    ds.ServiceName,
                    ds.APIName,
                    dsv.Version,
                    dsv.Status,
                    dsv.ReleaseDate,
                    dsv.Description,
                    dsv.created_by,
                    dsv.created,
                    dsv.modified_by,
                    dsv.modified
                from
                    DigitalServiceVersion dsv
                left join DigitalService ds on dsv.DigitalServiceRecordId = ds.id
                where dsv.id = %(Id)s"""
        
        cursor.execute(sql, {"Id": Id})
        
        # Get record
        record = cursor.fetchone()
        
    return record

# ****************************************************************************
# Add a new DigitalSvcVersion record
# ****************************************************************************
def AddDigitalSvcVersion(digitalSvcVersion):
    
    logger.info('Inside AddDigitalSvcVersion')

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlFieldList = ""
    sqlFieldValues = ""
    sqlQueryParams = {}

    if "DigitalServiceRecordId" in digitalSvcVersion:
        sqlFieldList += "DigitalServiceRecordId, "
        sqlFieldValues += "%(DigitalServiceRecordId)s, "
        sqlQueryParams["DigitalServiceRecordId"] = digitalSvcVersion["DigitalServiceRecordId"]
    if "Version" in digitalSvcVersion:
        sqlFieldList += "Version, "
        sqlFieldValues += "%(Version)s, "
        sqlQueryParams["Version"] = digitalSvcVersion["Version"]	
    if "Status" in digitalSvcVersion:
        sqlFieldList += "Status, "
        sqlFieldValues += "%(Status)s, "
        sqlQueryParams["Status"] = digitalSvcVersion["Status"]
    if "ReleaseDate" in digitalSvcVersion:
        sqlFieldList += "ReleaseDate, "
        sqlFieldValues += "%(ReleaseDate)s, "
        sqlQueryParams["ReleaseDate"] = digitalSvcVersion["ReleaseDate"]
    if "Description" in digitalSvcVersion:
        sqlFieldList += "Description, "
        sqlFieldValues += "%(Description)s, "
        sqlQueryParams["Description"] = digitalSvcVersion["Description"]	
	
    sqlFieldList += "created_by, "
    sqlFieldValues += "%(LoggedInUser)s, "
    sqlFieldList += "created "
    sqlFieldValues += "NOW() "

    sqlQueryParams["LoggedInUser"] = LoggedInUser
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate insert SQL
        sql = "INSERT INTO DigitalServiceVersion (" + sqlFieldList + ") VALUES (" + sqlFieldValues + ")"

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)
        
        # If record sucessfully added
        if cursor.rowcount > 0:
            
            # Get the updated record
            addedDigitalSvcVersion = GetDigitalSvcVersion(cursor.lastrowid)

        # Else record was not successfully added
        else:
            
            # Get the updated record
            addedDigitalSvcVersion = None

    return addedDigitalSvcVersion

# ****************************************************************************
# Update a DigitalSvcVersion Record
# ****************************************************************************
def UpdateDigitalSvcVersion(id, patchValues):
    
    logger.info('Inside UpdateDigitalSvcVersion')

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlQueryParams = {}

    conn = get_connection()
    
    with conn.cursor() as cursor:
               
        # Generate insert SQL
        sql = "UPDATE DigitalServiceVersion SET "

        if "DigitalServiceRecordId" in patchValues:
            sql += "DigitalServiceRecordId = %(DigitalServiceRecordId)s, "
            sqlQueryParams["DigitalServiceRecordId"] = patchValues["DigitalServiceRecordId"]
        if "Version" in patchValues:
            sql += "Version = %(Version)s, "
            sqlQueryParams["Version"] = patchValues["Version"]
        if "Status" in patchValues:
            sql += "Status = %(Status)s, "
            sqlQueryParams["Status"] = patchValues["Status"]	
        if "ReleaseDate" in patchValues:
            sql += "ReleaseDate = %(ReleaseDate)s, "
            sqlQueryParams["ReleaseDate"] = patchValues["ReleaseDate"]	
        if "Description" in patchValues:
            sql += "Description = %(Description)s, "
            sqlQueryParams["Description"] = patchValues["Description"]	
	
        sql += "modified_by = %(LoggedInUser)s, "
        sql += "modified = NOW() "
        sql += "WHERE id = %(Id)s "

        logger.info('UpdateDigitalSvcVersion SQL: ' + sql)

        sqlQueryParams["Id"] = id
        sqlQueryParams["LoggedInUser"] = LoggedInUser

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)
        
        # If record sucessfully added
        if cursor.rowcount > 0:
            
            # Get the updated record
            updatedDigitalSvcVersion = GetDigitalSvcVersion(id)

        # Else record was not successfully updated
        else:
            
            # Get the updated record
            updatedDigitalSvcVersion = None

    return updatedDigitalSvcVersion
    
# ****************************************************************************
# Delete an DigitalSvcVersion Record
# ****************************************************************************
def DeleteDigitalSvcVersion(request):

    logger.info('Inside DeleteDigitalSvcVersion')

    # Get the message body which contains the data to update 
    payload = request.json

    # Get the Id of the record to delete    
    Id = payload["id"]
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate delete SQL
        sql = """DELETE FROM DigitalServiceVersion 
                 WHERE id = %(Id)s"""

        # Execute delete SQL with appropriate parameters
        cursor.execute(sql, {"Id": Id})
        
        # If record sucessfully deleted
        if cursor.rowcount > 0:

            # Generate the success response message
            response = jsonify({ "data": {"id": str(Id) }, "message": "Successfully deleted DigitalSvcVersion Record: " + str(Id)})

        # Else record was not successfully deleted
        else:
            
            logger.info("Error deleting DigitalSvcVersion with id = " + str(Id))

            # Generate the error response
            errResponseMsg = json.dumps({"code": 1, "error": "Error deleting DigitalSvcVersion with id = " + str(Id)})

            # Return a 500 error response
            return Response(errResponseMsg, status=500, mimetype='application/json')
               
    return response


# ****************************************************************************
# Get details of the logged in user
# Note: This should be retrieved from the Access Token, not querystring params
# ****************************************************************************
def GetLoggedInUser(request):
    
    logger.info('Inside GetLoggedInUser')
    
    # Initialise variables
    userName = 'Undefined'

    # Get the username from the user profile in the querystring parameters
    if request.args.get('userProfile'):
        userProfile = json.loads(request.args.get('userProfile'))
        userName = userProfile.get('userName')
    
    return userName
