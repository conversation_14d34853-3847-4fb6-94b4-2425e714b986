# ****************************************************************************
# Author: Name
# Desc: Business Services for AccessSeeker
#
#   Record Manager functions
#   - GetAccessSeekers           --> Get List
#   - GetAccessSeeker            --> Get Record
#   - AddAccessSeeker            --> Add Record
#   - UpdateAccessSeeker         --> Update Record
#   - DeleteAccessSeeker         --> Delete Record
#   - Get<Entity><ChildEntity1>  --> Get Child records for an entity
#   - Get<Entity><ChildEntity2>  --> Get Child records for an entity
#
#   Other functions for the Business Service
#   - BusinessFunction1
#   - BusinessFunction2
#
# ****************************************************************************

from flask import request
from flask import Response
from flask import jsonify
from DBHandler import get_connection
from AGGridServices import generateWhereSQL
from AGGridServices import generateOrderBySQL
from AGGridServices import generateLimitOffsetSQL
import logging
import json

logger = logging.getLogger()

# ****************************************************************************
# Get List of <Entity> records (e.g. GetAssetCapJobs)
# ****************************************************************************
def GetAccessSeekers(request):

    logger.info('Inside GetAccessSeekers')

    # Get the payload which contains the parameters to query data 
    # including AG Grid filter model, sort model and paging details
    payload = request.json
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        sql = """select
                    SQL_CALC_FOUND_ROWS 
                    AccessSeeker.id,
                    AccessSeeker.AccessSeekerId,
                    AccessSeeker.Status,
                    AccessSeeker.Name,
                    AccessSeeker.Alias,
                    AccessSeeker.Type,
                    AccessSeeker.ParentAccessSeekerRecordId,
                    ParentAccessSeeker.AccessSeekerId as ParentAccessSeekerId,
                    ParentAccessSeeker.Name as ParentAccessSeekerName,
                    AccessSeeker.Category1,
                    AccessSeeker.Category2,
                    AccessSeeker.Website,
                    AccessSeeker.About,
                    AccessSeeker.Comments,
                    AccessSeeker.SDM,
                    AccessSeeker.AGM,
                    AccessSeeker.CDM,
                    AccessSeeker.created_by,
                    AccessSeeker.created,
                    AccessSeeker.modified_by,
                    AccessSeeker.modified
                from
                    AccessSeeker
                left join AccessSeeker as ParentAccessSeeker on AccessSeeker.ParentAccessSeekerRecordId = ParentAccessSeeker.id """

        # Select records based on the filtering criteria
        sql = sql + generateWhereSQL(payload)
        
        # Sort records based on the sort criteria
        sql = sql + generateOrderBySQL(payload)
        
        # Limit the results based on the paging parameters
        sql = sql + generateLimitOffsetSQL(payload)        

        cursor.execute(sql)
        records = cursor.fetchall()
        
        # Get the total number of records
        sql = "SELECT FOUND_ROWS() as totalRecords"
        cursor.execute(sql)
        totalRecordsResult = cursor.fetchone()
        totalRecords = totalRecordsResult['totalRecords']  
        
    response = jsonify({"totalRecords": totalRecords, "records": records})
    return response

# ****************************************************************************
# Get a specific AccessSeeker Record
# ****************************************************************************
def GetAccessSeeker(Id):

    logger.info('Inside AccessSeeker. Id: ' + str(Id))

    conn = get_connection()
    
    with conn.cursor() as cursor:

        sql = """select
                    SQL_CALC_FOUND_ROWS 
                    AccessSeeker.id,
                    AccessSeeker.AccessSeekerId,
                    AccessSeeker.Status,
                    AccessSeeker.Name,
                    AccessSeeker.Alias,
                    AccessSeeker.Type,
                    AccessSeeker.ParentAccessSeekerRecordId,
                    ParentAccessSeeker.AccessSeekerId as ParentAccessSeekerId,
                    ParentAccessSeeker.Name as ParentAccessSeekerName,
                    AccessSeeker.Category1,
                    AccessSeeker.Category2,
                    AccessSeeker.Website,
                    AccessSeeker.About,
                    AccessSeeker.Comments,
                    AccessSeeker.SDM,
                    AccessSeeker.AGM,
                    AccessSeeker.CDM,
                    AccessSeeker.created_by,
                    AccessSeeker.created,
                    AccessSeeker.modified_by,
                    AccessSeeker.modified
                 from 
                    AccessSeeker
                 left join AccessSeeker as ParentAccessSeeker on AccessSeeker.ParentAccessSeekerRecordId = ParentAccessSeeker.id
                 where AccessSeeker.id = %(Id)s"""
        
        cursor.execute(sql, {"Id": Id})
        
        # Get record
        record = cursor.fetchone()
        
    return record

# ****************************************************************************
# Add a new AccessSeeker record
# ****************************************************************************
def AddAccessSeeker(accessSeeker):
    
    logger.info('Inside AccessSeeker')

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlFieldList = ""
    sqlFieldValues = ""
    sqlQueryParams = {}

    if "AccessSeekerId" in accessSeeker:
        sqlFieldList += "AccessSeekerId, "
        sqlFieldValues += "%(AccessSeekerId)s, "
        sqlQueryParams["AccessSeekerId"] = accessSeeker["AccessSeekerId"]
    if "Name" in accessSeeker:
        sqlFieldList += "Name, "
        sqlFieldValues += "%(Name)s, "
        sqlQueryParams["Name"] = accessSeeker["Name"]
    if "Alias" in accessSeeker:
        sqlFieldList += "Alias, "
        sqlFieldValues += "%(Alias)s, "
        sqlQueryParams["Alias"] = accessSeeker["Alias"]
    if "Status" in accessSeeker:
        sqlFieldList += "Status, "
        sqlFieldValues += "%(Status)s, "
        sqlQueryParams["Status"] = accessSeeker["Status"]
    if "Type" in accessSeeker:
        sqlFieldList += "Type, "
        sqlFieldValues += "%(Type)s, "
        sqlQueryParams["Type"] = accessSeeker["Type"]
    if "Category1" in accessSeeker:
        sqlFieldList += "Category1, "
        sqlFieldValues += "%(Category1)s, "
        sqlQueryParams["Category1"] = accessSeeker["Category1"]
    if "Category2" in accessSeeker:
        sqlFieldList += "Category2, "
        sqlFieldValues += "%(Category2)s, "
        sqlQueryParams["Category2"] = accessSeeker["Category2"]
    if "Website" in accessSeeker:
        sqlFieldList += "Website, "
        sqlFieldValues += "%(Website)s, "
        sqlQueryParams["Website"] = accessSeeker["Website"]
    if "About" in accessSeeker:
        sqlFieldList += "About, "
        sqlFieldValues += "%(About)s, "
        sqlQueryParams["About"] = accessSeeker["About"]
    if "Comments" in accessSeeker:
        sqlFieldList += "Comments, "
        sqlFieldValues += "%(Comments)s, "
        sqlQueryParams["Comments"] = accessSeeker["Comments"]
    if "SDM" in accessSeeker:
        sqlFieldList += "SDM, "
        sqlFieldValues += "%(SDM)s, "
        sqlQueryParams["SDM"] = accessSeeker["SDM"]
    if "AGM" in accessSeeker:
        sqlFieldList += "AGM, "
        sqlFieldValues += "%(AGM)s, "
        sqlQueryParams["AGM"] = accessSeeker["AGM"]
    if "CDM" in accessSeeker:
        sqlFieldList += "CDM, "
        sqlFieldValues += "%(CDM)s, "
        sqlQueryParams["CDM"] = accessSeeker["CDM"]

    sqlFieldList += "created_by, "
    sqlFieldValues += "%(LoggedInUser)s, "
    sqlFieldList += "created "
    sqlFieldValues += "NOW() "

    sqlQueryParams["LoggedInUser"] = LoggedInUser
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate insert SQL
        sql = "INSERT INTO AccessSeeker (" + sqlFieldList + ") VALUES (" + sqlFieldValues + ")"

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)
        
        # If record sucessfully added
        if cursor.rowcount > 0:
            
            # Get the updated record
            addedAccessSeeker = GetAccessSeeker(cursor.lastrowid)

        # Else record was not successfully added
        else:
            
            # Get the updated record
            addedAccessSeeker = None

    return addedAccessSeeker

# ****************************************************************************
# Update a AccessSeeker Record
# ****************************************************************************
def UpdateAccessSeeker(id, patchValues):
    
    logger.info('Inside UpdateAccessSeeker')

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlQueryParams = {}

    conn = get_connection()
    
    with conn.cursor() as cursor:
               
        # Generate insert SQL
        sql = "UPDATE AccessSeeker SET "

        if "AccessSeekerId" in patchValues:
            sql += "AccessSeekerId = %(AccessSeekerId)s, "
            sqlQueryParams["AccessSeekerId"] = patchValues["AccessSeekerId"]
        if "Name" in patchValues:
            sql += "Name = %(Name)s, "
            sqlQueryParams["Name"] = patchValues["Name"]
        if "Alias" in patchValues:
            sql += "Alias = %(Alias)s, "
            sqlQueryParams["Alias"] = patchValues["Alias"]
        if "Status" in patchValues:
            sql += "Status = %(Status)s, "
            sqlQueryParams["Status"] = patchValues["Status"]
        if "Type" in patchValues:
            sql += "Type = %(Type)s, "
            sqlQueryParams["Type"] = patchValues["Type"]
        if "ParentAccessSeekerRecordId" in patchValues:
            sql += "ParentAccessSeekerRecordId = %(ParentAccessSeekerRecordId)s, "
            sqlQueryParams["ParentAccessSeekerRecordId"] = patchValues["ParentAccessSeekerRecordId"]
        if "Category1" in patchValues:
            sql += "Category1 = %(Category1)s, "
            sqlQueryParams["Category1"] = patchValues["Category1"]
        if "Category2" in patchValues:
            sql += "Category2 = %(Category2)s, "
            sqlQueryParams["Category2"] = patchValues["Category2"]
        if "Website" in patchValues:
            sql += "Website = %(Website)s, "
            sqlQueryParams["Website"] = patchValues["Website"]
        if "About" in patchValues:
            sql += "About = %(About)s, "
            sqlQueryParams["About"] = patchValues["About"]
        if "Comments" in patchValues:
            sql += "Comments = %(Comments)s, "
            sqlQueryParams["Comments"] = patchValues["Comments"]
        if "SDM" in patchValues:
            sql += "SDM = %(SDM)s, "
            sqlQueryParams["SDM"] = patchValues["SDM"]
        if "AGM" in patchValues:
            sql += "AGM = %(AGM)s, "
            sqlQueryParams["AGM"] = patchValues["AGM"]
        if "CDM" in patchValues:
            sql += "CDM = %(CDM)s, "
            sqlQueryParams["CDM"] = patchValues["CDM"]

        sql += "modified_by = %(LoggedInUser)s, "
        sql += "modified = NOW() "
        sql += "WHERE id = %(Id)s "

        logger.info('UpdateAccessSeeker SQL: ' + sql)

        sqlQueryParams["Id"] = id
        sqlQueryParams["LoggedInUser"] = LoggedInUser

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)
        
        # If record sucessfully added
        if cursor.rowcount > 0:
            
            # Get the updated record
            updatedAccessSeeker = GetAccessSeeker(id)

        # Else record was not successfully updated
        else:
            
            # Get the updated record
            updatedAccessSeeker = None

    return updatedAccessSeeker
    
# ****************************************************************************
# Delete an AccessSeeker Record
# ****************************************************************************
def DeleteAccessSeeker(request):

    logger.info('Inside DeleteAccessSeeker')

    # Get the message body which contains the data to update 
    payload = request.json

    # Get the Id of the record to delete    
    Id = payload["id"]
    
    conn = get_connection()
    
    with conn.cursor() as cursor:

        # Generate delete SQL
        sql = """DELETE FROM AccessSeeker 
                 WHERE id = %(Id)s"""

        # Execute delete SQL with appropriate parameters
        cursor.execute(sql, {"Id": Id})
        
        # If record sucessfully deleted
        if cursor.rowcount > 0:

            # Generate the success response message
            response = jsonify({ "data": {"id": str(Id) }, "message": "Successfully deleted AccessSeeker Record: " + str(Id)})

        # Else record was not successfully deleted
        else:
            
            logger.info("Error deleting AccessSeeker with id = " + str(Id))

            # Generate the error response
            errResponseMsg = json.dumps({"code": 1, "error": "Error deleting AccessSeeker with id = " + str(Id)})

            # Return a 500 error response
            return Response(errResponseMsg, status=500, mimetype='application/json')
               
    return response


# ****************************************************************************
# Get details of the logged in user
# Note: This should be retrieved from the Access Token, not querystring params
# ****************************************************************************
def GetLoggedInUser(request):
    
    logger.info('Inside GetLoggedInUser')
    
    # Initialise variables
    userName = 'Undefined'

    # Get the username from the user profile in the querystring parameters
    if request.args.get('userProfile'):
        userProfile = json.loads(request.args.get('userProfile'))
        userName = userProfile.get('userName')
    
    return userName
