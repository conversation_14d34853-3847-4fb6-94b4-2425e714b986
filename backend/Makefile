.ONESHELL:
SHELL = /bin/bash

DOCKER_IMAGE = pos-docker.apro.nbnco.net.au/rspsysops/insights/backend
DOCKER_TAG = 1.0.0

.PHONY help:
help:
	@echo "Available Targets: all, container-image, publish-container-image"

.PHONY all:
all: container-image, publish-container-image

.PHONY container-image:
container-image:
	@echo "Building container image"
	@docker build \
	  --platform linux/amd64 \
		-t $(DOCKER_IMAGE):$(DOCKER_TAG) \
		-f Dockerfile \
		.

.PHONY publish-container-image:
publish-container-image:
	@echo "Publishing container image"
	docker push $(DOCKER_IMAGE):$(DOCKER_TAG)
