#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check for MySQL client
if ! command_exists mysql; then
    echo -e "${RED}MySQL client not found. Please install MySQL client before running this script.${NC}"
    echo -e "${YELLOW}On macOS, you can use: brew install mysql-client${NC}"
    echo -e "${YELLOW}After installation, you may need to add it to your PATH:${NC}"
    echo -e "${YELLOW}echo 'export PATH=\"/usr/local/opt/mysql-client/bin:\$PATH\"' >> ~/.zshrc${NC}"
    echo -e "${YELLOW}Then restart your terminal or run: source ~/.zshrc${NC}"
    exit 1
fi

# Check for Docker
if ! command_exists docker; then
    echo -e "${RED}Docker not found. Please install Docker before running this script.${NC}"
    echo -e "${YELLOW}Visit https://docs.docker.com/get-docker/ for installation instructions.${NC}"
    exit 1
fi

# Check MySQL Docker container status
echo -e "${BLUE}Checking MySQL Docker container status...${NC}"
if docker ps | grep -q "local-mysql"; then
    echo -e "${GREEN}MySQL Docker container is running.${NC}"
else
    echo -e "${YELLOW}MySQL Docker container is not running.${NC}"

    # Check if container exists but is stopped
    if docker ps -a | grep -q "local-mysql"; then
        echo -e "${YELLOW}Found stopped MySQL container. You can start it with:${NC}"
        echo -e "${YELLOW}docker start local-mysql${NC}"
    else
        echo -e "${YELLOW}MySQL Docker container does not exist. Run setup_local_db.sh to create it.${NC}"
    fi
    exit 1
fi

# Try to connect to MySQL
echo -e "${BLUE}Testing MySQL connection...${NC}"
if mysql -u root -ppassword -h 127.0.0.1 -e "SELECT 1" &>/dev/null; then
    echo -e "${GREEN}Successfully connected to MySQL server in Docker.${NC}"
else
    echo -e "${RED}Failed to connect to MySQL server in Docker.${NC}"
    echo -e "${YELLOW}Possible issues:${NC}"
    echo -e "${YELLOW}1. MySQL container is not properly initialized${NC}"
    echo -e "${YELLOW}2. MySQL container is not exposing port 3306${NC}"
    echo -e "${YELLOW}3. MySQL container has a different root password${NC}"

    echo -e "\n${BLUE}Checking Docker container logs...${NC}"
    echo -e "${YELLOW}Last 10 lines of Docker container logs:${NC}"
    docker logs --tail 10 local-mysql

    echo -e "\n${BLUE}Checking if port 3306 is properly mapped...${NC}"
    if docker port local-mysql | grep -q "3306/tcp -> 0.0.0.0:3306"; then
        echo -e "${GREEN}Port 3306 is properly mapped.${NC}"
    else
        echo -e "${RED}Port 3306 is not properly mapped.${NC}"
        echo -e "${YELLOW}Current port mapping:${NC}"
        docker port local-mysql
        echo -e "${YELLOW}You may need to recreate the container with proper port mapping.${NC}"
    fi

    echo -e "\n${YELLOW}Try these troubleshooting steps:${NC}"
    echo -e "${YELLOW}1. Restart the container: docker restart local-mysql${NC}"
    echo -e "${YELLOW}2. Check container logs: docker logs local-mysql${NC}"
    echo -e "${YELLOW}3. If needed, recreate the container by running setup_local_db.sh${NC}"
    exit 1
fi

# Check if database exists
echo -e "${BLUE}Checking if 'mydb' database exists...${NC}"
if mysql -u root -ppassword -h 127.0.0.1 -e "USE mydb" &>/dev/null; then
    echo -e "${GREEN}Database 'mydb' exists.${NC}"

    # Check if tables exist
    echo -e "${BLUE}Checking database tables...${NC}"
    TABLE_COUNT=$(mysql -u root -ppassword -h 127.0.0.1 -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'mydb'" | grep -v "COUNT" | tr -d ' ')

    if [ "$TABLE_COUNT" -gt 0 ]; then
        echo -e "${GREEN}Database contains $TABLE_COUNT tables.${NC}"
        echo -e "${GREEN}Database setup appears to be complete.${NC}"
    else
        echo -e "${YELLOW}Database 'mydb' exists but contains no tables.${NC}"
        echo -e "${YELLOW}Run setup_local_db.sh to initialize the database schema.${NC}"
    fi
else
    echo -e "${YELLOW}Database 'mydb' does not exist.${NC}"
    echo -e "${YELLOW}Run setup_local_db.sh to create and initialize the database.${NC}"
fi

echo -e "\n${GREEN}Database connection check complete.${NC}"
