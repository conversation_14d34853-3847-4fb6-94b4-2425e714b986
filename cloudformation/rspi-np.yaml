AWSTemplateFormatVersion: 2010-09-09
Description: RDS Template
Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
      - Label: Environment Parameters
        Parameters:
          - Environment

      - Label: RDS Parameters
        Parameters:
          - MasterUsername
          - MasterPassword

Parameters:
  Environment:
    Description: Environment
    Type: String
    AllowedValues:
      - Dev

  MasterUsername:
    Description: Master Username
    Type: String
    NoEcho: True

  MasterPassword:
    Description: Master Password
    Type: String
    NoEcho: True

Mappings:
  Environment:
    Dev:
      AppDomainName: rspi.dev
      RdsAllocatedStorage: 50
      RdsAutoMinorVersionUpgrade: True
      RdsBackupRetentionPeriod: 30
      RdsDBEngine: mysql
      RdsDBEngineVersion: "8.0.41"
      RdsDBInstanceClass: db.t4g.micro
      RdsDBInstanceIdentifier: rspi
      RdsDBName: mydb
      RdsDBOptionGroupName: default:mysql-8-0
      RdsDBParameterGroupName: default.mysql8.0
      RdsDBPort: 3306
      RdsDomainName: rspi-db.dev
      RdsPreferredBackupWindow: 20:15-20:45
      RdsPreferredMaintenanceWindow: sat:17:30-sat:18:30
      EnvCode: np
      EnvSubCode: dev
  Common:
    Tags:
      FocalPointApplicationId: 2163
      FocalPointApplicationName: RSP Sys Ops
      FocalPointApplicationShortName: RSP Sys Ops
      FocalPointSdgCode: 101
      FocalPointSdgPlatformCode: cupc
      FocalPointSdgPlatformName: Customer Portals
      FocalPointSystemTeamEmail: <EMAIL>
      FocalPointSystemTeamName: Customer Channel
      TeamCode: cupc
      MapMigrated: mig30469
      ApplicationSource: new
      OperationalMode: CM2

Resources:
  SecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: !Sub
        - '${DBInstanceIdentifier} security group'
        - DBInstanceIdentifier: !FindInMap
            - Environment
            - !Ref Environment
            - RdsDBInstanceIdentifier
      GroupName: !Sub
        - '${DBInstanceIdentifier}-security-group'
        - DBInstanceIdentifier: !FindInMap
            - Environment
            - !Ref Environment
            - RdsDBInstanceIdentifier
      SecurityGroupIngress:
        - Description: Corporate network
          IpProtocol: tcp
          FromPort: !FindInMap
            - Environment
            - !Ref Environment
            - RdsDBPort
          ToPort: !FindInMap
            - Environment
            - !Ref Environment
            - RdsDBPort
          CidrIp: 10.0.0.0/8
      VpcId: vpc-972499f3
      Tags:
        - Key: nbn:focalPoint:applicationId
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointApplicationId
        - Key: nbn:focalPoint:applicationName
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointApplicationName
        - Key: nbn:focalPoint:applicationShortName
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointApplicationShortName
        - Key: nbn:focalPoint:sdgCode
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSdgCode
        - Key: nbn:focalPoint:sdgPlatformCode
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSdgPlatformCode
        - Key: nbn:focalPoint:sdgPlatformName
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSdgPlatformName
        - Key: nbn:focalPoint:systemTeamEmail
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSystemTeamEmail
        - Key: nbn:focalPoint:systemTeamName
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSystemTeamName
        - Key: nbn:teamCode
          Value: !FindInMap
            - Common
            - Tags
            - TeamCode
        - Key: map-migrated
          Value: !FindInMap
            - Common
            - Tags
            - MapMigrated
        - Key: nbn:applicationSource
          Value: !FindInMap
            - Common
            - Tags
            - ApplicationSource
        - Key: nbn:operationalMode
          Value: !FindInMap
            - Common
            - Tags
            - OperationalMode
        - Key: nbn:env:code
          Value: !FindInMap
            - Environment
            - !Ref Environment
            - EnvCode
        - Key: nbn:env:subcode
          Value: !FindInMap
            - Environment
            - !Ref Environment
            - EnvSubCode

  SubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: !Sub
        - '${DBInstanceIdentifier} subnet group'
        - DBInstanceIdentifier: !FindInMap
            - Environment
            - !Ref Environment
            - RdsDBInstanceIdentifier
      DBSubnetGroupName: !Sub
        - '${DBInstanceIdentifier}-subnet-group'
        - DBInstanceIdentifier: !FindInMap
            - Environment
            - !Ref Environment
            - RdsDBInstanceIdentifier
      SubnetIds:
        - subnet-f8b40fa1
        - subnet-cfa5c7b9
        - subnet-691a620d
      Tags:
        - Key: nbn:focalPoint:applicationId
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointApplicationId
        - Key: nbn:focalPoint:applicationName
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointApplicationName
        - Key: nbn:focalPoint:applicationShortName
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointApplicationShortName
        - Key: nbn:focalPoint:sdgCode
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSdgCode
        - Key: nbn:focalPoint:sdgPlatformCode
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSdgPlatformCode
        - Key: nbn:focalPoint:sdgPlatformName
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSdgPlatformName
        - Key: nbn:focalPoint:systemTeamEmail
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSystemTeamEmail
        - Key: nbn:focalPoint:systemTeamName
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSystemTeamName
        - Key: nbn:teamCode
          Value: !FindInMap
            - Common
            - Tags
            - TeamCode
        - Key: map-migrated
          Value: !FindInMap
            - Common
            - Tags
            - MapMigrated
        - Key: nbn:applicationSource
          Value: !FindInMap
            - Common
            - Tags
            - ApplicationSource
        - Key: nbn:operationalMode
          Value: !FindInMap
            - Common
            - Tags
            - OperationalMode
        - Key: nbn:env:code
          Value: !FindInMap
            - Environment
            - !Ref Environment
            - EnvCode
        - Key: nbn:env:subcode
          Value: !FindInMap
            - Environment
            - !Ref Environment
            - EnvSubCode

  Rds:
    Type: AWS::RDS::DBInstance
    Properties:
      AllocatedStorage: !FindInMap
        - Environment
        - !Ref Environment
        - RdsAllocatedStorage
      AutoMinorVersionUpgrade: !FindInMap
        - Environment
        - !Ref Environment
        - RdsAutoMinorVersionUpgrade
      BackupRetentionPeriod: !FindInMap
        - Environment
        - !Ref Environment
        - RdsBackupRetentionPeriod
      CopyTagsToSnapshot: True
      DBInstanceClass: !FindInMap
        - Environment
        - !Ref Environment
        - RdsDBInstanceClass
      DBInstanceIdentifier: !FindInMap
        - Environment
        - !Ref Environment
        - RdsDBInstanceIdentifier
      DBName: !FindInMap
        - Environment
        - !Ref Environment
        - RdsDBName
      DBParameterGroupName: !FindInMap
        - Environment
        - !Ref Environment
        - RdsDBParameterGroupName
      DBSubnetGroupName: !Ref SubnetGroup
      DeleteAutomatedBackups: False
      DeletionProtection: True
      Engine: !FindInMap
        - Environment
        - !Ref Environment
        - RdsDBEngine
      EngineVersion: !FindInMap
        - Environment
        - !Ref Environment
        - RdsDBEngineVersion
      KmsKeyId: arn:aws:kms:ap-southeast-2:036108257657:key/217680ef-423f-44e4-bdb7-90907a407238
      MasterUsername: !Ref MasterUsername
      MasterUserPassword: !Ref MasterPassword
      MultiAZ: False
      OptionGroupName: !FindInMap
        - Environment
        - !Ref Environment
        - RdsDBOptionGroupName
      Port: !FindInMap
        - Environment
        - !Ref Environment
        - RdsDBPort
      PreferredBackupWindow: !FindInMap
        - Environment
        - !Ref Environment
        - RdsPreferredBackupWindow
      PreferredMaintenanceWindow: !FindInMap
        - Environment
        - !Ref Environment
        - RdsPreferredMaintenanceWindow
      PubliclyAccessible: False
      StorageEncrypted: True
      StorageType: gp3
      Tags:
        - Key: nbn:focalPoint:applicationId
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointApplicationId
        - Key: nbn:focalPoint:applicationName
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointApplicationName
        - Key: nbn:focalPoint:applicationShortName
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointApplicationShortName
        - Key: nbn:focalPoint:sdgCode
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSdgCode
        - Key: nbn:focalPoint:sdgPlatformCode
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSdgPlatformCode
        - Key: nbn:focalPoint:sdgPlatformName
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSdgPlatformName
        - Key: nbn:focalPoint:systemTeamEmail
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSystemTeamEmail
        - Key: nbn:focalPoint:systemTeamName
          Value: !FindInMap
            - Common
            - Tags
            - FocalPointSystemTeamName
        - Key: nbn:teamCode
          Value: !FindInMap
            - Common
            - Tags
            - TeamCode
        - Key: map-migrated
          Value: !FindInMap
            - Common
            - Tags
            - MapMigrated
        - Key: nbn:applicationSource
          Value: !FindInMap
            - Common
            - Tags
            - ApplicationSource
        - Key: nbn:operationalMode
          Value: !FindInMap
            - Common
            - Tags
            - OperationalMode
        - Key: nbn:env:code
          Value: !FindInMap
            - Environment
            - !Ref Environment
            - EnvCode
        - Key: nbn:env:subcode
          Value: !FindInMap
            - Environment
            - !Ref Environment
            - EnvSubCode
      VPCSecurityGroups:
        - !Ref SecurityGroup
    DeletionPolicy: Snapshot

  AppRoute53:
    Type: AWS::Route53::RecordSet
    Properties:
      Comment: "App Route53"
      HostedZoneId: Z1NQCZ13WJ5LJ1
      Name: !Sub
        - '${DomainName}.cupc.inttest.nbn-aws.local'
        - DomainName: !FindInMap
            - Environment
            - !Ref Environment
            - AppDomainName
      AliasTarget:
        DNSName: dualstack.internal-a82ccb1d7c82a488382771147c6d293c-1089011398.ap-southeast-2.elb.amazonaws.com
        HostedZoneId: Z1GM3OXH4ZPM65
      Type: A

  RdsRoute53:
    Type: AWS::Route53::RecordSet
    Properties:
      Comment: !Sub
        - '${DBInstanceIdentifier} Route 53'
        - DBInstanceIdentifier: !FindInMap
            - Environment
            - !Ref Environment
            - RdsDBInstanceIdentifier
      HostedZoneId: Z1NQCZ13WJ5LJ1
      Name: !Sub
        - '${DomainName}.cupc.inttest.nbn-aws.local'
        - DomainName: !FindInMap
            - Environment
            - !Ref Environment
            - RdsDomainName
      ResourceRecords:
        - !GetAtt Rds.Endpoint.Address
      TTL: 300
      Type: CNAME

Outputs:
  AppDomain:
    Description: App Domain Name
    Value: !Ref AppRoute53

  RdsDomain:
    Description: Rds Domain Name
    Value: !Ref RdsRoute53

  RdsEndpoint:
    Description: Rds Endpoint
    Value: !GetAtt Rds.Endpoint.Address
