#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== RSPi Quick Start ===${NC}"

# Function to clean up on exit
cleanup() {
    echo -e "\n${BLUE}Shutting down servers...${NC}"
    
    # Kill backend process if it exists
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if ps -p $BACKEND_PID > /dev/null 2>&1; then
            kill $BACKEND_PID
            echo -e "${GREEN}Backend server stopped${NC}"
        fi
        rm -f backend.pid
    fi
    
    # Kill frontend process if it exists
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if ps -p $FRONTEND_PID > /dev/null 2>&1; then
            kill $FRONTEND_PID
            echo -e "${GREEN}Frontend server stopped${NC}"
        fi
        rm -f frontend.pid
    fi
    
    # Kill any remaining processes
    pkill -f "flask run" 2>/dev/null || true
    pkill -f "ng serve" 2>/dev/null || true
    
    echo -e "${GREEN}Cleanup complete${NC}"
    exit 0
}

# Set up trap to catch Ctrl+C and other termination signals
trap cleanup SIGINT SIGTERM

# Step 1: Kill any existing processes
echo -e "${YELLOW}Stopping any existing processes...${NC}"
pkill -f "flask run" 2>/dev/null || true
pkill -f "ng serve" 2>/dev/null || true
sleep 2

# Step 2: Verify MySQL container
echo -e "${YELLOW}Checking MySQL container...${NC}"
if ! docker ps | grep -q "local-mysql"; then
    if docker ps -a | grep -q "local-mysql"; then
        echo -e "${YELLOW}Starting existing MySQL container...${NC}"
        docker start local-mysql
        sleep 3
    else
        echo -e "${RED}MySQL container not found. Please run ./emergency_fix.sh first.${NC}"
        exit 1
    fi
fi
echo -e "${GREEN}✓ MySQL container is running${NC}"

# Step 3: Start Backend
echo -e "${YELLOW}Starting backend server...${NC}"
cd backend || exit 1

# Activate virtual environment
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
else
    echo -e "${RED}Virtual environment not found. Please run ./emergency_fix.sh first.${NC}"
    exit 1
fi

# Set Flask environment variables
export FLASK_APP=app/app.py
export FLASK_ENV=development
export FLASK_DEBUG=1

# Start Flask on port 5001
python -m flask run --host=0.0.0.0 --port=5001 > ../backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../backend.pid

cd ..

echo -e "${GREEN}✓ Backend started (PID: $BACKEND_PID)${NC}"

# Step 4: Start Frontend
echo -e "${YELLOW}Starting frontend server...${NC}"
cd frontend || exit 1

# Start Angular
npm start > ../frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > ../frontend.pid

cd ..

echo -e "${GREEN}✓ Frontend started (PID: $FRONTEND_PID)${NC}"

# Step 5: Wait a moment and check if processes are still running
sleep 5

# Check if backend process is still running
if ps -p $BACKEND_PID > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Backend process is running${NC}"
else
    echo -e "${RED}✗ Backend process died. Check backend.log for errors${NC}"
fi

# Check if frontend process is still running
if ps -p $FRONTEND_PID > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Frontend process is running${NC}"
else
    echo -e "${RED}✗ Frontend process died. Check frontend.log for errors${NC}"
fi

# Step 6: Open browser
if command -v open &> /dev/null; then
    echo -e "${BLUE}Opening application in browser...${NC}"
    sleep 3  # Give servers a moment to start
    open http://localhost:4200
elif command -v xdg-open &> /dev/null; then
    echo -e "${BLUE}Opening application in browser...${NC}"
    sleep 3
    xdg-open http://localhost:4200
fi

# Final status
echo -e "\n${BLUE}=== Application Started ===${NC}"
echo -e "${GREEN}Frontend: http://localhost:4200${NC}"
echo -e "${GREEN}Backend:  http://localhost:5001${NC}"
echo -e "${YELLOW}Backend PID: $BACKEND_PID${NC}"
echo -e "${YELLOW}Frontend PID: $FRONTEND_PID${NC}"
echo -e "\n${YELLOW}To check logs:${NC}"
echo -e "  Backend:  ${BLUE}tail -f backend.log${NC}"
echo -e "  Frontend: ${BLUE}tail -f frontend.log${NC}"
echo -e "\n${YELLOW}To check if servers are responding:${NC}"
echo -e "  Backend:  ${BLUE}curl http://localhost:5001${NC}"
echo -e "  Frontend: ${BLUE}curl http://localhost:4200${NC}"
echo -e "\n${RED}Press Ctrl+C to stop both servers${NC}"

# Keep script running and show live logs
echo -e "\n${BLUE}=== Live Logs (Press Ctrl+C to stop) ===${NC}"

# Wait for log files to be created
sleep 2

# Show live logs from both files
if [ -f "backend.log" ] && [ -f "frontend.log" ]; then
    tail -f backend.log frontend.log
else
    echo -e "${YELLOW}Waiting for log files to be created...${NC}"
    sleep 5
    if [ -f "backend.log" ] && [ -f "frontend.log" ]; then
        tail -f backend.log frontend.log
    else
        echo -e "${RED}Log files not found. Servers may not have started properly.${NC}"
        echo -e "${YELLOW}Check the process status manually:${NC}"
        echo -e "  ps -p $BACKEND_PID"
        echo -e "  ps -p $FRONTEND_PID"
        
        # Keep the script running so user can manually check
        while true; do
            sleep 10
            if ! ps -p $BACKEND_PID > /dev/null 2>&1; then
                echo -e "${RED}Backend process died${NC}"
                break
            fi
            if ! ps -p $FRONTEND_PID > /dev/null 2>&1; then
                echo -e "${RED}Frontend process died${NC}"
                break
            fi
        done
    fi
fi
