apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: insights
  labels:
    app: insights
spec:
  gateways: []
  hosts: []
  http:
    - match:
        - uri:
            prefix: /api
      rewrite:
        uri: /
      route:
        - destination:
            host: insights
            port:
              number: 5000
    - route:
        - destination:
            host: insights
            port:
              number: 8080
