apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: insights
  labels:
    app: insights
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: insights
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
  minReplicas: ""
  maxReplicas: ""
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 80
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
