apiVersion: apps/v1
kind: Deployment
metadata:
  name: insights
  labels:
    app: insights
spec:
  replicas: 1
  selector:
    matchLabels:
      app: insights
  template:
    metadata:
      labels:
        app: insights
    spec:
      nodeSelector:
        candc1.k8s.application: rspsysops
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      serviceAccountName: insights
      enableServiceLinks: false
      containers:
        - name: frontend
          image: ""
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: insights-frontend-env
          ports:
            - containerPort: 8080
              name: frontend
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL
          resources:
            requests:
              cpu: 0.5
              memory: 2Gi
            limits:
              cpu: 1
              memory: 4Gi
          volumeMounts:
            - name: volume-frontend-tmp
              mountPath: /tmp
        - name: backend
          image: ""
          imagePullPolicy: IfNotPresent
          envFrom:
            - secretRef:
                name: insights-backend-env
            - configMapRef:
                name: insights-backend-env
          env:
            - name: LOGGING_CONFIG_PATH
              value: /app/conf/logging.yaml
          ports:
            - containerPort: 5000
              name: backend
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL
          resources:
            requests:
              cpu: 0.5
              memory: 2Gi
            limits:
              cpu: 1
              memory: 4Gi
          volumeMounts:
            - name: volume-backend-config
              mountPath: /app/conf
            - name: volume-backend-tmp
              mountPath: /tmp
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - insights
              topologyKey: kubernetes.io/hostname
      volumes:
        - name: volume-frontend-tmp
          emptyDir: {}
        - name: volume-backend-tmp
          emptyDir: {}
        - name: volume-backend-config
          configMap:
            name: insights-backend-file
