apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: rsp-sys-ops
resources:
  - ../../base
  - configmap-backend-env.yaml
  - configmap-backend-file.yaml
  - configmap-frontend-env.yaml
patches:
  - target:
      group: apps
      version: v1
      kind: Deployment
      name: insights
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/image
        value: pos-docker.apro.nbnco.net.au/rspsysops/insights/frontend:1.0.0
      - op: replace
        path: /spec/template/spec/containers/0/imagePullPolicy
        value: Always
      - op: replace
        path: /spec/template/spec/containers/1/image
        value: pos-docker.apro.nbnco.net.au/rspsysops/insights/backend:1.0.0
      - op: replace
        path: /spec/template/spec/containers/1/imagePullPolicy
        value: Always
  - target:
      group: autoscaling
      version: v2
      kind: HorizontalPodAutoscaler
      name: insights
    patch: |-
      - op: replace
        path: /spec/minReplicas
        value: 2
      - op: replace
        path: /spec/maxReplicas
        value: 2
  - target:
      group: policy
      version: v1
      kind: PodDisruptionBudget
      name: insights
    patch: |-
      - op: replace
        path: /spec/maxUnavailable
        value: 1
  - target:
      group: networking.istio.io
      version: v1beta1
      kind: VirtualService
      name: insights
    patch: |-
      - op: add
        path: /spec/gateways/-
        value: istio-system/mseu-gateway
      - op: add
        path: /spec/hosts/-
        value: rspi.dev.cupc.inttest.nbn-aws.local
