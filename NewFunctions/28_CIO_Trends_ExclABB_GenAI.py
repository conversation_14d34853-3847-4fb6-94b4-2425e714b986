import streamlit as st
import pandas as pd
import altair as alt
import numpy as np
import logging
from datetime import datetime
import io
import base64
import os
import sys
from pathlib import Path
import requests
import sqlite3

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from insight_generator import InsightGenerator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set database path
DB_PATH = os.path.abspath("data/RSPDigitalAdoptionUtilisationStreamlitTest.sqlite")

# Initialize session states for tracking insight generation
if 'insights_queue' not in st.session_state:
    st.session_state.insights_queue = []
if 'currently_processing' not in st.session_state:
    st.session_state.currently_processing = None
if 'priority_insight' not in st.session_state:
    st.session_state.priority_insight = None
if 'section_insights' not in st.session_state:
    st.session_state.section_insights = {}
if 'processed_services' not in st.session_state:
    st.session_state.processed_services = set()

# Set page config with proper layout
st.set_page_config(
    page_title="Digital Usage Trends",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Add custom CSS to ensure proper chart sizing
st.markdown("""
<style>
    .block-container {
        padding-top: 1rem;
        padding-bottom: 0rem;
        padding-left: 5rem;
        padding-right: 5rem;
    }
    .element-container {
        width: 100% !important;
    }
    .stMarkdown {
        width: 100% !important;
    }
    .insight-expander {
        background-color: #f8f9fa;
        border-radius: 5px;
        margin-bottom: 1rem;
        padding: 1rem;
    }
    .insight-expander:hover {
        background-color: #e9ecef;
    }
    .insight-text {
        font-size: 1rem;
        line-height: 1.5;
        color: #212529;
    }
    .insight-timestamp {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 0.5rem;
    }
    .refresh-button {
        float: right;
        padding: 0.5rem 1rem;
    }
    .ai-insights {
        background-color: #e6f3ff;
        padding: 20px;
        border-radius: 10px;
        margin: 10px 0;
    }
</style>
""", unsafe_allow_html=True)

# Create the SQL connection to sqlite
conn = st.connection('sqlite', type='sql', url=f"sqlite:///{DB_PATH}")

# Create GeneratedInsights table if it doesn't exist
def create_insights_table():
    try:
        # Use direct SQLite connection for DDL operations
        with sqlite3.connect(DB_PATH) as sqlite_conn:
            sqlite_conn.execute("""
            CREATE TABLE IF NOT EXISTS GeneratedInsights (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                dataset_name TEXT NOT NULL,
                period TEXT NOT NULL,
                dimension INTEGER NOT NULL,
                insight_text TEXT NOT NULL,
                generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_valid BOOLEAN DEFAULT TRUE,
                data_start_period TEXT NOT NULL,
                data_end_period TEXT NOT NULL,
                UNIQUE(dataset_name, period, dimension)
            )
            """)
            sqlite_conn.commit()
        logger.info("GeneratedInsights table created or verified")
    except Exception as e:
        logger.error(f"Error creating GeneratedInsights table: {e}")
        st.error("Error setting up insights storage")

# Create table on startup
create_insights_table()

def save_chart(chart, chart_title):
    """Helper function to display Altair chart with export options"""
    try:
        # Configure chart to be responsive
        chart = chart.properties(
            width="container",
            height=400
        ).configure_view(
            strokeWidth=0
        ).configure_header(
            titleFontSize=14
        ).configure_legend(
            titleFontSize=12,
            labelFontSize=11
        ).configure_axis(
            labelFontSize=11,
            titleFontSize=12
        )

        # Display the chart with Vega-Lite actions menu enabled
        st.altair_chart(chart, use_container_width=True)
        
        logger.info(f"Successfully displayed chart for {chart_title}")
        
    except Exception as e:
        logger.error(f"Error displaying chart for {chart_title}: {e}")
        st.error(f"Error displaying chart: {e}")

def load_data():
    """Load and prepare the data from SQLite database"""
    logger.info("Loading data from SQLite database")
    try:
        # Query to get aggregated data by period and channel, using the same view as RSP_Digital_Usage.py
        query = """
        WITH PeriodData AS (
            SELECT DISTINCT Period 
            FROM DigitalUsageDetailView 
            ORDER BY substr(Period, -2) || 
                CASE 
                    WHEN substr(Period, 1, 3) = 'Jan' THEN '01'
                    WHEN substr(Period, 1, 3) = 'Feb' THEN '02'
                    WHEN substr(Period, 1, 3) = 'Mar' THEN '03'
                    WHEN substr(Period, 1, 3) = 'Apr' THEN '04'
                    WHEN substr(Period, 1, 3) = 'May' THEN '05'
                    WHEN substr(Period, 1, 3) = 'Jun' THEN '06'
                    WHEN substr(Period, 1, 3) = 'Jul' THEN '07'
                    WHEN substr(Period, 1, 3) = 'Aug' THEN '08'
                    WHEN substr(Period, 1, 3) = 'Sep' THEN '09'
                    WHEN substr(Period, 1, 3) = 'Oct' THEN '10'
                    WHEN substr(Period, 1, 3) = 'Nov' THEN '11'
                    WHEN substr(Period, 1, 3) = 'Dec' THEN '12'
                END DESC
        )
        SELECT 
            p.Period,
            COALESCE(AllTxn.TotalTxns, 0) as TotalTxns,
            COALESCE(APITxn.TotalTxns, 0) as TotalAPITxns,
            COALESCE(PortalTxn.TotalTxns, 0) as TotalPortalTxns
        FROM 
            PeriodData p
        LEFT OUTER JOIN 
            (SELECT Period, SUM(Total) as TotalTxns 
             FROM DigitalUsageDetailView 
             GROUP BY Period) as AllTxn 
        ON p.Period = AllTxn.Period
        LEFT OUTER JOIN 
            (SELECT Period, SUM(Total) as TotalTxns 
             FROM DigitalUsageDetailView 
             WHERE businessChannel = 'APIGWY' 
             GROUP BY Period) as APITxn 
        ON p.Period = APITxn.Period
        LEFT OUTER JOIN 
            (SELECT Period, SUM(Total) as TotalTxns 
             FROM DigitalUsageDetailView 
             WHERE businessChannel = 'ServicePortal' 
             GROUP BY Period) as PortalTxn 
        ON p.Period = PortalTxn.Period
        WHERE AllTxn.TotalTxns IS NOT NULL
        """
        
        logger.info("Executing query to fetch digital usage data")
        
        # Use the Streamlit connection to execute the query
        df = conn.query(query)
        
        if df.empty:
            logger.error("Query returned no data")
            st.error("No data available from the database")
            return None
            
        logger.info(f"Raw data loaded: {len(df)} rows")
        
        # Convert period to datetime for sorting
        df['PeriodDateTime'] = pd.to_datetime(df['Period'].apply(
            lambda x: f"20{x[-2:]}-{['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'].index(x[:3])+1:02d}-01"
        ))
        
        # Fill any NaN values with 0
        df = df.fillna(0)
        
        # Convert transaction columns to integers
        for col in ['TotalTxns', 'TotalAPITxns', 'TotalPortalTxns']:
            df[col] = df[col].astype(int)
        
        # Sort by PeriodDateTime
        df = df.sort_values('PeriodDateTime')
        
        # Add detailed logging for data validation
        logger.info(f"Data processed successfully. {len(df)} periods loaded.")
        logger.info(f"Date range: {df['PeriodDateTime'].min()} to {df['PeriodDateTime'].max()}")
        logger.info(f"Total transactions: {df['TotalTxns'].sum():,}")
        
        # Log sample of latest data for verification
        latest_period = df.iloc[-1]
        logger.info(f"Latest period ({latest_period['Period']}) stats:")
        logger.info(f"  Total Txns: {latest_period['TotalTxns']:,}")
        logger.info(f"  API Txns: {latest_period['TotalAPITxns']:,}")
        logger.info(f"  Portal Txns: {latest_period['TotalPortalTxns']:,}")
        
        return df
        
    except Exception as e:
        logger.error(f"Error loading data from SQLite: {e}")
        st.error(f"Error loading data from SQLite: {e}")
        return None

def process_next_insight():
    """Process the next insight in the queue if available"""
    if not st.session_state.currently_processing:
        next_item = None
        is_priority = False
        
        # Check for priority insight first
        if st.session_state.priority_insight:
            next_item = st.session_state.priority_insight
            st.session_state.priority_insight = None
            is_priority = True
            logger.info(f"Processing priority insight: {next_item['name']}")
        # Otherwise take from regular queue
        elif st.session_state.insights_queue:
            next_item = st.session_state.insights_queue.pop(0)
            logger.info(f"Processing queued insight: {next_item['name']}")
        
        if next_item:
            st.session_state.currently_processing = next_item['key']
            try:
                # Initialize insight generator
                insight_gen = InsightGenerator(conn)
                
                insights = insight_gen.generate_insights(
                    next_item['dataset_name'],
                    next_item['period'],
                    next_item['data'],
                    force_refresh=True
                )
                
                if insights:
                    st.session_state.section_insights[next_item['key']] = insights
                    logger.info(f"Successfully generated insights for {next_item['name']}")
                
            except Exception as e:
                logger.error(f"Error processing insights for {next_item['name']}: {str(e)}")
                if next_item['key'] in st.session_state.processed_services:
                    st.session_state.processed_services.remove(next_item['key'])
            finally:
                st.session_state.currently_processing = None
                
            # If this was a priority insight, rerun immediately to show the result
            if is_priority:
                st.rerun()
            # Otherwise, continue with queue if there are more items
            elif st.session_state.insights_queue:
                st.rerun()

def queue_insight_generation(key, dataset_name, period, data, name, is_priority=False):
    """Queue an insight for generation"""
    if key not in st.session_state.section_insights or is_priority:
        logger.info(f"Queueing insight generation for {name} {'(Priority)' if is_priority else ''}")
        insight_item = {
            'key': key,
            'dataset_name': dataset_name,
            'period': period,
            'data': data,
            'name': name
        }
        
        if is_priority:
            # Clear existing insight to show loading state
            if key in st.session_state.section_insights:
                del st.session_state.section_insights[key]
            # Remove from processed services to force regeneration
            if key in st.session_state.processed_services:
                st.session_state.processed_services.remove(key)
            # Set as priority insight to be processed next
            st.session_state.priority_insight = insight_item
            # Remove from regular queue if it exists
            st.session_state.insights_queue = [
                item for item in st.session_state.insights_queue 
                if item['key'] != key
            ]
            # Rerun to start processing the priority insight
            st.rerun()
        else:
            if key not in st.session_state.processed_services:
                st.session_state.insights_queue.append(insight_item)
                st.session_state.processed_services.add(key)

def display_trends():
    """Display the digital usage trends chart"""
    logger.info("Displaying digital usage trends")
    
    # Load the data
    df = load_data()
    if df is None:
        return
    
    st.title("Digital Usage History")
    
    # Get sorted list of unique periods
    sorted_periods = df.sort_values('PeriodDateTime')['Period'].unique()
    
    # Add month comparison section first so we can use its values to filter the chart
    st.markdown("### Month Comparison Analysis")
    
    # Create two columns for month selection
    col1, col2 = st.columns(2)
    
    with col1:
        month1 = st.selectbox(
            "Select first month",
            options=sorted_periods,
            index=0,
            key="comparison_month1"
        )
    
    with col2:
        # Default to last month for comparison
        month2 = st.selectbox(
            "Select second month",
            options=sorted_periods,
            index=len(sorted_periods)-1,
            key="comparison_month2"
        )
    
    # Get dates for selected months for filtering
    date1 = df[df['Period'] == month1]['PeriodDateTime'].iloc[0]
    date2 = df[df['Period'] == month2]['PeriodDateTime'].iloc[0]
    
    # Ensure date1 is before date2
    if date1 > date2:
        date1, date2 = date2, date1
        month1, month2 = month2, month1
    
    # Filter data for the chart based on selected comparison months
    mask = (df['PeriodDateTime'] >= date1) & (df['PeriodDateTime'] <= date2)
    filtered_df = df[mask].copy()
    
    # Options for trend visualization
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        show_trendlines = st.checkbox("Show trendlines", value=True, 
                                    help="Display calculated trendlines to visualize the overall direction")
    with col2:
        show_projections = st.checkbox("Show future projections", value=True,
                                    help="Display projected trends for future months")
    with col3:
        projection_months = st.number_input("Future projection months", min_value=0, max_value=12, value=6,
                                          help="Number of months to project trends into the future",
                                          disabled=not show_projections)
    with col4:
        confidence_interval = st.checkbox("Show confidence interval", value=True,
                                        help="Display the confidence interval for trend projections",
                                        disabled=not (show_trendlines and show_projections))
    
    # Create tabs for visualization
    tab_chart, tab_data, tab_insights = st.tabs(["Chart", "Data", "GenAI Insights"])
    
    # Store trendline data for the data tab
    all_trendline_data = []
    
    with tab_chart:
        # Define colors for each metric to match the screenshot
        colors = {
            'TotalTxns': '#0066cc',  # darker blue
            'TotalAPITxns': '#ff0000',  # red
            'TotalPortalTxns': '#99ccff'  # light blue
        }
        
        # Create base chart data
        chart_data = filtered_df.copy()
        
        # Add period index for trendline calculation
        chart_data['period_index'] = range(len(chart_data))
        
        # Initialize charts list
        charts = []
        
        # Create charts for each metric
        metrics = ['TotalTxns', 'TotalAPITxns', 'TotalPortalTxns']
        metric_names = {
            'TotalTxns': 'Total Transactions',
            'TotalAPITxns': 'API Transactions',
            'TotalPortalTxns': 'Portal Transactions'
        }
        
        # Get the sorted period list for x-axis ordering
        period_list = chart_data['Period'].tolist()
        
        # Prepare data for the chart with a 'Metric' column
        melted_data = pd.melt(
            chart_data,
            id_vars=['Period', 'period_index'],
            value_vars=metrics,
            var_name='Metric',
            value_name='Value'
        )
        
        # Create a mapping for the colors
        domain = list(metric_names.keys())
        range_ = [colors[m] for m in domain]
        
        # Create the base chart with proper legend
        base = alt.Chart(melted_data).encode(
            x=alt.X('Period:N', title='Period', sort=period_list),
            y=alt.Y('Value:Q', title='Value'),
            color=alt.Color('Metric:N', 
                          scale=alt.Scale(domain=domain, range=range_),
                          legend=alt.Legend(orient='right'))
        )
        
        # Add the lines with points
        lines = base.mark_line(point=True).encode(
            tooltip=[
                alt.Tooltip('Period:N', title='Period'),
                alt.Tooltip('Value:Q', title='Value', format=','),
                alt.Tooltip('Metric:N', title='Metric')
            ]
        )
        
        charts.append(lines)
        
        if show_trendlines and len(chart_data) >= 2:
            # Calculate future periods only if projections are enabled
            future_periods = []
            if show_projections and projection_months > 0:
                last_date = df[df['Period'] == period_list[-1]]['PeriodDateTime'].iloc[0]
                future_dates = pd.date_range(start=last_date, periods=projection_months + 1, freq='M')[1:]
                future_periods = [d.strftime('%b-%y') for d in future_dates]
            
            for metric in metrics:
                # Calculate trendline
                x = chart_data['period_index'].values
                y = chart_data[metric].values
                
                # Fit polynomial regression (degree 2 for more accurate trends)
                z = np.polyfit(x, y, 2)
                p = np.poly1d(z)
                
                # Generate x values based on whether projections are enabled
                if show_projections and projection_months > 0:
                    future_x = np.arange(len(x), len(x) + projection_months)
                    all_x = np.concatenate([x, future_x])
                    all_periods = period_list + future_periods
                else:
                    all_x = x
                    all_periods = period_list
                
                trendline_values = p(all_x)
                
                # Calculate confidence intervals only if both trendlines and projections are enabled
                if confidence_interval and show_projections and len(x) > 2:
                    # Calculate prediction interval
                    MSE = np.sum((y - p(x))**2) / (len(x) - 2)
                    std_error = np.sqrt(MSE * (1 + 1/len(x) + (all_x - np.mean(x))**2 / np.sum((x - np.mean(x))**2)))
                    ci = 1.96 * std_error  # 95% confidence interval
                    
                    # Create confidence interval data
                    ci_data = pd.DataFrame({
                        'Period': all_periods,
                        'Trendline': trendline_values,
                        'Upper': trendline_values + ci,
                        'Lower': trendline_values - ci,
                        'Metric': metric
                    })
                    
                    # Add confidence interval bands
                    ci_bands = alt.Chart(ci_data).mark_area(opacity=0.2).encode(
                        x=alt.X('Period:N', sort=all_periods),
                        y=alt.Y('Lower:Q'),
                        y2=alt.Y2('Upper:Q'),
                        color=alt.value(colors[metric])
                    )
                    charts.append(ci_bands)
                
                # Create trendline data
                trendline_data = pd.DataFrame({
                    'Period': all_periods,
                    'Trendline': trendline_values.round(0).astype(int),
                    'Metric': [metric] * len(all_periods)
                })
                
                # Store trendline data for the data tab
                all_trendline_data.append(trendline_data)
                
                # Create the trendline
                trend = alt.Chart(trendline_data).mark_line(
                    strokeDash=[5, 5],
                    strokeWidth=2,
                    opacity=0.7
                ).encode(
                    x=alt.X('Period:N', sort=all_periods),
                    y=alt.Y('Trendline:Q'),
                    color=alt.value(colors[metric])
                )
                
                # Add visible points on the trendline
                trend_points = alt.Chart(trendline_data).mark_circle(
                    size=30,
                    opacity=0.5
                ).encode(
                    x=alt.X('Period:N', sort=all_periods),
                    y=alt.Y('Trendline:Q'),
                    color=alt.value(colors[metric]),
                    tooltip=[
                        alt.Tooltip('Period:N', title='Period'),
                        alt.Tooltip('Metric:N', title='Metric'),
                        alt.Tooltip('Trendline:Q', title='Projected Value', format=',')
                    ]
                )
                
                charts.append(trend)
                charts.append(trend_points)
        
        # Combine all charts
        if charts:
            combined_chart = alt.layer(*charts).properties(
                title='Digital Usage History with Projections',
                width=800,
                height=500
            ).configure_axis(
                grid=True
            ).configure_view(
                strokeWidth=0
            ).interactive()
            
            # Use the save_chart function instead of st.altair_chart
            save_chart(combined_chart, "Digital_Usage_History")
            
            # Add month comparison section
            st.markdown("---")
            st.subheader("Month Comparison Analysis")
            
            # Get sorted list of unique periods
            sorted_periods = df.sort_values('PeriodDateTime')['Period'].unique()
            
            # Create two columns for month selection
            col1, col2 = st.columns(2)
            
            with col1:
                month1 = st.selectbox(
                    "Select first month",
                    options=sorted_periods,
                    index=0
                )
            
            with col2:
                # Default to last month for comparison
                month2 = st.selectbox(
                    "Select second month",
                    options=sorted_periods,
                    index=len(sorted_periods)-1
                )
            
            if month1 and month2:
                # Get data for selected months
                data1 = df[df['Period'] == month1].iloc[0]
                data2 = df[df['Period'] == month2].iloc[0]
                
                # Calculate trendline values for selected months
                if show_trendlines and len(all_trendline_data) > 0:
                    trendline_df = pd.concat(all_trendline_data)
                    trend1 = trendline_df[trendline_df['Period'] == month1]
                    trend2 = trendline_df[trendline_df['Period'] == month2]
                
                # Create comparison metrics
                st.markdown("### Key Insights")
                
                # Create three columns for different types of insights
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.markdown("#### Volume Comparisons")
                    for metric in metrics:
                        val1 = data1[metric]
                        val2 = data2[metric]
                        diff = val2 - val1
                        pct_change = (diff / val1) * 100
                        
                        st.markdown(f"**{metric_names[metric]}**")
                        st.markdown(f"{month1}: {val1:,.0f}")
                        st.markdown(f"{month2}: {val2:,.0f}")
                        st.markdown(f"Change: {diff:,.0f} ({pct_change:,.1f}%)")
                        st.markdown("---")
                
                with col2:
                    st.markdown("#### Channel Mix Analysis")
                    # Calculate channel percentages for each month
                    total1 = data1['TotalTxns']
                    total2 = data2['TotalTxns']
                    
                    api_pct1 = (data1['TotalAPITxns'] / total1) * 100
                    portal_pct1 = (data1['TotalPortalTxns'] / total1) * 100
                    
                    api_pct2 = (data2['TotalAPITxns'] / total2) * 100
                    portal_pct2 = (data2['TotalPortalTxns'] / total2) * 100
                    
                    st.markdown(f"**{month1} Channel Split**")
                    st.markdown(f"API: {api_pct1:.1f}%")
                    st.markdown(f"Portal: {portal_pct1:.1f}%")
                    
                    st.markdown(f"**{month2} Channel Split**")
                    st.markdown(f"API: {api_pct2:.1f}%")
                    st.markdown(f"Portal: {portal_pct2:.1f}%")
                    
                    # Calculate shift in channel mix
                    api_shift = api_pct2 - api_pct1
                    st.markdown("**Channel Shift**")
                    st.markdown(f"API Mix Change: {api_shift:+.1f}%")
                    st.markdown(f"Portal Mix Change: {-api_shift:+.1f}%")
                
                with col3:
                    st.markdown("#### Growth Patterns")
                    if show_trendlines and len(all_trendline_data) > 0:
                        # Compare actual vs trendline for both months
                        for metric in metrics:
                            trend_val1 = trend1[trend1['Metric'] == metric]['Trendline'].iloc[0]
                            trend_val2 = trend2[trend2['Metric'] == metric]['Trendline'].iloc[0]
                            
                            actual1 = data1[metric]
                            actual2 = data2[metric]
                            
                            # Calculate variance from trendline
                            var1 = ((actual1 - trend_val1) / trend_val1) * 100
                            var2 = ((actual2 - trend_val2) / trend_val2) * 100
                            
                            st.markdown(f"**{metric_names[metric]}**")
                            st.markdown(f"{month1} vs Trend: {var1:+.1f}%")
                            st.markdown(f"{month2} vs Trend: {var2:+.1f}%")
                            st.markdown("---")
                
                # Add overall insights
                st.markdown("### Key Observations")
                insights = []
                
                # Volume insights
                for metric in metrics:
                    val1 = data1[metric]
                    val2 = data2[metric]
                    pct_change = ((val2 - val1) / val1) * 100
                    
                    if abs(pct_change) > 5:  # Significant changes
                        insights.append(f"- {metric_names[metric]} showed a significant {'increase' if pct_change > 0 else 'decrease'} " 
                                     f"of {abs(pct_change):.1f}% from {month1} to {month2}")
                
                # Channel mix insights
                if abs(api_shift) > 2:  # Significant channel mix change
                    insights.append(f"- Channel mix shifted by {abs(api_shift):.1f}% towards {'API' if api_shift > 0 else 'Portal'} transactions")
                
                # Trend variance insights
                if show_trendlines and len(all_trendline_data) > 0:
                    for metric in metrics:
                        trend_val1 = trend1[trend1['Metric'] == metric]['Trendline'].iloc[0]
                        trend_val2 = trend2[trend2['Metric'] == metric]['Trendline'].iloc[0]
                        actual1 = data1[metric]
                        actual2 = data2[metric]
                        
                        var1 = ((actual1 - trend_val1) / trend_val1) * 100
                        var2 = ((actual2 - trend_val2) / trend_val2) * 100
                        
                        if abs(var2) > 5:  # Significant variance from trend
                            insights.append(f"- {metric_names[metric]} is {'above' if var2 > 0 else 'below'} the trendline "
                                         f"by {abs(var2):.1f}% in {month2}")
                
                # Add new advanced insights section
                st.markdown("### Advanced Analytics")
                
                # Create columns for additional metrics
                col1, col2 = st.columns(2)
                
                with col1:
                    st.markdown("#### Performance Metrics")
                    
                    # Calculate daily averages (assuming 30 days per month)
                    st.markdown("**Daily Average Transactions**")
                    for metric in metrics:
                        daily_avg1 = data1[metric] / 30
                        daily_avg2 = data2[metric] / 30
                        daily_change = ((daily_avg2 - daily_avg1) / daily_avg1) * 100
                        
                        st.markdown(f"**{metric_names[metric]}**")
                        st.markdown(f"{month1}: {daily_avg1:,.0f}/day")
                        st.markdown(f"{month2}: {daily_avg2:,.0f}/day")
                        st.markdown(f"Change: {daily_change:+.1f}%")
                        st.markdown("---")
                    
                    # Calculate API to Portal ratio
                    api_ratio1 = data1['TotalAPITxns'] / data1['TotalPortalTxns']
                    api_ratio2 = data2['TotalAPITxns'] / data2['TotalPortalTxns']
                    ratio_change = ((api_ratio2 - api_ratio1) / api_ratio1) * 100
                    
                    st.markdown("**API to Portal Ratio**")
                    st.markdown(f"{month1}: {api_ratio1:.2f}:1")
                    st.markdown(f"{month2}: {api_ratio2:.2f}:1")
                    st.markdown(f"Change: {ratio_change:+.1f}%")
                
                with col2:
                    st.markdown("#### Growth Analysis")
                    
                    # Calculate compound monthly growth rate (CMGR)
                    date1 = data1['PeriodDateTime']
                    date2 = data2['PeriodDateTime']
                    months_between = abs((date2.year - date1.year) * 12 + date2.month - date1.month)
                    
                    if months_between > 0:
                        st.markdown("**Compound Monthly Growth Rate**")
                        for metric in metrics:
                            val1 = data1[metric]
                            val2 = data2[metric]
                            cmgr = (pow(val2/val1, 1/months_between) - 1) * 100
                            
                            st.markdown(f"**{metric_names[metric]}**")
                            st.markdown(f"CMGR: {cmgr:+.2f}%")
                        st.markdown("---")
                
                # Add new insights based on advanced metrics
                
                # Daily volume insights
                for metric in metrics:
                    daily_avg1 = data1[metric] / 30
                    daily_avg2 = data2[metric] / 30
                    daily_change = ((daily_avg2 - daily_avg1) / daily_avg1) * 100
                    
                    if abs(daily_change) > 10:  # Significant daily average change
                        insights.append(f"- Daily average for {metric_names[metric]} {'increased' if daily_change > 0 else 'decreased'} "
                                     f"by {abs(daily_change):.1f}% ({daily_avg2:,.0f} vs {daily_avg1:,.0f} per day)")
                
                # API to Portal ratio insights
                if abs(ratio_change) > 10:
                    insights.append(f"- API to Portal ratio {'increased' if ratio_change > 0 else 'decreased'} by {abs(ratio_change):.1f}% "
                                 f"({api_ratio2:.2f}:1 vs {api_ratio1:.2f}:1)")
                
                # CMGR insights
                if months_between > 0:
                    for metric in metrics:
                        val1 = data1[metric]
                        val2 = data2[metric]
                        cmgr = (pow(val2/val1, 1/months_between) - 1) * 100
                        
                        if abs(cmgr) > 2:  # Significant monthly growth rate
                            insights.append(f"- {metric_names[metric]} shows a {abs(cmgr):.2f}% compound monthly "
                                         f"{'growth' if cmgr > 0 else 'decline'} rate over {months_between} months")
                
                # Add relative performance analysis
                st.markdown("### Relative Performance Analysis")
                
                # Calculate relative performance of channels
                total_growth = ((data2['TotalTxns'] - data1['TotalTxns']) / data1['TotalTxns']) * 100
                api_growth = ((data2['TotalAPITxns'] - data1['TotalAPITxns']) / data1['TotalAPITxns']) * 100
                portal_growth = ((data2['TotalPortalTxns'] - data1['TotalPortalTxns']) / data1['TotalPortalTxns']) * 100
                
                # Create a performance comparison DataFrame
                performance_df = pd.DataFrame({
                    'Metric': ['Total', 'API', 'Portal'],
                    'Growth Rate': [total_growth, api_growth, portal_growth]
                })
                
                # Display relative performance
                st.dataframe(
                    performance_df.style.format({'Growth Rate': '{:+.1f}%'})
                                    .background_gradient(cmap='RdYlGn', subset=['Growth Rate']),
                    hide_index=True
                )
                
                # Add relative performance insights
                channel_differences = {
                    'API vs Total': api_growth - total_growth,
                    'Portal vs Total': portal_growth - total_growth,
                    'API vs Portal': api_growth - portal_growth
                }
                
                for comparison, diff in channel_differences.items():
                    if abs(diff) > 5:  # Significant relative performance difference
                        parts = comparison.split(' vs ')
                        better_channel = parts[0] if diff > 0 else parts[1]
                        worse_channel = parts[1] if diff > 0 else parts[0]
                        insights.append(f"- {better_channel} outperformed {worse_channel} by {abs(diff):.1f} percentage points")
                
                # Add Channel Mix Trendline Analysis
                if show_trendlines and len(all_trendline_data) > 0:
                    st.markdown("### Channel Mix Trendline Analysis")
                    st.markdown("Comparison of actual channel mix versus predicted mix from trendlines")
                    
                    # Create a DataFrame for analysis
                    mix_analysis = pd.DataFrame()
                    mix_analysis['Period'] = filtered_df['Period']
                    
                    # Calculate actual channel mix percentages
                    mix_analysis['Actual API %'] = (filtered_df['TotalAPITxns'] / filtered_df['TotalTxns'] * 100).round(1)
                    mix_analysis['Actual Portal %'] = (filtered_df['TotalPortalTxns'] / filtered_df['TotalTxns'] * 100).round(1)
                    
                    # Get trendline data
                    trendline_df = pd.concat(all_trendline_data)
                    trendline_pivot = trendline_df.pivot(
                        index='Period',
                        columns='Metric',
                        values='Trendline'
                    ).reset_index()
                    
                    # Calculate predicted channel mix percentages
                    mix_analysis['Predicted API %'] = (trendline_pivot['TotalAPITxns'] / trendline_pivot['TotalTxns'] * 100).round(1)
                    mix_analysis['Predicted Portal %'] = (trendline_pivot['TotalPortalTxns'] / trendline_pivot['TotalTxns'] * 100).round(1)
                    
                    # Calculate variances
                    mix_analysis['API Mix Variance'] = (mix_analysis['Actual API %'] - mix_analysis['Predicted API %']).round(1)
                    mix_analysis['Portal Mix Variance'] = (mix_analysis['Actual Portal %'] - mix_analysis['Predicted Portal %']).round(1)
                    
                    # Create two columns for visualization
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.markdown("#### Channel Mix Variances")
                        # Style the DataFrame for display
                        styled_df = mix_analysis.style.format({
                            'Actual API %': '{:.1f}%',
                            'Predicted API %': '{:.1f}%',
                            'API Mix Variance': '{:+.1f}%',
                            'Actual Portal %': '{:.1f}%',
                            'Predicted Portal %': '{:.1f}%',
                            'Portal Mix Variance': '{:+.1f}%'
                        }).background_gradient(
                            subset=['API Mix Variance', 'Portal Mix Variance'],
                            cmap='RdYlGn',
                            vmin=-10,
                            vmax=10
                        )
                        st.dataframe(styled_df)
                    
                    with col2:
                        st.markdown("#### Key Observations")
                        mix_insights = []
                        
                        # Calculate average variances
                        avg_api_variance = mix_analysis['API Mix Variance'].mean()
                        avg_portal_variance = mix_analysis['Portal Mix Variance'].mean()
                        
                        # Add insights about average channel mix variance
                        if abs(avg_api_variance) > 2:
                            mix_insights.append(
                                f"- API channel is averaging {abs(avg_api_variance):.1f}% "
                                f"{'above' if avg_api_variance > 0 else 'below'} predicted mix"
                            )
                        
                        if abs(avg_portal_variance) > 2:
                            mix_insights.append(
                                f"- Portal channel is averaging {abs(avg_portal_variance):.1f}% "
                                f"{'above' if avg_portal_variance > 0 else 'below'} predicted mix"
                            )
                        
                        # Add insights about specific periods with significant variance
                        significant_variances = mix_analysis[
                            (abs(mix_analysis['API Mix Variance']) > 5) |
                            (abs(mix_analysis['Portal Mix Variance']) > 5)
                        ]
                        
                        for _, row in significant_variances.iterrows():
                            if abs(row['API Mix Variance']) > 5:
                                mix_insights.append(
                                    f"- In {row['Period']}, API mix was {abs(row['API Mix Variance']):.1f}% "
                                    f"{'higher' if row['API Mix Variance'] > 0 else 'lower'} than predicted"
                                )
                            if abs(row['Portal Mix Variance']) > 5:
                                mix_insights.append(
                                    f"- In {row['Period']}, Portal mix was {abs(row['Portal Mix Variance']):.1f}% "
                                    f"{'higher' if row['Portal Mix Variance'] > 0 else 'lower'} than predicted"
                                )
                        
                        # Display mix insights
                        if mix_insights:
                            for insight in mix_insights:
                                st.markdown(insight)
                        else:
                            st.markdown("Channel mix is closely following predicted trends with no significant deviations.")
                
                # Display all insights
                st.markdown("### Key Insights Summary")
                for insight in insights:
                    st.markdown(insight)
                
                if not insights:
                    st.markdown("No significant variations detected between the selected months.")
    
    with tab_data:
        if not df.empty:
            st.subheader("Actual Data")
            # Format the data for display
            display_df = filtered_df.copy()
            
            # Format numeric columns
            numeric_cols = ['TotalTxns', 'TotalAPITxns', 'TotalPortalTxns']
            for col in numeric_cols:
                display_df[col] = display_df[col].apply(lambda x: f"{x:,}")
            
            # Remove technical columns
            columns_to_drop = ['period_index']
            display_df = display_df.drop(columns=[col for col in columns_to_drop if col in display_df.columns])
            
            # Display the actual data
            st.dataframe(display_df, use_container_width=True)
            
            # Display trendline data if available
            if all_trendline_data and show_trendlines:
                st.subheader("Trendline Data")
                
                # Combine all trendline data
                trendline_df = pd.concat(all_trendline_data)
                
                # Pivot the data to show all metrics in columns
                trendline_pivot = trendline_df.pivot(
                    index='Period',
                    columns='Metric',
                    values='Trendline'
                ).reset_index()
                
                # Format the numbers with commas
                for col in trendline_pivot.columns:
                    if col != 'Period':
                        trendline_pivot[col] = trendline_pivot[col].apply(lambda x: f"{int(x):,}")
                
                # Display the trendline data
                st.dataframe(trendline_pivot, use_container_width=True)
            
            # Add download buttons
            col1, col2 = st.columns(2)
            with col1:
                csv = display_df.to_csv(index=False)
                st.download_button(
                    label="Download actual data as CSV",
                    data=csv,
                    file_name="digital_usage_trends_actual.csv",
                    mime="text/csv"
                )
            
            if all_trendline_data and show_trendlines:
                with col2:
                    trendline_csv = trendline_pivot.to_csv(index=False)
                    st.download_button(
                        label="Download trendline data as CSV",
                        data=trendline_csv,
                        file_name="digital_usage_trends_trendline.csv",
                        mime="text/csv"
                    )
        else:
            st.warning("No data available")

    with tab_insights:
        try:
            st.markdown("### AI-Generated Insights")
            
            # Add buttons for generating and refreshing insights
            col1, col2, col3 = st.columns([2, 2, 1])
            with col2:
                generate = st.button("🤖 Generate Insights", key="generate_insights")
            with col3:
                refresh = st.button("🔄 Refresh", key="refresh_insights")
            
            # Generate key for this dataset
            insight_key = f"trends_{month2}"
            
            # Queue insight generation if needed or refresh requested
            if generate or refresh:
                # Prepare data for insights - only include necessary columns
                insight_data = filtered_df[['Period', 'TotalTxns', 'TotalAPITxns', 'TotalPortalTxns']].copy()
                
                queue_insight_generation(
                    insight_key,
                    "digital_usage_trends",
                    month2,
                    insight_data,
                    f"Digital Usage Trends - {month2}",
                    is_priority=True
                )
            
            # Display insights if available
            if insight_key in st.session_state.section_insights:
                insights = st.session_state.section_insights[insight_key]
                
                # Create tabs for different time windows
                dimension_names = {
                    1: "24-Month Analysis",
                    2: "12-Month Analysis",
                    3: "6-Month Analysis",
                    4: "3-Month Analysis",
                    5: "Current Month Analysis"
                }
                
                for dimension in sorted(insights.keys()):
                    with st.expander(f"{dimension_names[dimension]} ({insights[dimension]['data_range']})"):
                        # Add the insights text with styling
                        st.markdown(
                            f"""<div class="ai-insights">
                                {insights[dimension]['text']}
                            </div>""",
                            unsafe_allow_html=True
                        )
                        # Add generation timestamp
                        st.caption(
                            f"""<div class="insight-timestamp">
                                Generated: {insights[dimension]['generated_at']}
                            </div>""",
                            unsafe_allow_html=True
                        )
            elif st.session_state.currently_processing == insight_key:
                st.info("⏳ Generating insights...")
            else:
                st.info("Click 'Generate Insights' to analyze the data.")
            
        except Exception as e:
            logger.error(f"Error in insights tab: {str(e)}")
            st.error(f"Error generating insights: {str(e)}")

# Add Digital Usage By Service visualization at the bottom
def display_service_usage():
    """Display Digital Usage By Service visualization"""
    logger.info("Displaying Digital Usage By Service")
    
    try:
        # Get the Digital Usage Periods
        digital_usage_periods = conn.query('select distinct(Period) as Period from DigitalUsageDetailView')

        # Sort by Period
        digital_usage_periods['PeriodDateTime'] = pd.to_datetime(digital_usage_periods['Period'], format='%b-%y')
        digital_usage_periods = digital_usage_periods.sort_values(by='PeriodDateTime', ascending=False)

        # Display period selection
        selected_period = st.selectbox(
            "Select a Digital Usage Period",
            digital_usage_periods,
            key="DisplayServiceUsageByMonth"
        )

        # Enhanced SQL query to include total transactions for percentage calculation
        digital_usage_by_API_sql = """
        WITH ServiceTotals AS (
            SELECT 
                serviceName,
                SUM(Total) as TotalTxns
            FROM DigitalUsageDetailView
            WHERE period = :Period
            GROUP BY serviceName
        )
        SELECT 
           a.APICode, 
           a.APIName as ServiceName, 
           CASE WHEN a.UsedForConnect = 'Y' THEN 1 ELSE 0 END as UsedForConnect,
           CASE WHEN a.UsedForAssure = 'Y' THEN 1 ELSE 0 END as UsedForAssure,
           a.LatestVersion, 
           ASCert.certcount as APICertCount, 
           (ASCert.certcount - IFNULL(ASNotOnLatestVersion.notonlatestversioncount, 0)) as OnLatestVersionCount,
           IFNULL(ASNotOnLatestVersion.notonlatestversioncount, 0) as NotOnLatestVersionCount,
           round((1.0 * (ASCert.certcount - IFNULL(ASNotOnLatestVersion.notonlatestversioncount, 0)) / ASCert.certcount),2) as CertOnLatestVersionPercent,
           APIUsage.utilcount as APIUtilCount,
           COALESCE(APIUsage.TotalTxnCount, 0) as TotalAPITxns,
           COALESCE(PortalUsage.TotalTxnCount, 0) as TotalPortalTxns,
           CASE 
               WHEN COALESCE(st.TotalTxns, 0) > 0 THEN ROUND(100.0 * COALESCE(APIUsage.TotalTxnCount, 0) / st.TotalTxns, 1)
               ELSE 0 
           END as APIPercentage
        FROM APIs a 
        LEFT JOIN (SELECT *, count(*) as certcount FROM AccessSeekerCertAPIsDetailView ascv GROUP BY CertifiedAPIName) as ASCert 
            ON a.APIName = ascert.CertifiedAPIName
        LEFT JOIN (SELECT serviceName, count(distinct(accessSeekerId)) as utilcount, sum(Total) as TotalTxnCount 
                  FROM DigitalUsageDetailView duv 
                  WHERE businessChannel = 'APIGWY' AND period = :Period 
                  GROUP BY serviceName) as APIUsage 
            ON a.UsageReportAPIName = APIUsage.serviceName 
        LEFT JOIN (SELECT serviceName, count(distinct(accessSeekerId)) as utilcount, sum(Total) as TotalTxnCount 
                  FROM DigitalUsageDetailView duv 
                  WHERE businessChannel = 'ServicePortal' AND period = :Period 
                  GROUP BY serviceName) as PortalUsage 
            ON a.UsageReportAPIName = PortalUsage.serviceName 
        LEFT JOIN (SELECT CertifiedAPIName, count(*) as notonlatestversioncount 
                  FROM AccessSeekerCertAPIsDetailView 
                  WHERE LatestCertifiedVersion != LatestVersion 
                  GROUP BY CertifiedAPIName) as ASNotOnLatestVersion 
            ON a.APIName = ASNotOnLatestVersion.CertifiedAPIName
        LEFT JOIN ServiceTotals st ON a.UsageReportAPIName = st.serviceName
        WHERE (a.UsedForConnect = 'Y' OR a.UsedForAssure = 'Y')
        AND (COALESCE(APIUsage.TotalTxnCount, 0) > 0 OR COALESCE(PortalUsage.TotalTxnCount, 0) > 0)
        """

        logger.info(f"Executing service usage query for period: {selected_period}")
        
        # Execute the query
        digital_usage_by_API = conn.query(
            digital_usage_by_API_sql, 
            params={"Period": selected_period}
        )

        if digital_usage_by_API.empty:
            logger.warning("No data returned for service usage query")
            st.warning("No data available for the selected period")
            return

        # Create tabs for visualization
        tab_chart, tab_data = st.tabs(["Graph", "Data"])

        with tab_chart:
            # Original chart for total transactions
            total_chart = (
                alt.Chart(digital_usage_by_API)
                .transform_fold(['TotalAPITxns', 'TotalPortalTxns'])
                .mark_bar()
                .encode(
                    x=alt.X("ServiceName", title='Service Name').sort("-y"),
                    y=alt.Y("value:Q", title="Txn Count"),
                    color=alt.Color(
                        "key:N",
                        scale=alt.Scale(
                            domain=['TotalAPITxns', 'TotalPortalTxns'],
                            range=['#0066cc', '#99ccff']
                        )
                    ),
                    order=alt.Order('key:N', sort='ascending')
                )
                .properties(
                    title=f'Digital Usage By Service ({selected_period})',
                    height=1000
                )
                .configure_title(
                    fontSize=20,
                    font='Courier',
                    anchor='middle',
                    color='blue'
                )
                .configure_axisX(titleColor='black', titleFontWeight='bold', labelAngle=45)
                .configure_axisY(titleColor='black', titleFontWeight='bold')
            )

            # Use save_chart instead of st.altair_chart
            save_chart(total_chart, f"Digital_Usage_By_Service_{selected_period}")

            # Create Connect and Assure API percentage charts
            st.markdown("---")
            st.subheader("API Usage Percentage by Service Type")

            # Filter and prepare data for Connect APIs
            connect_data = digital_usage_by_API[digital_usage_by_API['UsedForConnect'] == 1].copy()
            connect_data = connect_data.sort_values('APIPercentage', ascending=False)

            # Filter and prepare data for Assure APIs
            assure_data = digital_usage_by_API[digital_usage_by_API['UsedForAssure'] == 1].copy()
            assure_data = assure_data.sort_values('APIPercentage', ascending=False)

            # Create columns for the two charts
            col1, col2 = st.columns(2)

            base_chart_config = {
                'height': 300,
                'width': 'container',
                'color': '#0066cc',
                'grid': True,
                'axis_title_color': 'black',
                'axis_title_font_weight': 'bold',
                'title_font_size': 16,
                'title_font': 'Courier',
                'title_anchor': 'middle',
                'title_color': 'blue'
            }

            with col1:
                connect_chart = (
                    alt.Chart(connect_data)
                    .mark_bar(color=base_chart_config['color'])
                    .encode(
                        x=alt.X('ServiceName', title='Service Name'),
                        y=alt.Y('APIPercentage:Q', title='% API Txns', scale=alt.Scale(domain=[0, 100])),
                        tooltip=[
                            alt.Tooltip('ServiceName:N', title='Service'),
                            alt.Tooltip('APIPercentage:Q', title='API %', format='.1f')
                        ]
                    )
                    .properties(
                        title='Key Connect Services (API%)',
                        height=base_chart_config['height']
                    )
                    .configure_title(
                        fontSize=base_chart_config['title_font_size'],
                        font=base_chart_config['title_font'],
                        anchor=base_chart_config['title_anchor'],
                        color=base_chart_config['title_color']
                    )
                    .configure_axisX(labelAngle=45)
                    .configure_axisY(grid=base_chart_config['grid'])
                )
                # Use save_chart instead of st.altair_chart
                save_chart(connect_chart, f"Connect_Services_API_Usage_{selected_period}")

            with col2:
                assure_chart = (
                    alt.Chart(assure_data)
                    .mark_bar(color=base_chart_config['color'])
                    .encode(
                        x=alt.X('ServiceName', title='Service Name'),
                        y=alt.Y('APIPercentage:Q', title='% API Txns', scale=alt.Scale(domain=[0, 100])),
                        tooltip=[
                            alt.Tooltip('ServiceName:N', title='Service'),
                            alt.Tooltip('APIPercentage:Q', title='API %', format='.1f')
                        ]
                    )
                    .properties(
                        title='Key Assure Services (API%)',
                        height=base_chart_config['height']
                    )
                    .configure_title(
                        fontSize=base_chart_config['title_font_size'],
                        font=base_chart_config['title_font'],
                        anchor=base_chart_config['title_anchor'],
                        color=base_chart_config['title_color']
                    )
                    .configure_axisX(labelAngle=45)
                    .configure_axisY(grid=base_chart_config['grid'])
                )
                # Use save_chart instead of st.altair_chart
                save_chart(assure_chart, f"Assure_Services_API_Usage_{selected_period}")

        with tab_data:
            st.dataframe(digital_usage_by_API)

        logger.info("Successfully displayed service usage visualization")

    except Exception as e:
        logger.error(f"Error displaying service usage: {e}")
        st.error(f"Error displaying service usage: {e}")

def display_api_adoption():
    """Display RSP API Adoption & Utilisation visualization"""
    logger.info("Displaying RSP API Adoption & Utilisation")
    
    try:
        # Get the selected period from the existing selector
        selected_period = st.session_state.get("DisplayServiceUsageByMonth")
        
        if not selected_period:
            logger.warning("No period selected")
            st.warning("Please select a period from the Digital Usage By Service section above")
            return

        # Query to get API adoption and utilization data
        api_adoption_sql = """
        WITH APIUtilization AS (
            SELECT 
                a.APIName,
                COUNT(DISTINCT duv.accessSeekerId) as UtilCount
            FROM APIs a
            LEFT JOIN DigitalUsageDetailView duv 
                ON a.UsageReportAPIName = duv.serviceName 
                AND duv.period = :Period
                AND duv.businessChannel = 'APIGWY'
            GROUP BY a.APIName
        )
        SELECT 
            a.APIName,
            COUNT(DISTINCT ascv.AccessSeekerId) as CertCount,
            COALESCE(au.UtilCount, 0) as UtilCount
        FROM APIs a
        LEFT JOIN AccessSeekerCertAPIsDetailView ascv 
            ON a.APIName = ascv.CertifiedAPIName
        LEFT JOIN APIUtilization au 
            ON a.APIName = au.APIName
        GROUP BY a.APIName
        HAVING CertCount > 0 OR UtilCount > 0
        ORDER BY CertCount DESC
        """

        logger.info(f"Executing API adoption query for period: {selected_period}")
        
        # Execute the query
        api_adoption_data = conn.query(
            api_adoption_sql, 
            params={"Period": selected_period}
        )

        if api_adoption_data.empty:
            logger.warning("No data returned for API adoption query")
            st.warning("No data available for the selected period")
            return

        # Create tabs for visualization
        tab_chart, tab_data = st.tabs(["Graph", "Data"])

        with tab_chart:
            # Transform data for the chart
            chart_data = pd.melt(
                api_adoption_data,
                id_vars=['APIName'],
                value_vars=['CertCount', 'UtilCount'],
                var_name='Metric',
                value_name='Count'
            )

            # Create the visualization
            adoption_chart = (
                alt.Chart(chart_data)
                .mark_bar()
                .encode(
                    x=alt.X('APIName:N', title='API Name', sort='-y'),
                    y=alt.Y('Count:Q', title='RSP Count'),
                    color=alt.Color(
                        'Metric:N',
                        scale=alt.Scale(
                            domain=['CertCount', 'UtilCount'],
                            range=['#0066cc', '#99ccff']
                        ),
                        legend=alt.Legend(
                            title='',
                            orient='top-right',
                            symbolType='square'
                        )
                    )
                )
                .properties(
                    title=f'RSP API Adoption & Utilisation ({selected_period})',
                    height=400
                )
                .configure_title(
                    fontSize=20,
                    font='Courier',
                    anchor='middle',
                    color='blue'
                )
                .configure_axisX(
                    labelAngle=45,
                    titleColor='black',
                    titleFontWeight='bold'
                )
                .configure_axisY(
                    grid=True,
                    titleColor='black',
                    titleFontWeight='bold'
                )
                .configure_legend(
                    labelFontSize=12,
                    symbolSize=100,
                    labelColor='black'
                )
            )

            # Use save_chart instead of st.altair_chart
            save_chart(adoption_chart, f"RSP_API_Adoption_and_Utilisation_{selected_period}")

        with tab_data:
            st.dataframe(api_adoption_data)

        logger.info("Successfully displayed API adoption visualization")

    except Exception as e:
        logger.error(f"Error displaying API adoption: {e}")
        st.error(f"Error displaying API adoption: {e}")

def display_rsp_usage():
    """Display Digital Usage by RSP visualization"""
    logger.info("Displaying Digital Usage by RSP")
    
    try:
        # Get the selected period from the existing selector
        selected_period = st.session_state.get("DisplayServiceUsageByMonth")
        
        if not selected_period:
            logger.warning("No period selected")
            st.warning("Please select a period from the Digital Usage By Service section above")
            return

        # Query to get RSP usage data with additional metrics
        rsp_usage_sql = """
        WITH RSPTotals AS (
            SELECT 
                RSPName,
                CAST(SUM(Total) as INTEGER) as TotalTxns,
                ROW_NUMBER() OVER (ORDER BY SUM(Total) DESC) as DigitalVolRank,
                ROUND(SUM(CASE WHEN businessChannel = 'APIGWY' THEN Total ELSE 0 END) * 100.0 / NULLIF(SUM(Total), 0), 1) as APIPercentage
            FROM DigitalUsageDetailView
            WHERE Period = :Period
            GROUP BY RSPName
            HAVING TotalTxns > 0
        )
        SELECT 
            rt.DigitalVolRank,
            rt.RSPName,
            COALESCE(rt.APIPercentage, 0) as APIPercentage,
            COALESCE(acs.ServiceCountSept24, 0) as TotalServices,
            CASE 
                WHEN COALESCE(acs.ServiceCountSept24, 0) > 0 
                THEN ROUND(CAST(rt.TotalTxns AS FLOAT) / NULLIF(acs.ServiceCountSept24, 0), 1)
                ELSE 0 
            END as TxnPerService,
            COALESCE(api.TotalAPITxns, 0) as TotalAPITxns,
            COALESCE(portal.TotalPortalTxns, 0) as TotalPortalTxns,
            rt.TotalTxns
        FROM RSPTotals rt
        LEFT JOIN (
            SELECT 
                RSPName,
                CAST(SUM(Total) as INTEGER) as TotalAPITxns
            FROM DigitalUsageDetailView
            WHERE Period = :Period
            AND businessChannel = 'APIGWY'
            GROUP BY RSPName
        ) api ON rt.RSPName = api.RSPName
        LEFT JOIN (
            SELECT 
                RSPName,
                CAST(SUM(Total) as INTEGER) as TotalPortalTxns
            FROM DigitalUsageDetailView
            WHERE Period = :Period
            AND businessChannel = 'ServicePortal'
            GROUP BY RSPName
        ) portal ON rt.RSPName = portal.RSPName
        LEFT JOIN AccessSeekers acs ON rt.RSPName = acs.RSPName
        ORDER BY rt.TotalTxns DESC
        """

        logger.info(f"Executing RSP usage query for period: {selected_period}")
        
        # Execute the query
        rsp_usage_data = conn.query(
            rsp_usage_sql, 
            params={"Period": selected_period}
        )

        if rsp_usage_data.empty:
            logger.warning("No data returned for RSP usage query")
            st.warning("No data available for the selected period")
            return

        # Fill NaN values with 0 and convert numeric columns to appropriate types
        numeric_columns = {
            'DigitalVolRank': 'int',
            'APIPercentage': 'float',
            'TotalServices': 'int',
            'TxnPerService': 'float',
            'TotalAPITxns': 'int',
            'TotalPortalTxns': 'int',
            'TotalTxns': 'int'
        }

        # Replace NaN/None with 0 and convert to appropriate types
        for col, dtype in numeric_columns.items():
            if col in rsp_usage_data.columns:
                rsp_usage_data[col] = rsp_usage_data[col].fillna(0)
                try:
                    if dtype == 'int':
                        rsp_usage_data[col] = rsp_usage_data[col].astype('Int64')  # Use nullable integer type
                    else:
                        rsp_usage_data[col] = rsp_usage_data[col].astype(dtype)
                except Exception as e:
                    logger.error(f"Error converting column {col} to {dtype}: {e}")
                    st.error(f"Error processing data for column {col}")
                    return

        # Create tabs for visualization
        tab_chart, tab_data = st.tabs(["Graph", "Data"])

        with tab_chart:
            try:
                # Transform data for the chart
                chart_data = pd.melt(
                    rsp_usage_data,
                    id_vars=['RSPName'],
                    value_vars=['TotalAPITxns', 'TotalPortalTxns'],
                    var_name='Channel',
                    value_name='Txn Count'
                )

                # Create the visualization
                rsp_usage_chart = (
                    alt.Chart(chart_data)
                    .mark_bar()
                    .encode(
                        x=alt.X('RSPName:N', title='RSP Name', sort='-y'),
                        y=alt.Y('Txn Count:Q', title='Txn Count'),
                        color=alt.Color(
                            'Channel:N',
                            scale=alt.Scale(
                                domain=['TotalAPITxns', 'TotalPortalTxns'],
                                range=['#0066cc', '#99ccff']
                            ),
                            legend=alt.Legend(
                                title='',
                                orient='top-right',
                                symbolType='square'
                            )
                        ),
                        order=alt.Order('Channel:N', sort='ascending'),
                        tooltip=[
                            alt.Tooltip('RSPName:N', title='RSP'),
                            alt.Tooltip('Channel:N', title='Channel'),
                            alt.Tooltip('Txn Count:Q', title='Transactions', format=',')
                        ]
                    )
                    .properties(
                        title=f'Digital Usage By RSP ({selected_period})',
                        height=500
                    )
                    .configure_title(
                        fontSize=20,
                        font='Courier',
                        anchor='middle',
                        color='blue'
                    )
                    .configure_axisX(
                        labelAngle=45,
                        titleColor='black',
                        titleFontWeight='bold'
                    )
                    .configure_axisY(
                        grid=True,
                        titleColor='black',
                        titleFontWeight='bold'
                    )
                    .configure_legend(
                        labelFontSize=12,
                        symbolSize=100,
                        labelColor='black'
                    )
                )

                # Use save_chart instead of st.altair_chart
                save_chart(rsp_usage_chart, f"Digital_Usage_By_RSP_{selected_period}")
            except Exception as e:
                logger.error(f"Error creating chart: {e}")
                st.error("Error creating the visualization chart")
                return

        with tab_data:
            try:
                # Format the data for display
                display_df = rsp_usage_data.copy()
                
                # Rename columns for better display
                display_df = display_df.rename(columns={
                    'DigitalVolRank': 'Digital Vol Rank',
                    'APIPercentage': '% Txns via APIs',
                    'TotalServices': 'Total Services',
                    'TxnPerService': 'Ratio of Digital Txn to Active Services'
                })
                
                # Reorder columns to match the screenshot
                display_df = display_df[[
                    'Digital Vol Rank',
                    'RSPName',
                    '% Txns via APIs',
                    'Total Services',
                    'Ratio of Digital Txn to Active Services',
                    'TotalAPITxns',
                    'TotalPortalTxns',
                    'TotalTxns'
                ]]
                
                # Format numeric columns safely
                def safe_format(val, format_type):
                    try:
                        if pd.isna(val) or val is None:
                            return '0'
                        if format_type == 'percent':
                            return f"{float(val):.1f}%"
                        elif format_type == 'int':
                            return f"{int(val):,}"
                        elif format_type == 'float':
                            return f"{float(val):.1f}"
                        return str(val)
                    except Exception as e:
                        logger.error(f"Error formatting value {val}: {e}")
                        return '0'

                display_df['% Txns via APIs'] = display_df['% Txns via APIs'].apply(lambda x: safe_format(x, 'percent'))
                display_df['Total Services'] = display_df['Total Services'].apply(lambda x: safe_format(x, 'int'))
                display_df['Ratio of Digital Txn to Active Services'] = display_df['Ratio of Digital Txn to Active Services'].apply(lambda x: safe_format(x, 'float'))
                
                for col in ['TotalAPITxns', 'TotalPortalTxns', 'TotalTxns']:
                    display_df[col] = display_df[col].apply(lambda x: safe_format(x, 'int'))
                
                st.dataframe(display_df)
            except Exception as e:
                logger.error(f"Error formatting data display: {e}")
                st.error("Error formatting the data display")
                return

        logger.info("Successfully displayed RSP usage visualization")

    except Exception as e:
        logger.error(f"Error displaying RSP usage: {e}")
        st.error(f"Error displaying RSP usage: {e}")

def display_api_percentage_by_rsp():
    """Display Digital Usage API Percentage by RSP visualization"""
    logger.info("Displaying Digital Usage API Percentage by RSP")
    
    try:
        # Get the selected period from the existing selector
        selected_period = st.session_state.get("DisplayServiceUsageByMonth")
        
        if not selected_period:
            logger.warning("No period selected")
            st.warning("Please select a period from the Digital Usage By Service section above")
            return

        # Updated query to include ranks
        api_percentage_sql = """
        WITH RSPTotals AS (
            SELECT 
                RSPName,
                SUM(CASE WHEN businessChannel = 'APIGWY' THEN Total ELSE 0 END) as APITxns,
                SUM(Total) as TotalTxns,
                RANK() OVER (ORDER BY SUM(Total) DESC) as DigitalVolRank
            FROM DigitalUsageDetailView
            WHERE Period = :Period
            GROUP BY RSPName
            HAVING TotalTxns > 0
        ),
        ServiceCounts AS (
            SELECT 
                RSPName,
                ServiceCountSept24,
                RANK() OVER (ORDER BY ServiceCountSept24 DESC) as ServiceCountRank
            FROM AccessSeekers
        )
        SELECT 
            rt.DigitalVolRank as "Digital Vol Rank",
            rt.RSPName as "RSP",
            ROUND(CAST(rt.APITxns AS FLOAT) * 100 / rt.TotalTxns, 1) as "API%",
            sc.ServiceCountRank as "~Svc Count Rank"
        FROM RSPTotals rt
        LEFT JOIN ServiceCounts sc ON rt.RSPName = sc.RSPName
        ORDER BY rt.DigitalVolRank ASC
        """

        logger.info(f"Executing API percentage query for period: {selected_period}")
        
        # Execute the query
        api_percentage_data = conn.query(
            api_percentage_sql, 
            params={"Period": selected_period}
        )

        if api_percentage_data.empty:
            logger.warning("No data returned for API percentage query")
            st.warning("No data available for the selected period")
            return

        # Create tabs for visualization
        tab_chart, tab_data = st.tabs(["Graph", "Data"])

        with tab_chart:
            # Create the visualization using the API percentage
            chart_data = api_percentage_data.copy()
            api_percentage_chart = (
                alt.Chart(chart_data)
                .mark_bar(color='#0066cc')
                .encode(
                    x=alt.X('RSP:N', title='RSP Name', sort='-y'),
                    y=alt.Y('API%:Q', 
                           title='API Usage %',
                           scale=alt.Scale(domain=[0, 100])),
                    tooltip=[
                        alt.Tooltip('RSP:N', title='RSP'),
                        alt.Tooltip('API%:Q', title='API %', format='.1f'),
                        alt.Tooltip('Digital Vol Rank:Q', title='Digital Vol Rank'),
                        alt.Tooltip('~Svc Count Rank:Q', title='Service Count Rank')
                    ]
                )
                .properties(
                    title=f'Digital Usage API% ({selected_period})',
                    height=500
                )
                .configure_title(
                    fontSize=20,
                    font='Courier',
                    anchor='middle',
                    color='blue'
                )
                .configure_axisX(
                    labelAngle=45,
                    titleColor='black',
                    titleFontWeight='bold'
                )
                .configure_axisY(
                    grid=True,
                    titleColor='black',
                    titleFontWeight='bold'
                )
            )

            # Use save_chart instead of st.altair_chart
            save_chart(api_percentage_chart, f"Digital_Usage_API_Percentage_By_RSP_{selected_period}")

        with tab_data:
            # Format the data for display
            display_df = api_percentage_data.copy()
            
            # Format API percentage
            display_df['API%'] = display_df['API%'].apply(lambda x: f"{x:.1f}%")
            
            # Ensure integer formatting for rank columns
            display_df['Digital Vol Rank'] = display_df['Digital Vol Rank'].astype(int)
            display_df['~Svc Count Rank'] = display_df['~Svc Count Rank'].fillna(0).astype(int)
            
            # Display the dataframe
            st.dataframe(display_df, hide_index=True)

        logger.info("Successfully displayed API percentage visualization")

    except Exception as e:
        logger.error(f"Error displaying API percentage: {e}")
        st.error(f"Error displaying API percentage: {e}")

def display_service_portal_users():
    """Display Service Portal Users visualization with placeholder"""
    logger.info("Displaying Service Portal Users placeholder")
    
    try:
        # Create a placeholder dataframe
        placeholder_data = pd.DataFrame({
            'Metric': ['Total Logins', 'Unique Logins', 'Active Users'],
            'Value': [0, 0, 0]
        })

        # Create the placeholder chart
        placeholder_chart = (
            alt.Chart(placeholder_data)
            .mark_bar(color='#0066cc')
            .encode(
                x=alt.X('Metric:N', title='Metric'),
                y=alt.Y('Value:Q', title='Count')
            )
            .properties(
                title='Service Portal Users - Data Coming Soon',
                height=400
            )
        )

        # Add a "Data Coming Soon" text overlay
        text_overlay = (
            alt.Chart({'values': [{'text': 'Data Coming Soon'}]})
            .mark_text(
                fontSize=24,
                color='gray',
                angle=0,
                align='center',
                baseline='middle',
                dx=150,  # Adjust horizontal position
                dy=150   # Adjust vertical position
            )
            .encode(
                text='text:N'
            )
        )

        # Combine the chart and text
        combined_chart = alt.layer(placeholder_chart, text_overlay)

        # Use save_chart to display
        save_chart(combined_chart, "Service_Portal_Users")

        logger.info("Successfully displayed Service Portal Users placeholder")

    except Exception as e:
        logger.error(f"Error displaying Service Portal Users placeholder: {e}")
        st.error(f"Error displaying Service Portal Users placeholder: {e}")

def display_public_website_users():
    """Display Public Website Users visualization with placeholder"""
    logger.info("Displaying Public Website Users placeholder")
    
    try:
        # Create a placeholder dataframe
        placeholder_data = pd.DataFrame({
            'Metric': ['Total Users', 'Unique Users', 'Page Views'],
            'Value': [0, 0, 0]
        })

        # Create the placeholder chart
        placeholder_chart = (
            alt.Chart(placeholder_data)
            .mark_bar(color='#0066cc')
            .encode(
                x=alt.X('Metric:N', title='Metric'),
                y=alt.Y('Value:Q', title='Count')
            )
            .properties(
                title='Public Website Users - Data Coming Soon',
                height=400
            )
        )

        # Add a "Data Coming Soon" text overlay
        text_overlay = (
            alt.Chart({'values': [{'text': 'Data Coming Soon'}]})
            .mark_text(
                fontSize=24,
                color='gray',
                angle=0,
                align='center',
                baseline='middle',
                dx=150,  # Adjust horizontal position
                dy=150   # Adjust vertical position
            )
            .encode(
                text='text:N'
            )
        )

        # Combine the chart and text
        combined_chart = alt.layer(placeholder_chart, text_overlay)

        # Use save_chart to display
        save_chart(combined_chart, "Public_Website_Users")

        logger.info("Successfully displayed Public Website Users placeholder")

    except Exception as e:
        logger.error(f"Error displaying Public Website Users placeholder: {e}")
        st.error(f"Error displaying Public Website Users placeholder: {e}")

# Main content
display_trends()

# Add a divider before the Digital Usage section
st.markdown("---")
st.header("Digital Usage By Service", divider='blue')
display_service_usage()

# Add a divider before the API Adoption section
st.markdown("---")
st.header("RSP API Adoption & Utilisation", divider='blue')
display_api_adoption()

# Add a divider before the RSP Usage section
st.markdown("---")
st.header("Digital Usage By RSP", divider='blue')
display_rsp_usage()

# Add a divider before the API Percentage section
st.markdown("---")
st.header("Digital Usage API% By RSP", divider='blue')
display_api_percentage_by_rsp()

# Add a divider before the Service Portal Users section
st.markdown("---")
st.header("Service Portal Users", divider='blue')
display_service_portal_users()

# Add a divider before the Public Website Users section
st.markdown("---")
st.header("Public Website Users", divider='blue')
display_public_website_users()

# Process any pending insights
process_next_insight()