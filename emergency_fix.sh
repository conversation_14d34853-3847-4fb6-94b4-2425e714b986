#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== EMERGENCY FIX FOR RSPi APPLICATION ===${NC}"

# Step 1: Kill all existing processes
echo -e "${YELLOW}Killing all existing processes...${NC}"
pkill -f "flask run" || true
pkill -f "ng serve" || true
sleep 2

# Step 2: Fix Docker container
echo -e "${YELLOW}Fixing Docker MySQL container...${NC}"
if docker ps | grep -q "local-mysql"; then
    echo -e "${YELLOW}Stopping existing MySQL container...${NC}"
    docker stop local-mysql
    docker rm local-mysql
fi

echo -e "${YELLOW}Creating fresh MySQL container...${NC}"
docker run --name local-mysql \
    -e MYSQL_ROOT_PASSWORD=password \
    -e MYSQL_DATABASE=mydb \
    -p 3306:3306 \
    -d mysql:8.0

echo -e "${YELLOW}Waiting for MySQL to initialize (15 seconds)...${NC}"
sleep 15

# Step 3: Fix backend
echo -e "${YELLOW}Setting up backend...${NC}"
cd backend || exit 1

# Fix environment file
echo -e "${YELLOW}Creating proper backend .env file...${NC}"
cat > .env << EOL
# Local development environment settings
CORS_ORIGINS=http://localhost:4200
LOGGING_CONFIG_PATH=logging.yaml

# Local database settings
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=mydb
EOL

# Activate virtual environment
echo -e "${YELLOW}Activating virtual environment...${NC}"
source venv/bin/activate

# Install dependencies
echo -e "${YELLOW}Installing critical dependencies...${NC}"
pip install flask pymysql python-dotenv

# Start Flask with explicit app path
echo -e "${GREEN}Starting Flask server on port 5001...${NC}"
export FLASK_APP=app/app.py
export FLASK_ENV=development
export FLASK_DEBUG=1

# Start in background with full path
python -m flask run --host=0.0.0.0 --port=5001 > ../backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../backend.pid

# Return to root directory
cd ..

# Wait for backend to be ready
echo -e "${YELLOW}Waiting for backend to start...${NC}"
sleep 5

# Step 4: Initialize database if needed
echo -e "${YELLOW}Initializing database...${NC}"
if [ -f "backend/init_local_db.sql" ]; then
    echo -e "${YELLOW}Found database initialization script. Attempting to run it...${NC}"
    # We can't use mysql client directly since it's not installed
    # Use docker exec instead
    docker exec -i local-mysql mysql -uroot -ppassword mydb < backend/init_local_db.sql
    echo -e "${GREEN}Database initialization attempted.${NC}"
fi

# Step 5: Fix frontend
echo -e "${YELLOW}Setting up frontend...${NC}"
cd frontend || exit 1

# Fix environment file
echo -e "${YELLOW}Creating proper frontend .env file...${NC}"
echo "NG_APP_BASE_URL=http://localhost:5001/Test" > .env

# Start Angular with explicit options
echo -e "${GREEN}Starting Angular server on port 4200...${NC}"
npx ng serve --host 0.0.0.0 --port 4200 > ../frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > ../frontend.pid

# Return to root directory
cd ..

# Wait for frontend to be ready
echo -e "${YELLOW}Waiting for frontend to start (this may take a minute)...${NC}"
sleep 30

# Step 6: Verify services
echo -e "${BLUE}=== Verifying Services ===${NC}"

# Check backend
echo -e "${YELLOW}Checking backend status...${NC}"
if curl -s http://localhost:5001 > /dev/null; then
    echo -e "${GREEN}✓ Backend is responding${NC}"
else
    echo -e "${RED}✗ Backend is not responding${NC}"
    echo -e "${YELLOW}Last 10 lines of backend.log:${NC}"
    tail -n 10 backend.log
fi

# Check frontend
echo -e "${YELLOW}Checking frontend status...${NC}"
if curl -s http://localhost:4200 > /dev/null; then
    echo -e "${GREEN}✓ Frontend is responding${NC}"
else
    echo -e "${RED}✗ Frontend is not responding${NC}"
    echo -e "${YELLOW}Last 10 lines of frontend.log:${NC}"
    tail -n 10 frontend.log
fi

# Final instructions
echo -e "\n${BLUE}=== INSTRUCTIONS ===${NC}"
echo -e "${GREEN}1. Open your browser and go to: http://localhost:4200${NC}"
echo -e "${GREEN}2. If the page doesn't load, wait another minute and try again${NC}"
echo -e "${GREEN}3. If still not working, check the logs:${NC}"
echo -e "   - Backend log: ${YELLOW}cat backend.log${NC}"
echo -e "   - Frontend log: ${YELLOW}cat frontend.log${NC}"
echo -e "${GREEN}4. To restart the application, run this script again${NC}"

echo -e "\n${BLUE}=== EMERGENCY FIX COMPLETED ===${NC}"
