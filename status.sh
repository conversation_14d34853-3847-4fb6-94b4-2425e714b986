#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== RSPi Application Status ===${NC}"

# Check MySQL container
echo -e "${YELLOW}MySQL Container:${NC}"
if docker ps | grep -q "local-mysql"; then
    echo -e "  ${GREEN}✓ Running${NC}"
    CONTAINER_ID=$(docker ps | grep "local-mysql" | awk '{print $1}')
    echo -e "  ${YELLOW}Container ID: $CONTAINER_ID${NC}"
else
    echo -e "  ${RED}✗ Not running${NC}"
fi

# Check Backend
echo -e "\n${YELLOW}Backend (Flask):${NC}"
if pgrep -f "flask run" > /dev/null; then
    BACKEND_PID=$(pgrep -f "flask run")
    echo -e "  ${GREEN}✓ Process running (PID: $BACKEND_PID)${NC}"
    
    # Test connection
    if curl -s http://localhost:5001 > /dev/null; then
        echo -e "  ${GREEN}✓ Responding on http://localhost:5001${NC}"
    else
        echo -e "  ${RED}✗ Not responding on port 5001${NC}"
    fi
else
    echo -e "  ${RED}✗ Process not running${NC}"
fi

# Check Frontend
echo -e "\n${YELLOW}Frontend (Angular):${NC}"
if pgrep -f "ng serve" > /dev/null; then
    FRONTEND_PID=$(pgrep -f "ng serve")
    echo -e "  ${GREEN}✓ Process running (PID: $FRONTEND_PID)${NC}"
    
    # Test connection
    if curl -s http://localhost:4200 > /dev/null; then
        echo -e "  ${GREEN}✓ Responding on http://localhost:4200${NC}"
    else
        echo -e "  ${RED}✗ Not responding on port 4200${NC}"
    fi
else
    echo -e "  ${RED}✗ Process not running${NC}"
fi

# Check ports
echo -e "\n${YELLOW}Port Status:${NC}"
if command -v lsof &> /dev/null; then
    if lsof -i :5001 | grep -q LISTEN; then
        echo -e "  ${GREEN}✓ Port 5001 (Backend) is in use${NC}"
    else
        echo -e "  ${RED}✗ Port 5001 (Backend) is not in use${NC}"
    fi
    
    if lsof -i :4200 | grep -q LISTEN; then
        echo -e "  ${GREEN}✓ Port 4200 (Frontend) is in use${NC}"
    else
        echo -e "  ${RED}✗ Port 4200 (Frontend) is not in use${NC}"
    fi
    
    if lsof -i :3306 | grep -q LISTEN; then
        echo -e "  ${GREEN}✓ Port 3306 (MySQL) is in use${NC}"
    else
        echo -e "  ${RED}✗ Port 3306 (MySQL) is not in use${NC}"
    fi
else
    echo -e "  ${YELLOW}Cannot check ports (lsof not available)${NC}"
fi

# Check log files
echo -e "\n${YELLOW}Log Files:${NC}"
if [ -f "backend.log" ]; then
    BACKEND_LOG_SIZE=$(wc -l < backend.log)
    echo -e "  ${GREEN}✓ backend.log exists ($BACKEND_LOG_SIZE lines)${NC}"
else
    echo -e "  ${RED}✗ backend.log not found${NC}"
fi

if [ -f "frontend.log" ]; then
    FRONTEND_LOG_SIZE=$(wc -l < frontend.log)
    echo -e "  ${GREEN}✓ frontend.log exists ($FRONTEND_LOG_SIZE lines)${NC}"
else
    echo -e "  ${RED}✗ frontend.log not found${NC}"
fi

# Quick commands
echo -e "\n${BLUE}=== Quick Commands ===${NC}"
echo -e "${YELLOW}Start application:${NC} ./quick_start.sh"
echo -e "${YELLOW}View backend log:${NC} tail -f backend.log"
echo -e "${YELLOW}View frontend log:${NC} tail -f frontend.log"
echo -e "${YELLOW}Test backend:${NC} curl http://localhost:5001"
echo -e "${YELLOW}Test frontend:${NC} curl http://localhost:4200"
echo -e "${YELLOW}Open in browser:${NC} open http://localhost:4200"

echo -e "\n${BLUE}=== Status Check Complete ===${NC}"
