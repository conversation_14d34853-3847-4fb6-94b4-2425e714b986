# RSPTracker

RSPTracker is a web application designed to provide insights into Retail Service Provider (RSP) operations. It focuses on intelligence gathering, monitoring, and analysis across APIs, portals, and usage patterns. Additionally, it serves as a proof of concept for CRM-related functions, such as project management and data visualization.

## Features

- **RSP Intelligence**: Monitor and analyze API transactions, portal usage, and overall digital activity.
- **Data Visualization**: Interactive charts for digital usage history using AG Charts.
- **Dynamic Filtering**: Advanced filtering and sorting capabilities with AG Grid.
- **Project Management**: Create, update, and manage project records as part of CRM functionality.
- **Responsive Design**: Optimized for various screen sizes using Angular Material components.

## Prerequisites

- [Node.js](https://nodejs.org/) (version 16 or higher)
- [Angular CLI](https://angular.io/cli) (version 17.0.0)
- A running backend server (default URL: `http://localhost:5000`)
- Make
- Docker
- AWS Access
- KubeCTL

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd RSPi/frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Update environment variables:
   - Modify the backend URL in the `environment.ts` file if necessary.

## Development Server

Run the development server:
```bash
ng serve
```
Navigate to `http://localhost:4200/`. The application will automatically reload if you make changes to the source files.

## Usage

### RSP Intelligence
- Analyze API and portal usage trends with interactive charts.
- Gain insights into transaction patterns and digital activity.

### Project List
- View and filter project records using the AG Grid table.
- Sort and search by various fields like `Name`, `Org`, and `Category`.

### Project Form
- Create or update project records.
- Validate form inputs before submission.

## Build

To build the project for production:
```bash
ng build
```
The build artifacts will be stored in the `dist/` directory.

## Testing

### Unit Tests
Run unit tests using Karma:
```bash
ng test
```

### End-to-End Tests
Run end-to-end tests:
```bash
ng e2e
```
(Note: Ensure you have a testing package installed for e2e tests.)

## Deployment

### Backend Prepare
1. Change the docker tag version in the backend / Makefile
2. Build and publish image

```
cd backend 

make container-image
make publish-container-image
```

### Frontend Prepare
1. Change the docker tag version in the frontend / Makefile
2. Build and publish image

```
cd frontend 

make container-image
make publish-container-image
```

### Deploy Kubernetes
1. Change the image tag version in deployment / overlays / dev / kustomization.yaml (change dev to env that is being deployed)
2. Connect to Kubernetes cluster

```
aws sso login
aws eks update-kubeconfig --name candc1-nbn-central-eks
kubectl config set-context --current --namespace rsp-sys-ops
```

3. Create the backend secret if required (generally first time deployment or when rotating credentials)

```
kubectl create secret generic insights-backend-env --from-literal=DB_USER=placeholder --from-literal=DB_PASSWORD=placeholder
```

4. Deploy (change dev to env that is being deployed)

```
cd deployment
kubectl apply -k overlays/dev 
```

5. Test (change dev to env that is being deployed)

```
http://rspi.dev.cupc.inttest.nbn-aws.local
```

## Contribution Guidelines

1. Fork the repository.
2. Create a new branch for your feature or bug fix:
   ```bash
   git checkout -b feature-name
   ```
3. Commit your changes:
   ```bash
   git commit -m "Description of changes"
   ```
4. Push to your branch:
   ```bash
   git push origin feature-name
   ```
5. Open a pull request.

## Further Help

Run backend 
C:\Projects\RSPi\backend> .\venv\Scripts\Activate.ps1

(venv) PS C:\Projects\RSPi\backend> .\run.ps1
 * Serving Flask app 'app.py'
 * Debug mode: on

Run Frontend
npm build
npm start


For more information on Angular CLI, visit the [Angular CLI Overview and Command Reference](https://angular.io/cli).


