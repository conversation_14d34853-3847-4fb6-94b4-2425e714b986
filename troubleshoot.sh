#!/bin/bash

# RSPi Project Troubleshooting Script
# This script helps diagnose and fix common issues with the RSPi project

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to display error and exit
error_exit() {
    echo -e "${RED}ERROR: $1${NC}" >&2
    exit 1
}

# Function to check MySQL connection
check_mysql_connection() {
    echo -e "${BLUE}Checking MySQL connection...${NC}"
    
    if ! command_exists mysql; then
        echo -e "${YELLOW}MySQL client not found. Cannot check connection.${NC}"
        return 1
    fi
    
    if mysql -u root -ppassword -h 127.0.0.1 -e "SELECT 1" &>/dev/null; then
        echo -e "${GREEN}MySQL connection successful.${NC}"
        
        # Check if database exists
        if mysql -u root -ppassword -h 127.0.0.1 -e "USE mydb" &>/dev/null; then
            echo -e "${GREEN}Database 'mydb' exists.${NC}"
            
            # Count tables
            TABLE_COUNT=$(mysql -u root -ppassword -h 127.0.0.1 -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'mydb'" | grep -v "COUNT" | tr -d ' ')
            echo -e "${GREEN}Database contains $TABLE_COUNT tables.${NC}"
            
            return 0
        else
            echo -e "${RED}Database 'mydb' does not exist.${NC}"
            return 1
        fi
    else
        echo -e "${RED}MySQL connection failed.${NC}"
        
        # Check if Docker container is running
        if command_exists docker; then
            if docker ps | grep -q "local-mysql"; then
                echo -e "${YELLOW}MySQL Docker container is running but connection failed.${NC}"
                echo -e "${YELLOW}Container logs:${NC}"
                docker logs --tail 20 local-mysql
            else
                echo -e "${RED}MySQL Docker container is not running.${NC}"
                echo -e "${YELLOW}Try starting it with: docker start local-mysql${NC}"
            fi
        else
            echo -e "${YELLOW}Docker not found. Cannot check container status.${NC}"
        fi
        
        return 1
    fi
}

# Function to check backend environment
check_backend_environment() {
    echo -e "${BLUE}Checking backend environment...${NC}"
    
    if [ ! -d "backend" ]; then
        echo -e "${RED}Backend directory not found.${NC}"
        return 1
    fi
    
    if [ ! -f "backend/.env" ]; then
        echo -e "${RED}Backend .env file not found.${NC}"
        echo -e "${YELLOW}Try running setup_local_db.sh to create it.${NC}"
        return 1
    else
        echo -e "${GREEN}Backend .env file exists.${NC}"
        echo -e "${YELLOW}Contents:${NC}"
        cat backend/.env
    fi
    
    if [ ! -d "backend/venv" ]; then
        echo -e "${RED}Backend virtual environment not found.${NC}"
        echo -e "${YELLOW}Try running start.sh to create it.${NC}"
        return 1
    else
        echo -e "${GREEN}Backend virtual environment exists.${NC}"
    fi
    
    return 0
}

# Function to check frontend environment
check_frontend_environment() {
    echo -e "${BLUE}Checking frontend environment...${NC}"
    
    if [ ! -d "frontend" ]; then
        echo -e "${RED}Frontend directory not found.${NC}"
        return 1
    fi
    
    if [ ! -f "frontend/.env" ]; then
        echo -e "${RED}Frontend .env file not found.${NC}"
        echo -e "${YELLOW}Try running start.sh to create it.${NC}"
        return 1
    else
        echo -e "${GREEN}Frontend .env file exists.${NC}"
        echo -e "${YELLOW}Contents:${NC}"
        cat frontend/.env
    fi
    
    if [ ! -d "frontend/node_modules" ]; then
        echo -e "${RED}Frontend node_modules directory not found.${NC}"
        echo -e "${YELLOW}Try running start.sh to install dependencies.${NC}"
        return 1
    else
        echo -e "${GREEN}Frontend node_modules directory exists.${NC}"
    fi
    
    return 0
}

# Function to check running processes
check_running_processes() {
    echo -e "${BLUE}Checking running processes...${NC}"
    
    # Check for backend process
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if ps -p $BACKEND_PID > /dev/null; then
            echo -e "${GREEN}Backend process is running (PID: $BACKEND_PID).${NC}"
        else
            echo -e "${RED}Backend process is not running (PID file exists but process is dead).${NC}"
            echo -e "${YELLOW}Try removing backend.pid and running start.sh again.${NC}"
        fi
    else
        echo -e "${RED}Backend process is not running (no PID file).${NC}"
    fi
    
    # Check for frontend process
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if ps -p $FRONTEND_PID > /dev/null; then
            echo -e "${GREEN}Frontend process is running (PID: $FRONTEND_PID).${NC}"
        else
            echo -e "${RED}Frontend process is not running (PID file exists but process is dead).${NC}"
            echo -e "${YELLOW}Try removing frontend.pid and running start.sh again.${NC}"
        fi
    else
        echo -e "${RED}Frontend process is not running (no PID file).${NC}"
    fi
    
    return 0
}

# Function to fix common issues
fix_common_issues() {
    echo -e "${BLUE}Attempting to fix common issues...${NC}"
    
    # Remove PID files if processes are not running
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if ! ps -p $BACKEND_PID > /dev/null; then
            echo -e "${YELLOW}Removing stale backend.pid file...${NC}"
            rm backend.pid
        fi
    fi
    
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if ! ps -p $FRONTEND_PID > /dev/null; then
            echo -e "${YELLOW}Removing stale frontend.pid file...${NC}"
            rm frontend.pid
        fi
    fi
    
    # Check MySQL container
    if command_exists docker; then
        if ! docker ps | grep -q "local-mysql"; then
            if docker ps -a | grep -q "local-mysql"; then
                echo -e "${YELLOW}Starting stopped MySQL container...${NC}"
                docker start local-mysql
            fi
        fi
    fi
    
    echo -e "${GREEN}Fixed common issues.${NC}"
    echo -e "${YELLOW}You can now try running start.sh again.${NC}"
    
    return 0
}

# Main execution
echo -e "${BLUE}Running RSPi Project Troubleshooting...${NC}"

# Run checks
check_mysql_connection
check_backend_environment
check_frontend_environment
check_running_processes

# Ask if user wants to fix common issues
echo -e "${YELLOW}Would you like to attempt to fix common issues? (y/n)${NC}"
read -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    fix_common_issues
fi

echo -e "${BLUE}Troubleshooting complete.${NC}"
echo -e "${YELLOW}If you're still having issues, try the following:${NC}"
echo -e "${YELLOW}1. Run SKIP_DB_SETUP=true ./start.sh to skip database setup${NC}"
echo -e "${YELLOW}2. Check the logs in backend.log and frontend.log${NC}"
echo -e "${YELLOW}3. Manually start the MySQL container with: docker start local-mysql${NC}"
echo -e "${YELLOW}4. Manually initialize the database with: mysql -u root -ppassword -h 127.0.0.1 < backend/init_local_db.sql${NC}"
