# General
- The project is a RSP (Retail Service Provider) Tracker application with Angular frontend and Flask backend, using MySQL database.
- The project has a Flask backend (started with 'flask run') and a frontend that uses npm (started with 'npm start').
- The project should use a local database during development instead of connecting to the remote database.
- The project has both frontend and backend components, and I should understand the project structure and standards before implementing new features.

# Local Development Setup
- **Required Software**:
  - Node.js and npm for frontend
  - Python 3 for backend
  - MySQL client for database connection
  - Docker for containerized MySQL database (required)
- **Installation on macOS**:
  - Install MySQL client: `brew install mysql-client`
  - Add MySQL client to PATH: `echo 'export PATH="/usr/local/opt/mysql-client/bin:$PATH"' >> ~/.zshrc`
  - Install Docker Desktop: https://docs.docker.com/desktop/install/mac-install/
- **Starting the Application**:
  - Use `./emergency_fix.sh` for initial setup (creates fresh database and environment)
  - Use `./start_clean.sh` for normal startup (assumes database is already configured)
  - Backend runs on port 5001 (to avoid macOS AirPlay conflicts on port 5000)
  - Frontend runs on port 4200
- **Database Setup**:
  - MySQL database runs in a Docker container named `local-mysql`
  - Container is automatically created and started by `setup_local_db.sh`
  - Default database name: `mydb`
  - Default credentials: username `root`, password `password`
  - Database is exposed on localhost port `3306`
  - Data is ephemeral (not persisted between container recreations)

# Troubleshooting
- **Docker Database Connection Issues**:
  - Check if Docker is running: `docker info`
  - Check if MySQL container is running: `docker ps | grep local-mysql`
  - Start container if stopped: `docker start local-mysql`
  - Check container logs: `docker logs local-mysql`
  - Verify port mapping: `docker port local-mysql`
  - Test database connection: `mysql -u root -ppassword -h 127.0.0.1`
  - Recreate container if needed: `docker rm -f local-mysql && ./setup_local_db.sh`
  - Use the diagnostic script: `./check_db_connection.sh`
- **Frontend Connection Issues**:
  - Verify the frontend is using the correct API URL in `.env` file
  - Default URL should be: `NG_APP_BASE_URL=http://localhost:5000/Test`
  - Check browser console for CORS errors
  - Ensure backend is running and accessible at http://localhost:5000
- **Application Startup Issues**:
  - Run backend and frontend separately for better error visibility:
    ```
    # Terminal 1 - Backend
    cd backend
    source venv/bin/activate
    export FLASK_APP=app/app.py
    export FLASK_ENV=development
    flask run

    # Terminal 2 - Frontend
    cd frontend
    npm start
    ```
  - Check for error messages in each terminal

# Backend Architecture
- Flask-based RESTful API with consistent endpoint patterns: GET /{entity}/{id} for single records, POST /{entity} for creation, PATCH /{entity}/{id} for updates, and POST /{entity}List for filtered lists.
- Organized with business service modules (e.g., AccessSeekerBusSvc.py) for each entity, with standard CRUD functions (Get, Add, Update) and database operations handled through DBHandler.py.
- BACKEND RESPONSE FORMAT: JSON responses with a "data" wrapper for successful operations, and error responses with code and error message for failures, using appropriate HTTP status codes.
- BACKEND DATABASE: MySQL database accessed via PyMySQL with connection pooling, using DictCursor for JSON-friendly results, with configuration from environment variables.
- BACKEND GRID SUPPORT: AGGridServices.py provides SQL generation for filtering, sorting, and pagination to support AG Grid on the frontend, with functions like generateWhereSQL(), generateOrderBySQL(), and generateLimitOffsetSQL().
- BACKEND BUSINESS SERVICES: Each entity has its own business service module (e.g., AccessSeekerBusSvc.py) with standard functions: Get{Entity}s (list), Get{Entity} (single), Add{Entity} (create), and Update{Entity} (update).
- BACKEND ERROR HANDLING: Consistent error handling with try/except blocks, logging of exceptions, and appropriate HTTP status codes (400 for client errors, 500 for server errors).
- BACKEND API PATTERN: API endpoints follow a consistent naming pattern with /Test prefix (e.g., /Test/AccessSeeker, /Test/AccessSeekerList) and standard HTTP methods (GET, POST, PATCH) for CRUD operations.
- BACKEND CONFIGURATION: Configuration handled via pydantic_settings with environment variables for database connection, CORS settings, and logging configuration.
- BACKEND ENTITY PATTERN: Each entity has a consistent database schema with standard fields (id, created, created_by, modified, modified_by) plus entity-specific fields.

# Frontend Architecture
- Angular application with component-based architecture, using Angular Material for UI components and AG Grid for data tables.
- FRONTEND STRUCTURE: Organized by feature with components, services, and models. Each entity has its own set of components (list, form, main container) and a dedicated service.
- FRONTEND MODELS: All data models are defined in models.ts with TypeScript interfaces (e.g., IAccessSeeker, INote) including both full models and skeleton models for passing minimal data between components.
- FRONTEND SKELETON MODELS: Each entity has a skeleton interface (e.g., IAccessSeekerSkeleton) with minimal properties for passing between components, reducing data transfer overhead.
- FRONTEND SERVICES: Each entity has a dedicated service (e.g., AccessSeekerService) with standard methods: getRecords (list), getRecord (single), createRecord, and updateRecord, all returning Observables.
- FRONTEND SERVICE API CALLS: Services use HttpClient for API calls with consistent URL construction (environment.base_url + endpoint), proper HTTP methods, and return types cast as Observable<any>.
- FRONTEND COMPONENT PATTERN: Each entity follows a three-component pattern: (1) Main container component managing state, (2) List component with AG Grid, and (3) Form component for editing, with ViewChild relationships.
- FRONTEND MAIN COMPONENT PATTERN: Main container components (e.g., DigitalSvcComponent) manage state, handle tab selection, and coordinate between list and form components using ViewChild and event binding.
- FRONTEND ROUTING: Routes defined in app.routes.ts with each entity having its own route (e.g., '/accessseeker', '/note'), and components loaded via RouterOutlet in the main app component.
- FRONTEND NAVIGATION: Side navigation bar (side-navbar.component) with Material Design icons and mat-list-items for each entity, using routerLink and routerLinkActive for navigation and highlighting.
- FRONTEND GRID IMPLEMENTATION: AG Grid used for all list components with standard configuration for selection, filtering, sorting, and pagination, connected to backend via service calls.
- FRONTEND GRID CONFIGURATION: AG Grid components use consistent configuration with rowSelection="single", pagination=true, and columnDefs with field, headerName, filter, and sortable properties.
- FRONTEND FORM IMPLEMENTATION: Angular Reactive Forms used in all form components with Material form controls (mat-form-field, mat-input, mat-select), validation, and two-way binding.
- FRONTEND DIALOG IMPLEMENTATION: MatDialog used for confirmation dialogs (YesNoDialogComponent) and form dialogs (e.g., ContactFormDialogComponent), with data passing via injection token.
- FRONTEND LIST COMPONENT PATTERN: List components (e.g., DigitalSvcListComponent) use AG Grid with column definitions, row selection, and emit selection events to parent components.
- FRONTEND FORM COMPONENT PATTERN: Form components (e.g., DigitalSvcFormComponent) use Reactive Forms with FormGroup, FormControl, and validation, with methods to load, save, and clear form data.
- FRONTEND ENVIRONMENT CONFIGURATION: Environment settings in environment.ts with base_url for API endpoints, using Angular environment variables for different deployment environments.
- FRONTEND COMPONENT IMPORTS: Components import necessary Angular Material modules (MatCardModule, MatIconModule, etc.) and other components they depend on, with proper declarations in the @Component decorator.
- FRONTEND SELECTION HANDLING: List components emit selection events using AG Grid's SelectionChangedEvent, which parent components handle to update form components and manage application state.
- FRONTEND TAB MANAGEMENT: Main container components use MatTabsModule to organize content into tabs, with selectedTabIndex property to programmatically control active tab.
- FRONTEND NOTIFICATION PATTERN: MatSnackBar used for temporary notifications/messages to users, typically after operations like save, update, or error conditions.
- FRONTEND COMPONENT LIFECYCLE: Components implement OnInit for initialization logic, with ViewChild references initialized after view initialization, and proper cleanup in OnDestroy when needed.
- FRONTEND FORM VALIDATION: Form components implement validation using Angular's built-in validators with visual feedback through mat-error elements and disabled submit buttons for invalid forms.
- FRONTEND LAYOUT PATTERN: Components use mat-card for container elements, with mat-card-header, mat-card-content, and mat-card-actions for consistent layout and styling.
- FRONTEND STYLING: Use @use instead of @import in SCSS files to avoid deprecation warnings. Chart styles are centralized in src/app/styles/chart-styles.scss.
# Frontend Charting with AG Charts

## SIMPLIFIED APPROACH (ALWAYS USE THIS APPROACH FIRST)
- ALWAYS use the simplest possible configuration for AG Charts
- ALWAYS use inline styles for chart containers:
  ```html
  <div style="width: 100%; height: 500px; border: 1px solid #ccc; margin: 20px 0;">
    <ag-charts [options]="options"></ag-charts>
  </div>
  ```
- ALWAYS use minimal chart options:
  ```typescript
  this.options = {
    data: chartData,
    series: [
      {
        type: 'line', // or 'bar', 'pie', etc.
        xKey: 'xField',
        yKey: 'yField',
        yName: 'Display Name',
      }
    ],
    axes: [
      {
        type: 'category',
        position: 'bottom',
      },
      {
        type: 'number',
        position: 'left',
      },
    ],
  };
  ```
- AVOID using complex options like titles, subtitles, container padding, etc. until basic charts work
- AVOID using mat-tab-group to contain charts until basic charts work
- ALWAYS check for null or empty data before generating charts:
  ```typescript
  if (!this.chartData || this.chartData.length === 0) {
    console.warn('No data available for chart');
    return;
  }
  ```

## AG Charts Module and Component Setup
- ALWAYS use `AgChartsModule` from 'ag-charts-angular' in component imports, NOT `AgChartsAngularModule`
- ALWAYS use `<ag-charts [options]="options"></ag-charts>` in templates, NOT `<ag-charts-angular>`
- ALWAYS add AgChartsModule to the imports array in the @Component decorator

## AG Charts Styling
- For complex layouts, create a dedicated chart container with proper dimensions:
  ```scss
  .chart-container {
    height: 500px;
    width: 100%;
    margin: 20px 0;
    display: block;
    position: relative;
  }

  ag-charts {
    display: block !important;
    width: 100% !important;
    height: 500px !important;
    min-height: 500px !important;
  }
  ```
- For simple layouts, use inline styles (see SIMPLIFIED APPROACH above)
- ALWAYS ensure parent containers have proper width and height settings

## AG Charts Data Handling
- ALWAYS initialize chart data as empty arrays in component properties
- ALWAYS handle data loading errors properly
- ALWAYS regenerate charts when data or options change
- ALWAYS use proper typing for chart data and options

## AG Charts Debugging
- Add console logs to track chart initialization and data loading
- Check browser console for AG Charts-specific errors
- Verify chart options structure matches AG Charts documentation
- Inspect DOM elements to ensure chart containers are rendered properly

## Common Chart Types Configuration

### Line Charts
```typescript
{
  type: 'line',
  xKey: 'Period',
  yKey: 'Value',
  yName: 'Display Name',
  stroke: '#0066cc',
  marker: {
    fill: '#0066cc',
    stroke: '#0066cc'
  }
}
```

### Bar Charts
```typescript
{
  type: 'bar',
  xKey: 'Category',
  yKey: 'Value',
  yName: 'Display Name',
  fill: '#0066cc',
  // For stacked bars
  stacked: true
}
```

### Pie Charts
```typescript
{
  type: 'pie',
  angleKey: 'Value',
  labelKey: 'Category',
  calloutLabelKey: 'Category',
  sectorLabelKey: 'Value',
  fills: ['#0066cc', '#99ccff', '#ff0000', '#ffcc00', '#00cc66']
}
```

## Responsive Chart Design
- ALWAYS use `autoSize: true` in chart options
- ALWAYS use percentage-based widths (e.g., `width: '100%'`)
- ALWAYS use media queries to adjust chart height on different screen sizes:
  ```scss
  @media (max-width: 768px) {
    .chart-container {
      height: 400px;
    }

    ag-charts {
      height: 400px !important;
    }
  }
  ```
- ALWAYS test charts on different screen sizes and orientations
- Consider using Angular's BreakpointObserver to adjust chart options based on screen size:
  ```typescript
  constructor(private breakpointObserver: BreakpointObserver) {
    this.breakpointObserver.observe([
      Breakpoints.HandsetPortrait,
      Breakpoints.TabletPortrait
    ]).subscribe(result => {
      if (result.matches) {
        // Adjust chart options for smaller screens
        this.options.axes[0].label.rotation = 90;
      } else {
        // Reset for larger screens
        this.options.axes[0].label.rotation = 45;
      }
      // Regenerate chart with new options
      this.generateChart();
    });
  }
  ```

## Performance Considerations
- AVOID rendering too many data points (consider aggregating data for large datasets)
- AVOID complex chart animations on mobile devices
- CONSIDER using pagination or lazy loading for large datasets
- ALWAYS use proper Angular change detection strategies
- CONSIDER using OnPush change detection for better performance

## Integration with Angular Material
- ALWAYS place charts inside Angular Material components with proper layout:
  ```html
  <mat-card>
    <mat-card-header>
      <mat-card-title>Chart Title</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="chart-container">
        <ag-charts [options]="options"></ag-charts>
      </div>
    </mat-card-content>
  </mat-card>
  ```
- ALWAYS handle chart rendering in tabs properly:
  ```html
  <mat-tab-group (selectedTabChange)="onTabChange($event)">
    <mat-tab label="Chart">
      <div class="chart-container">
        <ag-charts [options]="options"></ag-charts>
      </div>
    </mat-tab>
  </mat-tab-group>
  ```
  ```typescript
  onTabChange(event: MatTabChangeEvent): void {
    // Regenerate chart when tab is selected to ensure proper rendering
    if (event.index === 0) { // Chart tab index
      setTimeout(() => this.generateChart(), 0);
    }
  }
  ```
- ALWAYS set proper CSS for mat-card-content containing charts:
  ```scss
  mat-card-content {
    width: 100%;
    padding: 16px;
    min-height: 550px;
    display: flex;
    flex-direction: column;
  }
  ```

## Common Troubleshooting Tips
- **Chart Not Visible**:
  1. First, try the SIMPLIFIED APPROACH above with inline styles and minimal options
  2. Check browser console for errors
  3. Verify data is loaded correctly (add console.log statements)
  4. Remove the chart from any complex containers (mat-tab-group, mat-card, etc.)
  5. Use a simple div with inline styles as shown in the SIMPLIFIED APPROACH

- **Chart Appears White/Empty**:
  1. Simplify chart options to bare minimum (data, series, axes only)
  2. Remove all styling from containers and use inline styles
  3. Check that data is properly formatted and not empty
  4. Try a different chart type (e.g., change from line to bar)
  5. Verify that the chart container has explicit dimensions

- **Chart Squashed/Distorted**:
  1. Use inline styles with explicit dimensions
  2. Remove the chart from mat-tab-group or other complex containers
  3. Ensure the chart container has sufficient height and width

- **Chart Not Updating**:
  1. Verify change detection is triggered
  2. Ensure chart options are properly updated
  3. Use setTimeout to regenerate chart after data changes

- **Chart Rendering Errors**:
  1. Check browser console for errors
  2. Verify all required properties in chart options
  3. Remove any unsupported properties from chart options

- **Chart Disappears on Tab Change**:
  1. Avoid using mat-tab-group initially
  2. If tabs are required, regenerate chart when tab is selected using setTimeout

- **Chart Data Not Showing**:
  1. Verify data format matches expected format
  2. Check for null/undefined values
  3. Ensure xKey and yKey match the property names in your data

- **Chart Legend Issues**:
  1. Ensure legend position is set correctly
  2. Verify series names are provided using yName property

- **Chart Axis Labels Overlapping**:
  1. Use rotation for axis labels
  2. Adjust padding
  3. Consider abbreviating labels

- **Chart Performance Issues**:
  1. Reduce data points
  2. Simplify chart options
  3. Consider pagination or lazy loading

## Documentation Resources
- [AG Charts Angular Documentation](https://www.ag-grid.com/angular-charts/getting-started/)
- [AG Charts API Reference](https://www.ag-grid.com/angular-charts/api-chart-options/)
- [Angular Material Documentation](https://material.angular.io/components/categories)
- [Angular Change Detection](https://angular.io/guide/change-detection)

## Complete Example: Simplified Line Chart Component

### Component TypeScript (example-chart.component.ts)
```typescript
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AgChartsModule } from 'ag-charts-angular';

// Data interface
interface IChartData {
  Period: string;
  Value1: number;
  Value2: number;
}

@Component({
  selector: 'app-example-chart',
  standalone: true,
  imports: [
    CommonModule,
    AgChartsModule
  ],
  template: `
    <!-- Simplified chart display -->
    <div style="width: 100%; height: 500px; border: 1px solid #ccc; margin: 20px 0;">
      <ag-charts [options]="options"></ag-charts>
    </div>

    <!-- Data display -->
    <div style="width: 100%; margin: 20px 0; overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse;">
        <thead>
          <tr>
            <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Period</th>
            <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Value 1</th>
            <th style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd; background-color: #f2f2f2;">Value 2</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of chartData" style="border-bottom: 1px solid #ddd;">
            <td style="padding: 8px; text-align: left;">{{ item.Period }}</td>
            <td style="padding: 8px; text-align: left;">{{ item.Value1 | number }}</td>
            <td style="padding: 8px; text-align: left;">{{ item.Value2 | number }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  `
})
export class ExampleChartComponent implements OnInit {
  // Chart data
  chartData: IChartData[] = [];

  // Chart options
  options: any;

  constructor() { }

  ngOnInit(): void {
    // Load data
    this.loadData();
  }

  // Load chart data
  loadData(): void {
    // In a real app, this would be a service call
    this.chartData = [
      { Period: 'Jan', Value1: 100, Value2: 50 },
      { Period: 'Feb', Value1: 120, Value2: 55 },
      { Period: 'Mar', Value1: 140, Value2: 60 },
      { Period: 'Apr', Value1: 160, Value2: 65 },
      { Period: 'May', Value1: 180, Value2: 70 },
      { Period: 'Jun', Value1: 200, Value2: 75 }
    ];

    // Generate chart
    this.generateChart();
  }

  // Generate the chart with minimal configuration
  generateChart(): void {
    console.log('Generating chart with data:', this.chartData);

    if (!this.chartData || this.chartData.length === 0) {
      console.warn('No data available for chart');
      return;
    }

    // Set chart options with minimal configuration
    this.options = {
      data: this.chartData,
      series: [
        {
          type: 'line',
          xKey: 'Period',
          yKey: 'Value1',
          yName: 'Series 1',
        },
        {
          type: 'line',
          xKey: 'Period',
          yKey: 'Value2',
          yName: 'Series 2',
        }
      ],
      legend: {
        position: 'bottom',
      },
      axes: [
        {
          type: 'category',
          position: 'bottom',
        },
        {
          type: 'number',
          position: 'left',
        },
      ],
    };

    console.log('Chart options set:', this.options);
  }
}
```