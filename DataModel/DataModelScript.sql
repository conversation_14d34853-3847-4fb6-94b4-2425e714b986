--  Drop table
--  DROP TABLE RSPTracker.AccessSeeker;

CREATE TABLE AccessSeeker (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified <PERSON><PERSON><PERSON><PERSON><PERSON> NULL,
	modified_by varchar(45) NULL,
	AccessSeekerId varchar(45) NULL,
	Status varchar(25) NULL,
	Name varchar(100) NULL,
	<PERSON><PERSON> varchar(100) NULL,
	Type varchar(50) NULL,
	ParentAccessSeekerRecordId INT NULL,
	Category1 varchar(100) NULL,
	Category2 varchar(100) NULL,
	Website varchar(100) NULL,
	About varchar(500) NULL,
	Comments varchar(500) NULL,
	SDM varchar(80) NULL,
	AGM varchar(80) NULL,
	CDM varchar(80) NULL,
	CONSTRAINT AccessSeeker_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE Note (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	AccessSeekerRecordId INT NULL,
	Type varchar(50) NULL,
	Title varchar(100) NULL,
	Sequence INT NULL,
	Description varchar(2000) NULL,
	CONSTRAINT Note_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE Contact (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	AccessSeekerRecordId INT NULL,
	FirstName varchar(50) NULL,
	LastName varchar(50) NULL,
	Role varchar(50) NULL,
	Phone varchar(50) NULL,
	Email varchar(200) NULL,
	Notes varchar(500) NULL,
	CONSTRAINT Contact_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE Task (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	AccessSeekerRecordId INT NULL,
	Title varchar(100) NULL,
	Type varchar(50) NULL,
	Status varchar(25) NULL,
	AssignedTo varchar(100) NULL,
	DueDate DATE NULL,
	CompletedDate DATE NULL,
	Notes varchar(500) NULL,	
	CONSTRAINT Task_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE Project (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	AccessSeekerRecordId INT NULL,
	Name varchar(100) NULL,
	Org varchar(50) NULL,
	Category1 varchar(100),
	Category2 varchar(100),
	Status varchar(25) NULL,
	PlannedStartDate DATE NULL,
	ActualStartDate DATE NULL,
	PlannedEndDate DATE NULL,
	ActualEndDate DATE NULL,
	Description varchar(500) NULL,
	Notes varchar(500) NULL,
	CONSTRAINT Project_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE DigitalService (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	ServiceCode varchar(50) NULL,
	ServiceName varchar(50) NULL,
	Status varchar(25) NULL,
	APIName varchar(50) NULL,
	Purpose varchar(200) NULL,
	Description varchar(500) NULL,
	UsedForConnect varchar(1) NULL,
	UsedForAssure varchar(1) NULL,
	UsedForBilling varchar(1) NULL,
	UsedForOther varchar(1) NULL,
	Notes varchar(500) NULL,
	CONSTRAINT DigitalService_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE DigitalServiceVersion (
	id INT auto_increment NOT NULL,
	created DATETIME NULL,
	created_by varchar(45) NULL,
	modified DATETIME NULL,
	modified_by varchar(45) NULL,
	DigitalServiceRecordId INT NULL,
	Version varchar(2) NULL,
	Status varchar(25) NULL,
	ReleaseDate DATE NULL,
	Description varchar(500) NULL,
	CONSTRAINT DigitalServiceVersion_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;