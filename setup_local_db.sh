#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to display error and exit
error_exit() {
    echo -e "${RED}ERROR: $1${NC}" >&2
    exit 1
}

# Check for MySQL
if ! command_exists mysql; then
    echo -e "${YELLOW}MySQL client not found. Please install MySQL before running this script.${NC}"
    echo -e "${YELLOW}On macOS, you can use: brew install mysql${NC}"
    echo -e "${YELLOW}On Ubuntu, you can use: sudo apt-get install mysql-server${NC}"
    exit 1
fi

# Check for Docker
if ! command_exists docker; then
    echo -e "${YELLOW}Docker not found. Please install Docker before running this script.${NC}"
    echo -e "${YELLOW}Visit https://docs.docker.com/get-docker/ for installation instructions.${NC}"
    exit 1
fi

# Function to start MySQL in Docker if not running locally
start_mysql_docker() {
    echo -e "${BLUE}Checking if MySQL is running locally...${NC}"

    # Try to connect to local MySQL
    if mysql -u root -ppassword -h 127.0.0.1 -e "SELECT 1" &>/dev/null; then
        echo -e "${GREEN}MySQL is already running locally.${NC}"
        return 0
    fi

    echo -e "${YELLOW}Local MySQL not available. Starting MySQL in Docker...${NC}"

    # Check if MySQL container is already running
    if docker ps | grep -q "local-mysql"; then
        echo -e "${GREEN}MySQL Docker container is already running.${NC}"
        return 0
    fi

    # Check if MySQL container exists but is stopped
    if docker ps -a | grep -q "local-mysql"; then
        echo -e "${YELLOW}Starting existing MySQL Docker container...${NC}"
        docker start local-mysql

        # Wait for MySQL to start
        echo -e "${YELLOW}Waiting for MySQL to start...${NC}"
        sleep 10
        return 0
    fi

    # Create and start a new MySQL container
    echo -e "${YELLOW}Creating and starting new MySQL Docker container...${NC}"
    docker run --name local-mysql \
        -e MYSQL_ROOT_PASSWORD=password \
        -e MYSQL_DATABASE=mydb \
        -p 3306:3306 \
        -d mysql:8.0

    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to start MySQL Docker container. Checking for existing container...${NC}"

        # Check if container exists but is in a bad state
        if docker ps -a | grep -q "local-mysql"; then
            echo -e "${YELLOW}Found existing container. Removing it and trying again...${NC}"
            docker rm -f local-mysql

            # Try to create the container again
            docker run --name local-mysql \
                -e MYSQL_ROOT_PASSWORD=password \
                -e MYSQL_DATABASE=mydb \
                -p 3306:3306 \
                -d mysql:8.0

            if [ $? -ne 0 ]; then
                error_exit "Failed to start MySQL Docker container after cleanup attempt."
            fi
        else
            error_exit "Failed to start MySQL Docker container."
        fi
    fi

    # Wait for MySQL to start
    echo -e "${YELLOW}Waiting for MySQL to start...${NC}"
    sleep 20

    # Verify MySQL is actually running
    if ! docker ps | grep -q "local-mysql"; then
        error_exit "MySQL container started but is not running. Check Docker logs with: docker logs local-mysql"
    fi

    # Try to connect to MySQL to verify it's accepting connections
    echo -e "${YELLOW}Verifying MySQL connection...${NC}"
    for i in {1..5}; do
        if mysql -u root -ppassword -h 127.0.0.1 -e "SELECT 1" &>/dev/null; then
            echo -e "${GREEN}MySQL Docker container started successfully and is accepting connections.${NC}"
            break
        fi

        if [ $i -eq 5 ]; then
            echo -e "${YELLOW}Warning: Could not verify MySQL connection, but continuing anyway. You may need to restart if database operations fail.${NC}"
        else
            echo -e "${YELLOW}Waiting for MySQL to accept connections (attempt $i/5)...${NC}"
            sleep 5
        fi
    done
}

# Function to initialize the database
initialize_database() {
    echo -e "${BLUE}Initializing database...${NC}"

    # Check if the initialization script exists
    if [ ! -f "backend/init_local_db.sql" ]; then
        error_exit "Database initialization script not found at backend/init_local_db.sql"
    fi

    # Run the initialization script
    mysql -u root -ppassword -h 127.0.0.1 < backend/init_local_db.sql

    if [ $? -ne 0 ]; then
        echo -e "${YELLOW}Warning: There was an issue initializing the database. This might be because the database already exists.${NC}"
        echo -e "${YELLOW}Checking if database exists...${NC}"

        # Check if the database exists
        if mysql -u root -ppassword -h 127.0.0.1 -e "USE mydb" &>/dev/null; then
            echo -e "${GREEN}Database 'mydb' exists. Continuing...${NC}"
        else
            error_exit "Failed to initialize database and database does not exist."
        fi
    else
        echo -e "${GREEN}Database initialized successfully.${NC}"
    fi

    # Verify tables were created
    echo -e "${YELLOW}Verifying database tables...${NC}"
    TABLE_COUNT=$(mysql -u root -ppassword -h 127.0.0.1 -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'mydb'" | grep -v "COUNT" | tr -d ' ')

    if [ "$TABLE_COUNT" -gt 0 ]; then
        echo -e "${GREEN}Database contains $TABLE_COUNT tables. Verification successful.${NC}"
    else
        echo -e "${YELLOW}Warning: No tables found in the database. There might be an issue with initialization.${NC}"
        echo -e "${YELLOW}Continuing anyway, but you may need to manually initialize the database.${NC}"
    fi
}

# Function to copy the local environment file
setup_env_file() {
    echo -e "${BLUE}Setting up environment file...${NC}"

    # Copy the local environment file to .env
    cp backend/.env.local backend/.env

    if [ $? -ne 0 ]; then
        error_exit "Failed to set up environment file."
    fi

    echo -e "${GREEN}Environment file set up successfully.${NC}"
}

# Main execution
echo -e "${BLUE}Setting up local database for RSPi Project...${NC}"

# Start MySQL
start_mysql_docker

# Initialize the database
initialize_database

# Set up environment file
setup_env_file

echo -e "${GREEN}Local database setup complete!${NC}"
echo -e "${GREEN}You can now run ./start.sh to start the application with the local database.${NC}"
