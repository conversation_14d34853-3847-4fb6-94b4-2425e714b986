#!/bin/bash

# RSPi Project Starter Script (Fixed Version)
# This script starts both the frontend and backend services and displays their combined logs

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to display error and exit
error_exit() {
    echo -e "${RED}ERROR: $1${NC}" >&2
    exit 1
}

# Function to check and setup local database
check_local_database() {
    echo -e "${BLUE}Checking local database setup...${NC}"
    
    # Check if SKIP_DB_SETUP environment variable is set
    if [ "$SKIP_DB_SETUP" = "true" ]; then
        echo -e "${YELLOW}Skipping database setup as requested by SKIP_DB_SETUP environment variable.${NC}"
        return 0
    fi
    
    # Check if setup_local_db.sh exists and is executable
    if [ -f "./setup_local_db.sh" ] && [ -x "./setup_local_db.sh" ]; then
        echo -e "${YELLOW}Running local database setup...${NC}"
        ./setup_local_db.sh
        
        if [ $? -ne 0 ]; then
            echo -e "${RED}Local database setup failed.${NC}"
            echo -e "${YELLOW}Would you like to continue without database setup? (y/n)${NC}"
            read -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                error_exit "Aborting due to database setup failure."
            else
                echo -e "${YELLOW}Continuing without database setup. The application may not work correctly.${NC}"
                echo -e "${YELLOW}To skip database setup in the future, run: SKIP_DB_SETUP=true ./start.sh${NC}"
            fi
        fi
    else
        echo -e "${YELLOW}Local database setup script not found or not executable.${NC}"
        echo -e "${YELLOW}Make sure setup_local_db.sh exists and is executable (chmod +x setup_local_db.sh).${NC}"
        echo -e "${YELLOW}Would you like to continue without database setup? (y/n)${NC}"
        read -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            error_exit "Aborting due to missing database setup script."
        else
            echo -e "${YELLOW}Continuing without database setup. The application may not work correctly.${NC}"
        fi
    fi
}

# Function to start the backend
start_backend() {
    echo -e "${BLUE}Setting up backend...${NC}"
    
    # Check and setup local database
    check_local_database
    
    cd backend || error_exit "Backend directory not found"
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        echo -e "${YELLOW}Creating Python virtual environment...${NC}"
        python3 -m venv venv
        if [ $? -ne 0 ]; then
            error_exit "Failed to create Python virtual environment."
        fi
    fi
    
    # Activate virtual environment
    echo -e "${YELLOW}Activating virtual environment...${NC}"
    source venv/bin/activate
    
    # Check if dependencies are already installed
    if pip list | grep -q "Flask"; then
        echo -e "${GREEN}Flask is already installed. Skipping dependency installation.${NC}"
    else
        echo -e "${YELLOW}Installing backend dependencies...${NC}"
        pip install -r requirements.txt
        if [ $? -ne 0 ]; then
            echo -e "${RED}Failed to install dependencies. Continuing anyway, but the application may not work correctly.${NC}"
        fi
    fi
    
    # Make sure python-dotenv is installed
    pip install python-dotenv
    
    # Set Flask environment variables
    export FLASK_APP=app/app.py
    export FLASK_ENV=development
    export FLASK_DEBUG=1
    
    # Start Flask in the background and save PID
    echo -e "${GREEN}Starting backend server...${NC}"
    flask run > ../backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../backend.pid
    
    # Return to the root directory
    cd ..
    
    echo -e "${GREEN}Backend server started on http://localhost:5000${NC}"
}

# Function to start the frontend
start_frontend() {
    echo -e "${BLUE}Setting up frontend...${NC}"
    cd frontend || error_exit "Frontend directory not found"
    
    # Create or update .env file with local API URL
    echo -e "${YELLOW}Setting up frontend environment...${NC}"
    echo "NG_APP_BASE_URL=http://localhost:5000/Test" > .env
    
    # Start Angular in the background using npm directly
    echo -e "${GREEN}Starting frontend server...${NC}"
    npm start > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../frontend.pid
    
    # Return to the root directory
    cd ..
    
    echo -e "${GREEN}Frontend server started on http://localhost:4200${NC}"
    echo -e "${YELLOW}Frontend is configured to use local API at http://localhost:5000/Test${NC}"
}

# Function to display combined logs
display_logs() {
    echo -e "${BLUE}Displaying combined logs (press Ctrl+C to stop)...${NC}"
    echo -e "${YELLOW}=== Combined Logs (Backend + Frontend) ===${NC}"
    
    # Use tail to follow both log files
    tail -f backend.log frontend.log
}

# Function to clean up on exit
cleanup() {
    echo -e "\n${BLUE}Shutting down servers...${NC}"
    
    # Kill backend process if it exists
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if ps -p $BACKEND_PID > /dev/null; then
            kill $BACKEND_PID
            echo -e "${GREEN}Backend server stopped${NC}"
        fi
        rm backend.pid
    fi
    
    # Kill frontend process if it exists
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if ps -p $FRONTEND_PID > /dev/null; then
            kill $FRONTEND_PID
            echo -e "${GREEN}Frontend server stopped${NC}"
        fi
        rm frontend.pid
    fi
    
    # Remove log files
    rm -f backend.log frontend.log
    
    echo -e "${GREEN}Cleanup complete${NC}"
    exit 0
}

# Set up trap to catch Ctrl+C and other termination signals
trap cleanup SIGINT SIGTERM

# Main execution
echo -e "${BLUE}Starting RSPi Project...${NC}"

# Start backend
start_backend

# Start frontend
start_frontend

# Display combined logs
display_logs
