#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== RSPi Application Structure Check ===${NC}"

# Check backend structure
echo -e "${YELLOW}Checking backend structure...${NC}"

# Check if backend directory exists
if [ -d "backend" ]; then
    echo -e "${GREEN}✓ Backend directory exists${NC}"
    
    # Check key files
    if [ -f "backend/app/app.py" ]; then
        echo -e "${GREEN}✓ Main app file exists: backend/app/app.py${NC}"
    else
        echo -e "${RED}✗ Main app file missing: backend/app/app.py${NC}"
    fi
    
    if [ -f "backend/app/DBHandler.py" ]; then
        echo -e "${GREEN}✓ DB Handler file exists: backend/app/DBHandler.py${NC}"
    else
        echo -e "${RED}✗ DB Handler file missing: backend/app/DBHandler.py${NC}"
    fi
    
    if [ -f "backend/requirements.txt" ]; then
        echo -e "${GREEN}✓ Requirements file exists: backend/requirements.txt${NC}"
        echo -e "${YELLOW}Key dependencies in requirements.txt:${NC}"
        grep -E "flask|pymysql|python-dotenv" backend/requirements.txt || echo "  No key dependencies found"
    else
        echo -e "${RED}✗ Requirements file missing: backend/requirements.txt${NC}"
    fi
    
    if [ -f "backend/init_local_db.sql" ]; then
        echo -e "${GREEN}✓ Database initialization script exists: backend/init_local_db.sql${NC}"
    else
        echo -e "${RED}✗ Database initialization script missing: backend/init_local_db.sql${NC}"
    fi
    
    # Check virtual environment
    if [ -d "backend/venv" ]; then
        echo -e "${GREEN}✓ Virtual environment exists: backend/venv${NC}"
    else
        echo -e "${RED}✗ Virtual environment missing: backend/venv${NC}"
    fi
    
    # Check app routes
    if [ -f "backend/app/app.py" ]; then
        echo -e "${YELLOW}Checking app routes in app.py:${NC}"
        grep -E "@app.route|@blueprint.route" backend/app/app.py | head -n 10
    fi
else
    echo -e "${RED}✗ Backend directory missing${NC}"
fi

# Check frontend structure
echo -e "\n${YELLOW}Checking frontend structure...${NC}"

# Check if frontend directory exists
if [ -d "frontend" ]; then
    echo -e "${GREEN}✓ Frontend directory exists${NC}"
    
    # Check key files
    if [ -f "frontend/package.json" ]; then
        echo -e "${GREEN}✓ Package file exists: frontend/package.json${NC}"
        echo -e "${YELLOW}Angular version:${NC}"
        grep -E "\"@angular/core\"" frontend/package.json || echo "  Angular core not found"
    else
        echo -e "${RED}✗ Package file missing: frontend/package.json${NC}"
    fi
    
    if [ -f "frontend/angular.json" ]; then
        echo -e "${GREEN}✓ Angular config file exists: frontend/angular.json${NC}"
    else
        echo -e "${RED}✗ Angular config file missing: frontend/angular.json${NC}"
    fi
    
    if [ -d "frontend/node_modules" ]; then
        echo -e "${GREEN}✓ Node modules directory exists: frontend/node_modules${NC}"
    else
        echo -e "${RED}✗ Node modules directory missing: frontend/node_modules${NC}"
    fi
    
    if [ -f "frontend/src/main.ts" ]; then
        echo -e "${GREEN}✓ Main entry file exists: frontend/src/main.ts${NC}"
    else
        echo -e "${RED}✗ Main entry file missing: frontend/src/main.ts${NC}"
    fi
    
    # Check environment configuration
    if [ -f "frontend/src/app/environment/environment.ts" ]; then
        echo -e "${GREEN}✓ Environment file exists: frontend/src/app/environment/environment.ts${NC}"
        echo -e "${YELLOW}Environment configuration:${NC}"
        cat frontend/src/app/environment/environment.ts
    else
        echo -e "${RED}✗ Environment file missing: frontend/src/app/environment/environment.ts${NC}"
    fi
else
    echo -e "${RED}✗ Frontend directory missing${NC}"
fi

# Check Docker configuration
echo -e "\n${YELLOW}Checking Docker configuration...${NC}"
if [ -f "setup_local_db.sh" ]; then
    echo -e "${GREEN}✓ Database setup script exists: setup_local_db.sh${NC}"
    echo -e "${YELLOW}Docker container configuration:${NC}"
    grep -E "docker run|--name local-mysql" setup_local_db.sh | head -n 5
else
    echo -e "${RED}✗ Database setup script missing: setup_local_db.sh${NC}"
fi

echo -e "\n${BLUE}=== Application Structure Check Complete ===${NC}"
