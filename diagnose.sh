#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== RSPi Application Diagnostic Tool ===${NC}"

# Check Docker status
echo -e "${YELLOW}Checking Docker status...${NC}"
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker is not installed or not in PATH${NC}"
else
    if ! docker info &> /dev/null; then
        echo -e "${RED}Docker is installed but not running${NC}"
    else
        echo -e "${GREEN}Docker is running${NC}"
        
        # Check MySQL container
        echo -e "${YELLOW}Checking MySQL container...${NC}"
        if docker ps | grep -q "local-mysql"; then
            echo -e "${GREEN}MySQL container is running${NC}"
            
            # Get container details
            echo -e "${YELLOW}MySQL container details:${NC}"
            docker inspect --format='{{.Name}} - {{.Config.Image}} - {{range $p, $conf := .NetworkSettings.Ports}}{{$p}} -> {{(index $conf 0).HostPort}}{{end}}' $(docker ps -q --filter name=local-mysql)
            
            # Test MySQL connection
            echo -e "${YELLOW}Testing MySQL connection...${NC}"
            if command -v mysql &> /dev/null; then
                if mysql -u root -ppassword -h 127.0.0.1 -e "SELECT 1" &> /dev/null; then
                    echo -e "${GREEN}MySQL connection successful${NC}"
                    
                    # Check if database exists
                    if mysql -u root -ppassword -h 127.0.0.1 -e "USE mydb" &> /dev/null; then
                        echo -e "${GREEN}Database 'mydb' exists${NC}"
                        
                        # Count tables
                        TABLE_COUNT=$(mysql -u root -ppassword -h 127.0.0.1 -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'mydb'" | grep -v "COUNT" | tr -d ' ')
                        echo -e "${GREEN}Database contains $TABLE_COUNT tables${NC}"
                    else
                        echo -e "${RED}Database 'mydb' does not exist${NC}"
                    fi
                else
                    echo -e "${RED}MySQL connection failed${NC}"
                fi
            else
                echo -e "${RED}MySQL client not installed${NC}"
            fi
        else
            echo -e "${RED}MySQL container is not running${NC}"
            
            # Check if container exists but is stopped
            if docker ps -a | grep -q "local-mysql"; then
                echo -e "${YELLOW}MySQL container exists but is stopped${NC}"
            else
                echo -e "${RED}MySQL container does not exist${NC}"
            fi
        fi
    fi
fi

# Check backend status
echo -e "\n${YELLOW}Checking backend status...${NC}"
if pgrep -f "flask run" > /dev/null; then
    echo -e "${GREEN}Flask process is running${NC}"
    
    # Check if port 5001 is in use
    if command -v lsof &> /dev/null; then
        if lsof -i :5001 | grep -q LISTEN; then
            echo -e "${GREEN}Backend is listening on port 5001${NC}"
        else
            echo -e "${RED}No process is listening on port 5001${NC}"
        fi
    else
        echo -e "${YELLOW}Cannot check port status (lsof not found)${NC}"
    fi
    
    # Test backend connection
    echo -e "${YELLOW}Testing backend connection...${NC}"
    if curl -s http://localhost:5001 > /dev/null; then
        echo -e "${GREEN}Backend connection successful${NC}"
        
        # Check backend response
        RESPONSE=$(curl -s http://localhost:5001)
        echo -e "${YELLOW}Backend response:${NC} ${RESPONSE:0:100}..."
    else
        echo -e "${RED}Backend connection failed${NC}"
    fi
else
    echo -e "${RED}Flask process is not running${NC}"
fi

# Check frontend status
echo -e "\n${YELLOW}Checking frontend status...${NC}"
if pgrep -f "ng serve" > /dev/null; then
    echo -e "${GREEN}Angular process is running${NC}"
    
    # Check if port 4200 is in use
    if command -v lsof &> /dev/null; then
        if lsof -i :4200 | grep -q LISTEN; then
            echo -e "${GREEN}Frontend is listening on port 4200${NC}"
        else
            echo -e "${RED}No process is listening on port 4200${NC}"
        fi
    else
        echo -e "${YELLOW}Cannot check port status (lsof not found)${NC}"
    fi
    
    # Test frontend connection
    echo -e "${YELLOW}Testing frontend connection...${NC}"
    if curl -s http://localhost:4200 > /dev/null; then
        echo -e "${GREEN}Frontend connection successful${NC}"
    else
        echo -e "${RED}Frontend connection failed${NC}"
    fi
else
    echo -e "${RED}Angular process is not running${NC}"
fi

# Check environment files
echo -e "\n${YELLOW}Checking environment files...${NC}"
if [ -f "backend/.env" ]; then
    echo -e "${GREEN}Backend .env file exists${NC}"
    echo -e "${YELLOW}Backend .env contents:${NC}"
    cat backend/.env | grep -v PASSWORD
else
    echo -e "${RED}Backend .env file does not exist${NC}"
fi

if [ -f "frontend/.env" ]; then
    echo -e "${GREEN}Frontend .env file exists${NC}"
    echo -e "${YELLOW}Frontend .env contents:${NC}"
    cat frontend/.env
else
    echo -e "${RED}Frontend .env file does not exist${NC}"
fi

# Check log files
echo -e "\n${YELLOW}Checking log files...${NC}"
if [ -f "backend.log" ]; then
    echo -e "${GREEN}Backend log file exists${NC}"
    echo -e "${YELLOW}Last 10 lines of backend.log:${NC}"
    tail -n 10 backend.log
else
    echo -e "${RED}Backend log file does not exist${NC}"
fi

if [ -f "frontend.log" ]; then
    echo -e "${GREEN}Frontend log file exists${NC}"
    echo -e "${YELLOW}Last 10 lines of frontend.log:${NC}"
    tail -n 10 frontend.log
else
    echo -e "${RED}Frontend log file does not exist${NC}"
fi

echo -e "\n${BLUE}=== Diagnostic Complete ===${NC}"
