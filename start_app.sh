#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to display error and exit
error_exit() {
    echo -e "${RED}ERROR: $1${NC}" >&2
    exit 1
}

# Check for required dependencies
echo -e "${BLUE}Checking dependencies...${NC}"

# Check for Node.js
if ! command_exists node; then
    error_exit "Node.js is not installed. Please install Node.js before running this script."
fi

# Check for npm
if ! command_exists npm; then
    error_exit "npm is not installed. Please install npm before running this script."
fi

# Check for Python
if ! command_exists python3; then
    error_exit "Python 3 is not installed. Please install Python 3 before running this script."
fi

# Check for Docker
if ! command_exists docker; then
    error_exit "Docker is not installed. Please install Docker before running this script."
fi

# Check if Docker is running
if ! docker info &>/dev/null; then
    error_exit "Docker is not running. Please start Docker and try again."
fi

# Check if MySQL container is running
echo -e "${BLUE}Checking MySQL container...${NC}"
if ! docker ps | grep -q "local-mysql"; then
    echo -e "${YELLOW}MySQL container is not running.${NC}"

    # Check if container exists but is stopped
    if docker ps -a | grep -q "local-mysql"; then
        echo -e "${YELLOW}Starting existing MySQL container...${NC}"
        docker start local-mysql
        sleep 5
    else
        echo -e "${YELLOW}Creating new MySQL container...${NC}"
        docker run --name local-mysql \
            -e MYSQL_ROOT_PASSWORD=password \
            -e MYSQL_DATABASE=mydb \
            -p 3306:3306 \
            -d mysql:8.0

        # Wait for MySQL to start
        echo -e "${YELLOW}Waiting for MySQL to start...${NC}"
        sleep 20
    fi

    # Verify MySQL is running
    if ! docker ps | grep -q "local-mysql"; then
        error_exit "Failed to start MySQL container."
    fi
else
    echo -e "${GREEN}MySQL container is running.${NC}"
fi

# Function to start the backend
start_backend() {
    echo -e "${BLUE}Starting backend...${NC}"

    cd backend || error_exit "Backend directory not found"

    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        echo -e "${YELLOW}Creating Python virtual environment...${NC}"
        python3 -m venv venv
    fi

    # Activate virtual environment
    echo -e "${YELLOW}Activating virtual environment...${NC}"
    source venv/bin/activate

    # Install dependencies if needed
    if ! pip list | grep -q "Flask"; then
        echo -e "${YELLOW}Installing backend dependencies...${NC}"
        pip install -r requirements.txt
    fi

    # Make sure python-dotenv is installed
    pip install python-dotenv

    # Set up environment file
    if [ -f ".env.local" ]; then
        echo -e "${YELLOW}Setting up environment file...${NC}"
        cp .env.local .env
    fi

    # Set Flask environment variables
    export FLASK_APP=app/app.py
    export FLASK_ENV=development
    export FLASK_DEBUG=1

    # Kill any existing Flask processes
    pkill -f "flask run" || true

    # Start Flask in the background with port 5001 (to avoid conflict with AirPlay on macOS)
    echo -e "${GREEN}Starting Flask server...${NC}"
    flask run --host=0.0.0.0 --port=5001 > ../backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../backend.pid

    # Return to the root directory
    cd ..

    echo -e "${GREEN}Backend server started on http://localhost:5001${NC}"

    # Wait for backend to be ready
    echo -e "${YELLOW}Waiting for backend to be ready...${NC}"
    for i in {1..30}; do
        if curl -s http://localhost:5001/Test > /dev/null; then
            echo -e "${GREEN}Backend is ready!${NC}"
            break
        fi

        if [ $i -eq 30 ]; then
            echo -e "${YELLOW}Backend may not be fully initialized yet. Continuing anyway...${NC}"
        fi

        echo -n "."
        sleep 1
    done
    echo
}

# Function to start the frontend
start_frontend() {
    echo -e "${BLUE}Starting frontend...${NC}"

    cd frontend || error_exit "Frontend directory not found"

    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}Installing frontend dependencies...${NC}"
        npm install
    fi

    # Create or update .env file with local API URL
    echo -e "${YELLOW}Setting up frontend environment...${NC}"
    echo "NG_APP_BASE_URL=http://localhost:5001/Test" > .env

    # Kill any existing Angular processes
    pkill -f "ng serve" || true

    # Start Angular in the background
    echo -e "${GREEN}Starting Angular server...${NC}"
    npx ng serve --host 0.0.0.0 --disable-host-check > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../frontend.pid

    # Return to the root directory
    cd ..

    echo -e "${GREEN}Frontend server started on http://localhost:4200${NC}"

    # Wait for frontend to be ready
    echo -e "${YELLOW}Waiting for frontend to be ready...${NC}"
    for i in {1..60}; do
        if curl -s http://localhost:4200 > /dev/null; then
            echo -e "${GREEN}Frontend is ready!${NC}"
            break
        fi

        if [ $i -eq 60 ]; then
            echo -e "${YELLOW}Frontend may not be fully initialized yet. Continuing anyway...${NC}"
        fi

        echo -n "."
        sleep 1
    done
    echo
}

# Function to display combined logs
display_logs() {
    echo -e "${BLUE}Displaying combined logs (press Ctrl+C to stop)...${NC}"
    echo -e "${YELLOW}=== Combined Logs (Backend + Frontend) ===${NC}"

    # Use tail to follow both log files
    tail -f backend.log frontend.log
}

# Function to clean up on exit
cleanup() {
    echo -e "\n${BLUE}Shutting down servers...${NC}"

    # Kill backend process if it exists
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if ps -p $BACKEND_PID > /dev/null; then
            kill $BACKEND_PID
            echo -e "${GREEN}Backend server stopped${NC}"
        fi
        rm backend.pid
    fi

    # Kill frontend process if it exists
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if ps -p $FRONTEND_PID > /dev/null; then
            kill $FRONTEND_PID
            echo -e "${GREEN}Frontend server stopped${NC}"
        fi
        rm frontend.pid
    fi

    echo -e "${GREEN}Cleanup complete${NC}"
    exit 0
}

# Set up trap to catch Ctrl+C and other termination signals
trap cleanup SIGINT SIGTERM

# Main execution
echo -e "${BLUE}Starting RSPi Project...${NC}"

# Start backend
start_backend

# Start frontend
start_frontend

# Open browser
if command_exists open; then
    echo -e "${BLUE}Opening application in browser...${NC}"
    open http://localhost:4200
elif command_exists xdg-open; then
    echo -e "${BLUE}Opening application in browser...${NC}"
    xdg-open http://localhost:4200
else
    echo -e "${YELLOW}Please open http://localhost:4200 in your browser.${NC}"
fi

# Display combined logs
display_logs
